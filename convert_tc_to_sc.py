import os
import json
import re
import sys

try:
    import opencc
except ImportError:
    print("Installing OpenCC-Python...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "opencc-python-reimplemented"])
    import opencc

# Initialize the converter
converter = opencc.OpenCC('t2s')  # Traditional to Simplified

def convert_text(text):
    """Convert Traditional Chinese text to Simplified Chinese."""
    return converter.convert(text)

def process_json_file(file_path):
    """Process a JSON file, converting all Traditional Chinese to Simplified Chinese."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Convert the entire content
        converted_content = convert_text(content)
        
        # Write back to the file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(converted_content)
        
        print(f"Converted: {file_path}")
    except Exception as e:
        print(f"Error processing {file_path}: {e}")

def process_python_file(file_path):
    """Process a Python file, converting all Traditional Chinese to Simplified Chinese."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Convert the entire content
        converted_content = convert_text(content)
        
        # Write back to the file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(converted_content)
        
        print(f"Converted: {file_path}")
    except Exception as e:
        print(f"Error processing {file_path}: {e}")

def process_markdown_file(file_path):
    """Process a Markdown file, converting all Traditional Chinese to Simplified Chinese."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Convert the entire content
        converted_content = convert_text(content)
        
        # Write back to the file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(converted_content)
        
        print(f"Converted: {file_path}")
    except Exception as e:
        print(f"Error processing {file_path}: {e}")

def process_directory(directory):
    """Process all files in a directory recursively."""
    for root, dirs, files in os.walk(directory):
        for file in files:
            file_path = os.path.join(root, file)
            
            # Skip the conversion script itself
            if file == 'convert_tc_to_sc.py':
                continue
                
            # Process based on file extension
            if file.endswith('.json'):
                process_json_file(file_path)
            elif file.endswith('.py'):
                process_python_file(file_path)
            elif file.endswith('.md'):
                process_markdown_file(file_path)

def main():
    # Get the current directory
    current_dir = os.getcwd()
    print(f"Starting conversion in: {current_dir}")
    
    # Process specific files first
    specific_files = [
        os.path.join(current_dir, 'scripts', 'character_select.py'),
        os.path.join(current_dir, 'zh_TW.json'),
        os.path.join(current_dir, 'README.md'),
        os.path.join(current_dir, 'custom_settings.json'),
        os.path.join(current_dir, 'settings.json')
    ]
    
    for file_path in specific_files:
        if os.path.exists(file_path):
            if file_path.endswith('.json'):
                process_json_file(file_path)
            elif file_path.endswith('.py'):
                process_python_file(file_path)
            elif file_path.endswith('.md'):
                process_markdown_file(file_path)
    
    # Process the entire directory to catch any other files
    process_directory(current_dir)
    
    print("Conversion completed!")

if __name__ == "__main__":
    main()
