[{"prompt": "vocaloid, hatsune miku, vocaloid", "chinese_prompt": "初音未来 (Vocaloid)", "image_path": "assets/output_1_vocaloid__hatsune_miku__vocaloid_0.webp"}, {"prompt": "touhou, haku<PERSON>i reimu, touhou", "chinese_prompt": "博丽灵梦 (东方)", "image_path": "assets/output_1_touhou__hakurei_reimu__touhou_1.webp"}, {"prompt": "touhou, kiri<PERSON> marisa, touhou", "chinese_prompt": "雾雨魔理沙 (东方)", "image_path": "assets/output_1_touhou__kiri<PERSON>_marisa__touhou_2.webp"}, {"prompt": "touhou, remilia scarlet, touhou", "chinese_prompt": "蕾米莉亚·斯卡雷特 (东方)", "image_path": "assets/output_1_touhou__remilia_scarlet__touhou_3.webp"}, {"prompt": "touhou, flandre scarlet, touhou", "chinese_prompt": "芙兰朵露·斯卡蕾特 (东方)", "image_path": "assets/output_1_touhou__flandre_scarlet__touhou_4.webp"}, {"prompt": "touhou, i<PERSON><PERSON><PERSON> sakuya, touhou", "chinese_prompt": "十六夜咲夜 (东方)", "image_path": "assets/output_1_touhou__izayo<PERSON>_sakuya__touhou_5.webp"}, {"prompt": "kantai_collection, admiral (kancolle), kantai collection", "chinese_prompt": "提督 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__admiral__ka<PERSON><PERSON>___kantai_collection_6.webp"}, {"prompt": "fate_(series), artoria pendragon (fate), fate (series)", "chinese_prompt": "阿尔托莉亚·潘德拉贡 (Fate)", "image_path": "assets/output_1_fate__series___artoria_pendragon__fate___fate__series__7.webp"}, {"prompt": "touhou, kochiya sanae, touhou", "chinese_prompt": "东风谷早苗 (东方)", "image_path": "assets/output_1_touhou__kochiya_sanae__touhou_8.webp"}, {"prompt": "touhou, konpaku youmu, touhou", "chinese_prompt": "魂魄妖梦 (东方)", "image_path": "assets/output_1_touhou__konpaku_youmu__touhou_9.webp"}, {"prompt": "touh<PERSON>, alice margatroid, touhou", "chinese_prompt": "爱丽丝·玛格特罗伊德 (东方)", "image_path": "assets/output_1_touhou__alice_margatroid__touhou_10.webp"}, {"prompt": "touh<PERSON>, k<PERSON><PERSON> koishi, touhou", "chinese_prompt": "古明地恋 (东方)", "image_path": "assets/output_1_touhou__kome<PERSON>_koishi__touhou_11.webp"}, {"prompt": "touhou, cirno, touhou", "chinese_prompt": "琪露诺 (东方)", "image_path": "assets/output_1_touhou__cirno__touhou_12.webp"}, {"prompt": "touhou, patchou<PERSON> knowledge, touhou", "chinese_prompt": "帕秋莉·诺雷奇 (东方)", "image_path": "assets/output_1_touhou__patchouli_knowledge__touhou_13.webp"}, {"prompt": "touhou, yakumo yukari, touhou", "chinese_prompt": "八云紫 (东方)", "image_path": "assets/output_1_touh<PERSON>__yaku<PERSON>_yukari__touhou_14.webp"}, {"prompt": "touhou, <PERSON><PERSON><PERSON> aya, touhou", "chinese_prompt": "射命丸文 (东方)", "image_path": "assets/output_1_touhou__shame<PERSON><PERSON>_aya__touhou_15.webp"}, {"prompt": "touh<PERSON>, reisen u<PERSON><PERSON> inaba, touhou", "chinese_prompt": "铃仙・优昙华院 (东方)", "image_path": "assets/output_1_touhou__reisen_u<PERSON><PERSON>_inaba__touhou_16.webp"}, {"prompt": "touhou, fu<PERSON><PERSON> no mokou, touhou", "chinese_prompt": "藤原妹红 (东方)", "image_path": "assets/output_1_touhou__fuji<PERSON>_no_mokou__touhou_17.webp"}, {"prompt": "mahou_shoujo_madoka_magica, a<PERSON><PERSON>, mahou shoujo madoka magica", "chinese_prompt": "暁美焰 (魔法少女小圆)", "image_path": "assets/output_1_mahou_shoujo_madoka_magica__a<PERSON><PERSON>_ho<PERSON>__mahou_shoujo_madoka_magica_18.webp"}, {"prompt": "touh<PERSON>, k<PERSON><PERSON> satori, touhou", "chinese_prompt": "古明地觉 (东方)", "image_path": "assets/output_1_touhou__kome<PERSON>_satori__touhou_19.webp"}, {"prompt": "mahou_shoujo_madoka_magica, kaname madoka, mahou shoujo madoka magica", "chinese_prompt": "鹿目圆 (魔法少女小圆)", "image_path": "assets/output_1_mahou_shoujo_madoka_magica__kaname_madoka__mahou_shoujo_madoka_magica_20.webp"}, {"prompt": "touhou, hong meiling, touhou", "chinese_prompt": "红美玲 (东方)", "image_path": "assets/output_1_touhou__hong_meiling__touhou_21.webp"}, {"prompt": "touh<PERSON>, sa<PERSON><PERSON><PERSON><PERSON> yuyuko, touhou", "chinese_prompt": "西行寺幽幽子 (东方)", "image_path": "assets/output_1_touh<PERSON>__saigyou<PERSON>_yuyuko__touhou_22.webp"}, {"prompt": "touh<PERSON>, in<PERSON><PERSON><PERSON> momiji, touhou", "chinese_prompt": "犬走椛 (东方)", "image_path": "assets/output_1_touh<PERSON>__in<PERSON><PERSON><PERSON>_momiji__touhou_23.webp"}, {"prompt": "kantai_collection, kaga (kancolle), kantai collection", "chinese_prompt": "加贺 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__kaga__kancolle___kantai_collection_24.webp"}, {"prompt": "vocaloid, kagamine rin, vocaloid", "chinese_prompt": "镜音铃 (Vocaloid)", "image_path": "assets/output_1_vocaloid__kagamine_rin__vocaloid_25.webp"}, {"prompt": "touhou, yaku<PERSON> ran, touhou", "chinese_prompt": "八云蓝 (东方)", "image_path": "assets/output_1_touhou__yaku<PERSON>_ran__touhou_26.webp"}, {"prompt": "touhou, kaen<PERSON><PERSON> rin, touhou", "chinese_prompt": "火焰猫燐 (东方)", "image_path": "assets/output_1_touhou__kaen<PERSON><PERSON>_rin__touhou_27.webp"}, {"prompt": "touh<PERSON>, kon<PERSON><PERSON> youmu (ghost), touhou", "chinese_prompt": "魂魄妖梦(鬼魂) (东方)", "image_path": "assets/output_1_touhou__konpa<PERSON>_youmu__ghost___touhou_28.webp"}, {"prompt": "touhou, rumia, touhou", "chinese_prompt": "露米娅 (东方)", "image_path": "assets/output_1_touhou__rumia__touhou_29.webp"}, {"prompt": "touhou, moriya suwa<PERSON>, touhou", "chinese_prompt": "泄矢诹访子 (东方)", "image_path": "assets/output_1_touhou__moriya_suwa<PERSON>__touhou_30.webp"}, {"prompt": "kantai_collection, shimakaze (kancolle), kantai collection", "chinese_prompt": "岛风 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__shimakaze__kancolle___kantai_collection_31.webp"}, {"prompt": "mahou_shoujo_madoka_magica, miki sayaka, mahou shoujo madoka magica", "chinese_prompt": "美树沙耶香 (魔法少女小圆)", "image_path": "assets/output_1_mahou_shoujo_madoka_magica__miki_sayaka__mahou_shoujo_madoka_magica_32.webp"}, {"prompt": "touhou, chen, touhou", "chinese_prompt": "橙 (东方)", "image_path": "assets/output_1_touhou__chen__touhou_33.webp"}, {"prompt": "genshin_impact, ganyu (genshin impact), genshin impact", "chinese_prompt": "甘雨 (原神)", "image_path": "assets/output_1_genshin_impact__ganyu__genshin_impact___genshin_impact_34.webp"}, {"prompt": "touhou, ka<PERSON>i yuuka, touhou", "chinese_prompt": "风见幽香 (东方)", "image_path": "assets/output_1_touh<PERSON>__ka<PERSON>i_yuuka__touhou_35.webp"}, {"prompt": "touh<PERSON>, re<PERSON><PERSON> utsu<PERSON>, touhou", "chinese_prompt": "灵乌路空 (东方)", "image_path": "assets/output_1_touh<PERSON>__re<PERSON><PERSON>_u<PERSON><PERSON>__touhou_36.webp"}, {"prompt": "fate_(series), saber (fate), fate (series)", "chinese_prompt": "<PERSON><PERSON> (Fate)", "image_path": "assets/output_1_fate__series___saber__fate___fate__series__37.webp"}, {"prompt": "blue_archive, sensei (blue archive), blue archive", "chinese_prompt": "水羽三森 (蔚蓝档案)", "image_path": "assets/output_1_blue_archive__sensei__blue_archive___blue_archive_38.webp"}, {"prompt": "fate_(series), mash kyrielight, fate (series)", "chinese_prompt": "玛修·基利艾拉特 (Fate)", "image_path": "assets/output_1_fate__series___mash_kyrielight__fate__series__39.webp"}, {"prompt": "kantai_collection, hibiki (kancolle), kantai collection", "chinese_prompt": "响 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__hibiki__kancolle___kantai_collection_40.webp"}, {"prompt": "touhou, tatara kogasa, touhou", "chinese_prompt": "多多良小伞 (东方)", "image_path": "assets/output_1_touhou__ta<PERSON>_kogasa__touhou_41.webp"}, {"prompt": "touh<PERSON>, ka<PERSON><PERSON> nitori, touhou", "chinese_prompt": "河城荷取 (东方)", "image_path": "assets/output_1_touh<PERSON>__ka<PERSON><PERSON>_nitori__touhou_42.webp"}, {"prompt": "kantai_collection, shigure (kancolle), kantai collection", "chinese_prompt": "时雨 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__shigure__kancolle___kantai_collection_43.webp"}, {"prompt": "touh<PERSON>, ka<PERSON><PERSON><PERSON><PERSON> keine, touhou", "chinese_prompt": "上白泽慧音 (东方)", "image_path": "assets/output_1_touh<PERSON>__ka<PERSON><PERSON><PERSON><PERSON>_keine__touhou_44.webp"}, {"prompt": "touhou, hi<PERSON><PERSON><PERSON> tenshi, touhou", "chinese_prompt": "比那名居天子 (东方)", "image_path": "assets/output_1_touhou__hi<PERSON><PERSON><PERSON>_tenshi__touhou_45.webp"}, {"prompt": "mahou_shoujo_madoka_magica, sakura kyoko, mahou shoujo madoka magica", "chinese_prompt": "佐仓杏子 (魔法少女小圆)", "image_path": "assets/output_1_mahou_shoujo_madoka_magica__sakura_kyoko__mahou_shoujo_madoka_magica_46.webp"}, {"prompt": "mahou_shoujo_madoka_magica, to<PERSON><PERSON> mami, mahou shoujo madoka magica", "chinese_prompt": "巴麻美 (魔法少女小圆)", "image_path": "assets/output_1_mahou_shoujo_madoka_magica__tomoe_mami__mahou_shoujo_madoka_magica_47.webp"}, {"prompt": "final_fantasy, warrior of light (ff14), final fantasy", "chinese_prompt": "光之战士 (ff14) (最终幻想)", "image_path": "assets/output_1_final_fantasy__warrior_of_light__ff14___final_fantasy_48.webp"}, {"prompt": "touh<PERSON>, <PERSON><PERSON><PERSON> kaguya, touhou", "chinese_prompt": "蓬莱山辉夜 (东方)", "image_path": "assets/output_1_touh<PERSON>__houraisan_kaguya__touhou_49.webp"}, {"prompt": "touh<PERSON>, mystia lorelei, touhou", "chinese_prompt": "米斯蒂娅·萝蕾拉 (东方)", "image_path": "assets/output_1_touh<PERSON>__mystia_lorelei__touhou_50.webp"}, {"prompt": "genshin_impact, raiden shogun, genshin impact", "chinese_prompt": "雷电将军 (原神)", "image_path": "assets/output_1_genshin_impact__raiden_shogun__genshin_impact_51.webp"}, {"prompt": "genshin_impact, lumine (genshin impact), genshin impact", "chinese_prompt": "萤 (原神)", "image_path": "assets/output_1_genshin_impact__lumine__genshin_impact___genshin_impact_52.webp"}, {"prompt": "touhou, koakuma, touhou", "chinese_prompt": "小恶魔 (东方)", "image_path": "assets/output_1_touhou__koaku<PERSON>__touhou_53.webp"}, {"prompt": "the_legend_of_zelda, link, the legend of zelda", "chinese_prompt": "林克 (塞尔达传说)", "image_path": "assets/output_1_the_legend_of_zelda__link__the_legend_of_zelda_54.webp"}, {"prompt": "neon_genesis_evangelion, sour<PERSON><PERSON> asuka langley, neon genesis evangelion", "chinese_prompt": "惣流·明日香·兰格雷 (新世纪福音战士)", "image_path": "assets/output_1_neon_genesis_evangelion__souryuu_asuka_langley__neon_genesis_evangelion_55.webp"}, {"prompt": "touhou, inaba tewi, touhou", "chinese_prompt": "因幡帝 (东方)", "image_path": "assets/output_1_touhou__inaba_tewi__touhou_56.webp"}, {"prompt": "touhou, ibuki suika, touhou", "chinese_prompt": "伊吹萃香 (东方)", "image_path": "assets/output_1_touh<PERSON>__i<PERSON><PERSON>_suika__touhou_57.webp"}, {"prompt": "hololive, houshou marine, hololive", "chinese_prompt": "宝钟玛琳 (Hololive)", "image_path": "assets/output_1_hololive__housh<PERSON>_marine__hololive_58.webp"}, {"prompt": "kantai_collection, kongou (kancolle), kantai collection", "chinese_prompt": "金刚 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__kongou__kancolle___kantai_collection_59.webp"}, {"prompt": "touhou, nazrin, touhou", "chinese_prompt": "纳兹琳 (东方)", "image_path": "assets/output_1_touh<PERSON>__nazrin__touhou_60.webp"}, {"prompt": "vocaloid, kagamine len, vocaloid", "chinese_prompt": "镜音连 (Vocaloid)", "image_path": "assets/output_1_vocaloid__kagamine_len__vocaloid_61.webp"}, {"prompt": "hololive, gawr gura, hololive", "chinese_prompt": "噶呜·古拉 (鲨鲨) (Hololive)", "image_path": "assets/output_1_hololive__gawr_gura__hololive_62.webp"}, {"prompt": "kantai_collection, inazuma (kancolle), kantai collection", "chinese_prompt": "电 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__inazuma__kancolle___kantai_collection_63.webp"}, {"prompt": "touh<PERSON>, hi<PERSON><PERSON><PERSON>, touhou", "chinese_prompt": "圣白莲 (东方)", "image_path": "assets/output_1_touh<PERSON>__hi<PERSON><PERSON>_by<PERSON><PERSON>__touhou_64.webp"}, {"prompt": "fate_(series), fu<PERSON><PERSON> ritsuka (male), fate (series)", "chinese_prompt": "藤丸立香(男性) (Fate)", "image_path": "assets/output_1_fate__series___fu<PERSON><PERSON>_ritsuka__male___fate__series__65.webp"}, {"prompt": "fate_(series), tamamo (fate), fate (series)", "chinese_prompt": "玉藻前 (Fate)", "image_path": "assets/output_1_fate__series___tamamo__fate___fate__series__66.webp"}, {"prompt": "kantai_collection, akagi (kancolle), kantai collection", "chinese_prompt": "赤城 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__akagi__kancolle___kantai_collection_67.webp"}, {"prompt": "touhou, houjuu nue, touhou", "chinese_prompt": "封兽鵺 (东方)", "image_path": "assets/output_1_touhou__houjuu_nue__touhou_68.webp"}, {"prompt": "fate_(series), fu<PERSON><PERSON> ritsuka (female), fate (series)", "chinese_prompt": "藤丸立香(女性) (Fate)", "image_path": "assets/output_1_fate__series___fu<PERSON><PERSON>_ritsuka__female___fate__series__69.webp"}, {"prompt": "splatoon_(series), inkling player character, splatoon (series)", "chinese_prompt": "玩家角色 (斯普拉遁)", "image_path": "assets/output_1_splatoon__series___inkling_player_character__splatoon__series__70.webp"}, {"prompt": "pokemon, pikachu, pokemon", "chinese_prompt": "皮卡丘 (宝可梦)", "image_path": "assets/output_1_pokemon__pikachu__pokemon_71.webp"}, {"prompt": "fate_(series), jeanne d'arc alter (fate), fate (series)", "chinese_prompt": "黑贞德 (Fate)", "image_path": "assets/output_1_fate__series___jeanne_d_arc_alter__fate___fate__series__72.webp"}, {"prompt": "vocaloid, megurine luka, vocaloid", "chinese_prompt": "巡音流歌 (Vocaloid)", "image_path": "assets/output_1_vocaloid__megurine_luka__vocaloid_73.webp"}, {"prompt": "touhou, yasaka kanako, touhou", "chinese_prompt": "八坂神奈子 (东方)", "image_path": "assets/output_1_touhou__yasa<PERSON>_kanako__touhou_74.webp"}, {"prompt": "touhou, yagokoro eirin, touhou", "chinese_prompt": "八意永琳 (东方)", "image_path": "assets/output_1_touhou__yago<PERSON><PERSON>_eirin__touhou_75.webp"}, {"prompt": "kantai_collection, tenryuu (kancolle), kantai collection", "chinese_prompt": "天龙 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__tenryuu__kancolle___kantai_collection_76.webp"}, {"prompt": "kantai_collection, yuudachi (kancolle), kantai collection", "chinese_prompt": "夕立 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__yu<PERSON><PERSON>__kancolle___kantai_collection_77.webp"}, {"prompt": "bocchi_the_rock!, gotoh hitori, bocchi the rock!", "chinese_prompt": "后藤一里 (孤独摇滚)", "image_path": "assets/output_1_bocchi_the_rock___gotoh_hitori__bocchi_the_rock__78.webp"}, {"prompt": "genshin_impact, hu tao (genshin impact), genshin impact", "chinese_prompt": "胡桃 (原神)", "image_path": "assets/output_1_genshin_impact__hu_tao__genshin_impact___genshin_impact_79.webp"}, {"prompt": "touh<PERSON>, mi<PERSON><PERSON> parsee, touhou", "chinese_prompt": "水桥帕露西 (东方)", "image_path": "assets/output_1_touh<PERSON>__mi<PERSON><PERSON>_parsee__touhou_80.webp"}, {"prompt": "the_legend_of_zelda, princess zelda, the legend of zelda", "chinese_prompt": "赛尔达公主 (塞尔达传说)", "image_path": "assets/output_1_the_legend_of_zelda__princess_zelda__the_legend_of_zelda_81.webp"}, {"prompt": "final_fantasy, tifa lockhart, final fantasy", "chinese_prompt": "蒂法·洛克哈特 (ff7) (最终幻想)", "image_path": "assets/output_1_final_fantasy__tifa_lockhart__final_fantasy_82.webp"}, {"prompt": "kantai_collection, fubuki (kancolle), kantai collection", "chinese_prompt": "吹雪 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__fubuki__kancolle___kantai_collection_83.webp"}, {"prompt": "blue_archive, yuuka (blue archive), blue archive", "chinese_prompt": "早濑优香 (蔚蓝档案)", "image_path": "assets/output_1_blue_archive__yuuka__blue_archive___blue_archive_84.webp"}, {"prompt": "fate_(series), abigail williams (fate), fate (series)", "chinese_prompt": "艾比盖儿·威廉斯 (Fate)", "image_path": "assets/output_1_fate__series___abigail_williams__fate___fate__series__85.webp"}, {"prompt": "blue_archive, asuna (blue archive), blue archive", "chinese_prompt": "一之濑明日奈 (蔚蓝档案)", "image_path": "assets/output_1_blue_archive__asuna__blue_archive___blue_archive_86.webp"}, {"prompt": "kantai_collection, i<PERSON><PERSON><PERSON> (kancolle), kantai collection", "chinese_prompt": "雷 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__i<PERSON><PERSON><PERSON>__kancolle___kantai_collection_87.webp"}, {"prompt": "hololive, shir<PERSON><PERSON> fubuki, hololive", "chinese_prompt": "白上吹雪 (Hololive)", "image_path": "assets/output_1_hololive__shir<PERSON><PERSON>_fubuki__hololive_88.webp"}, {"prompt": "kantai_collection, akatsuki (kancolle), kantai collection", "chinese_prompt": "晓 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__akatsuki__kancolle___kantai_collection_89.webp"}, {"prompt": "kantai_collection, zuikaku (kancolle), kantai collection", "chinese_prompt": "瑞鹤 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__zuikaku__kancolle___kantai_collection_90.webp"}, {"prompt": "k-on!, akiyama mio, k-on!", "chinese_prompt": "秋山澪 (K-ON! 轻音部)", "image_path": "assets/output_1_k-on___aki<PERSON>_mio__k-on__91.webp"}, {"prompt": "touhou, kagiyama hina, touhou", "chinese_prompt": "键山雏 (东方)", "image_path": "assets/output_1_touh<PERSON>__kagiyama_hina__touhou_92.webp"}, {"prompt": "splatoon_(series), inkling girl, splatoon (series)", "chinese_prompt": "女孩角色, (斯普拉遁)", "image_path": "assets/output_1_splatoon__series___inkling_girl__splatoon__series__93.webp"}, {"prompt": "touh<PERSON>, toy<PERSON><PERSON><PERSON><PERSON> no miko, touhou", "chinese_prompt": "丰聪耳神子 (东方)", "image_path": "assets/output_1_touh<PERSON>__toyosatom<PERSON>i_no_miko__touhou_94.webp"}, {"prompt": "<PERSON><PERSON><PERSON>_haru<PERSON>_no_yu<PERSON><PERSON><PERSON>, su<PERSON><PERSON> haruhi, suzu<PERSON> haruhi no yuuutsu", "chinese_prompt": "凉宫春日 (凉宫春日的忧郁)", "image_path": "assets/output_1_su<PERSON><PERSON>_haru<PERSON>_no_yuuutsu__suzu<PERSON>_haru<PERSON>__suzu<PERSON>_haruhi_no_yuuutsu_95.webp"}, {"prompt": "kantai_collection, hamakaze (kancolle), kantai collection", "chinese_prompt": "滨风 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__hamakaze__kancolle___kantai_collection_96.webp"}, {"prompt": "fate_(series), nero claudius (fate), fate (series)", "chinese_prompt": "尼禄 (Fate)", "image_path": "assets/output_1_fate__series___nero_claudius__fate___fate__series__97.webp"}, {"prompt": "blue_archive, doodle sensei (blue archive), blue archive", "chinese_prompt": "水羽三森 涂鸦 (蔚蓝档案)", "image_path": "assets/output_1_blue_archive__doodle_sensei__blue_archive___blue_archive_98.webp"}, {"prompt": "kantai_collection, haruna (kancolle), kantai collection", "chinese_prompt": "榛名 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__haruna__kancolle___kantai_collection_99.webp"}, {"prompt": "nier_(series), 2b (nier:automata), nier (series)", "chinese_prompt": "2B (尼尔:自动人形)", "image_path": "assets/output_1_nier__series___2b__nier_automata___nier__series__100.webp"}, {"prompt": "pokemon, gardevoir, pokemon", "chinese_prompt": "沙奈朵 (宝可梦)", "image_path": "assets/output_1_pokemon__gardevoir__pokemon_101.webp"}, {"prompt": "hololive, ho<PERSON><PERSON> suisei, hololive", "chinese_prompt": "星街彗星 (Hololive)", "image_path": "assets/output_1_hololive__ho<PERSON><PERSON>_suisei__hololive_102.webp"}, {"prompt": "kantai_collection, nagato (kancolle), kantai collection", "chinese_prompt": "长门 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__nagato__kancolle___kantai_collection_103.webp"}, {"prompt": "touh<PERSON>, mori<PERSON><PERSON> r<PERSON>, touhou", "chinese_prompt": "森近霖之助 (东方)", "image_path": "assets/output_1_touh<PERSON>__mori<PERSON><PERSON>_rinn<PERSON><PERSON>__touhou_104.webp"}, {"prompt": "genshin_impact, yae miko, genshin impact", "chinese_prompt": "八重神子 (原神)", "image_path": "assets/output_1_genshin_impact__yae_miko__genshin_impact_105.webp"}, {"prompt": "danga<PERSON><PERSON><PERSON>_(series), na<PERSON>i chia<PERSON>, danga<PERSON><PERSON><PERSON> (series)", "chinese_prompt": "七海千秋 (弹丸论破)", "image_path": "assets/output_1_danganronpa__series___nanami_chiaki__danganronpa__series__106.webp"}, {"prompt": "touh<PERSON>, hoshi<PERSON>a yuugi, touhou", "chinese_prompt": "星熊勇仪 (东方)", "image_path": "assets/output_1_touhou__hoshiguma_yuugi__touhou_107.webp"}, {"prompt": "blue_archive, shiroko (blue archive), blue archive", "chinese_prompt": "砂狼白子 (蔚蓝档案)", "image_path": "assets/output_1_blue_archive__shiroko__blue_archive___blue_archive_108.webp"}, {"prompt": "kantai_collection, kashima (kancolle), kantai collection", "chinese_prompt": "鹿岛 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__ka<PERSON>__kancolle___kantai_collection_109.webp"}, {"prompt": "lyrical_nanoha, fate testarossa, lyrical nanoha", "chinese_prompt": "菲特·泰斯塔罗沙 (魔法少女奈叶)", "image_path": "assets/output_1_lyrical_nanoha__fate_testarossa__lyrical_nanoha_110.webp"}, {"prompt": "kantai_collection, shigure kai ni (kancolle), kantai collection", "chinese_prompt": "时雨改二 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__shigure_kai_ni__kancolle___kantai_collection_111.webp"}, {"prompt": "girls_und_panzer, ni<PERSON><PERSON><PERSON> miho, girls und panzer", "chinese_prompt": "西住美穗 (少女与战车)", "image_path": "assets/output_1_girls_und_panzer__ni<PERSON><PERSON><PERSON>_miho__girls_und_panzer_112.webp"}, {"prompt": "genshin_impact, aether (genshin impact), genshin impact", "chinese_prompt": "空 (原神)", "image_path": "assets/output_1_genshin_impact__aether__genshin_impact___genshin_impact_113.webp"}, {"prompt": "touhou, daiyousei, touhou", "chinese_prompt": "大妖精 (东方)", "image_path": "assets/output_1_touhou__daiyousei__touhou_114.webp"}, {"prompt": "touhou, mononobe no futo, touhou", "chinese_prompt": "物部布都 (东方)", "image_path": "assets/output_1_touhou__mononobe_no_futo__touhou_115.webp"}, {"prompt": "fate_(series), to<PERSON><PERSON> rin, fate (series)", "chinese_prompt": "远坂凛 (Fate)", "image_path": "assets/output_1_fate__series___tohsaka_rin__fate__series__116.webp"}, {"prompt": "touhou, usami renko, touhou", "chinese_prompt": "宇佐见莲子 (东方)", "image_path": "assets/output_1_touh<PERSON>__usami_renko__touhou_117.webp"}, {"prompt": "touhou, wriggle nightbug, touhou", "chinese_prompt": "莉格露・奈特巴格 (东方)", "image_path": "assets/output_1_touhou__wriggle_nightbug__touhou_118.webp"}, {"prompt": "touhou, shanghai doll, touhou", "chinese_prompt": "上海人偶 (东方)", "image_path": "assets/output_1_touhou__shanghai_doll__touhou_119.webp"}, {"prompt": "touhou, shiki eiki, touhou", "chinese_prompt": "四季映姬 (东方)", "image_path": "assets/output_1_touhou__shiki_eiki__touhou_120.webp"}, {"prompt": "toaru_majutsu_no_index, misa<PERSON> mikoto, toaru majutsu no index", "chinese_prompt": "御坂美琴 (魔法禁书目录)", "image_path": "assets/output_1_toaru_majutsu_no_index__misaka_mikoto__toaru_majutsu_no_index_121.webp"}, {"prompt": "re:zero_kara_hajimeru_isekai_seikatsu, rem (re:zero), re:zero kara hajimeru isekai seikatsu", "chinese_prompt": "雷姆 (Re:从零开始的异世界生活)", "image_path": "assets/output_1_re_zero_kara_hajimeru_isekai_seikatsu__rem__re_zero___re_zero_kara_hajimeru_isekai_seikatsu_122.webp"}, {"prompt": "final_fantasy, cloud strife, final fantasy", "chinese_prompt": "克劳德·史特莱夫 (ff7) (最终幻想)", "image_path": "assets/output_1_final_fantasy__cloud_strife__final_fantasy_123.webp"}, {"prompt": "blue_archive, hina (blue archive), blue archive", "chinese_prompt": "空崎阳奈 (蔚蓝档案)", "image_path": "assets/output_1_blue_archive__hina__blue_archive___blue_archive_124.webp"}, {"prompt": "genshin_impact, scaramouche (genshin impact), genshin impact", "chinese_prompt": "散兵 (原神)", "image_path": "assets/output_1_genshin_impact__scaramouche__genshin_impact___genshin_impact_125.webp"}, {"prompt": "genshin_impact, zhongli (genshin impact), genshin impact", "chinese_prompt": "钟离(原神)", "image_path": "assets/output_1_genshin_impact__zhongli__genshin_impact___genshin_impact_126.webp"}, {"prompt": "su<PERSON><PERSON>_haru<PERSON>_no_yuu<PERSON><PERSON>, nagato yuki, suzu<PERSON> haruhi no yuuutsu", "chinese_prompt": "长门有希 (凉宫春日的忧郁)", "image_path": "assets/output_1_su<PERSON><PERSON>_haruhi_no_yuuutsu__nagato_yuki__suzu<PERSON>_haruhi_no_yuuutsu_127.webp"}, {"prompt": "k-on!, nakano azusa, k-on!", "chinese_prompt": "中野梓 (K-ON! 轻音部)", "image_path": "assets/output_1_k-on___nakano_azusa__k-on__128.webp"}, {"prompt": "hololive, usada pekora, hololive", "chinese_prompt": "兔田佩克拉 (Hololive)", "image_path": "assets/output_1_hololive__usada_pekora__hololive_129.webp"}, {"prompt": "touhou, onozuka komachi, touhou", "chinese_prompt": "小野冢小町 (东方)", "image_path": "assets/output_1_touh<PERSON>__onoz<PERSON>_komachi__touhou_130.webp"}, {"prompt": "touhou, mari<PERSON> hearn, touhou", "chinese_prompt": "玛艾露贝莉·赫恩 (东方)", "image_path": "assets/output_1_touhou__maribel_hearn__touhou_131.webp"}, {"prompt": "idolmaster, producer (idolmaster), idolmaster", "chinese_prompt": "制作人 (偶像大师)", "image_path": "assets/output_1_idolmaster__producer__idolmaster___idolmaster_132.webp"}, {"prompt": "fate_(series), ill<PERSON><PERSON><PERSON> von <PERSON>, fate (series)", "chinese_prompt": "伊莉雅丝菲尔·冯·爱因兹贝伦 (Fate)", "image_path": "assets/output_1_fate__series___illyasvie<PERSON>_<PERSON>_<PERSON><PERSON><PERSON><PERSON>n__fate__series__133.webp"}, {"prompt": "azur_lane, manjuu (azur lane), azur lane", "chinese_prompt": "蛮啾 (碧蓝航线)", "image_path": "assets/output_1_azur_lane__manjuu__azur_lane___azur_lane_134.webp"}, {"prompt": "kantai_collection, suzuya (kancolle), kantai collection", "chinese_prompt": "铃谷 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__suzuya__kancolle___kantai_collection_135.webp"}, {"prompt": "genshin_impact, furina (genshin impact), genshin impact", "chinese_prompt": "芙宁娜 (原神)", "image_path": "assets/output_1_genshin_impact__furina__genshin_impact___genshin_impact_136.webp"}, {"prompt": "neon_genesis_evangelion, ayana<PERSON> rei, neon genesis evangelion", "chinese_prompt": "绫波零 (新世纪福音战士)", "image_path": "assets/output_1_neon_genesis_evangelion__ayanami_rei__neon_genesis_evangelion_137.webp"}, {"prompt": "mahou_shoujo_madoka_magica, kyu<PERSON>, mahou shoujo madoka magica", "chinese_prompt": "丘比 (魔法少女小圆)", "image_path": "assets/output_1_mahou_shoujo_madoka_magica__kyubey__mahou_shoujo_madoka_magica_138.webp"}, {"prompt": "kantai_collection, ryuu<PERSON> (kancolle), kantai collection", "chinese_prompt": "龙骧 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__ryuu<PERSON>__kancolle___kantai_collection_139.webp"}, {"prompt": "fate_(series), scathach (fate), fate (series)", "chinese_prompt": "斯卡哈 (Fate)", "image_path": "assets/output_1_fate__series___scathach__fate___fate__series__140.webp"}, {"prompt": "touh<PERSON>, im<PERSON><PERSON><PERSON> kager<PERSON>, touhou", "chinese_prompt": "今泉影狼 (东方)", "image_path": "assets/output_1_touh<PERSON>__im<PERSON><PERSON><PERSON>_kager<PERSON>__touhou_141.webp"}, {"prompt": "k-on!, hi<PERSON><PERSON> yui, k-on!", "chinese_prompt": "平泽唯 (K-ON! 轻音部)", "image_path": "assets/output_1_k-on___hi<PERSON><PERSON>_yui__k-on__142.webp"}, {"prompt": "hololive, ninomae ina'nis, hololive", "chinese_prompt": "一伊那尔栖 (Hololive)", "image_path": "assets/output_1_hololive__ninomae_ina_nis__hololive_143.webp"}, {"prompt": "kantai_collection, yu<PERSON>chi kai ni (kancolle), kantai collection", "chinese_prompt": "夕立改二 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__yuuda<PERSON>_kai_ni__kancolle___kantai_collection_144.webp"}, {"prompt": "hololive, mori calliope, hololive", "chinese_prompt": "森美声 (Hololive)", "image_path": "assets/output_1_hololive__mori_calliope__hololive_145.webp"}, {"prompt": "honkai_(series), trailblazer (honkai: star rail), honkai (series)", "chinese_prompt": "开拓者 (崩坏: 星穹铁道) (崩坏)", "image_path": "assets/output_1_honkai__series___trailblazer__honkai__star_rail___honkai__series__146.webp"}, {"prompt": "hololive, minato aqua, hololive", "chinese_prompt": "凑阿库娅 (Hololive)", "image_path": "assets/output_1_hololive__minato_aqua__hololive_147.webp"}, {"prompt": "spy_x_family, yor briar, spy x family", "chinese_prompt": "约儿 (SPY x FAMILY 间谍家家酒)", "image_path": "assets/output_1_spy_x_family__yor_briar__spy_x_family_148.webp"}, {"prompt": "kono_subarashii_sekai_ni_shukufuku_wo!, megumin, kono subarashii sekai ni shukufuku wo!", "chinese_prompt": "惠惠 (为美好的世界献上祝福)", "image_path": "assets/output_1_kono_subarashii_sekai_ni_shukufuku_wo___megumin__kono_subarashii_sekai_ni_shukufuku_wo__149.webp"}, {"prompt": "kantai_collection, houshou (kancolle), kantai collection", "chinese_prompt": "凤翔 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__houshou__kancolle___kantai_collection_150.webp"}, {"prompt": "arknights, doctor (arknights), arknights", "chinese_prompt": "博士 (明日方舟)", "image_path": "assets/output_1_arknights__doctor__arknights___arknights_151.webp"}, {"prompt": "lyrical_nanoha, taka<PERSON><PERSON> nanoha, lyrical nanoha", "chinese_prompt": "高町奈叶 (魔法少女奈叶)", "image_path": "assets/output_1_lyrical_nanoha__takamachi_nanoha__lyrical_nanoha_152.webp"}, {"prompt": "touhou, to<PERSON><PERSON> shou, touhou", "chinese_prompt": "寅丸星 (东方)", "image_path": "assets/output_1_touhou__to<PERSON><PERSON>_shou__touhou_153.webp"}, {"prompt": "kill_la_kill, matoi ryuuko, kill la kill", "chinese_prompt": "缠流子 (斩服少女)", "image_path": "assets/output_1_kill_la_kill__matoi_ryuuko__kill_la_kill_154.webp"}, {"prompt": "fate_(series), jeanne d'arc (fate), fate (series)", "chinese_prompt": "贞德 (Fate)", "image_path": "assets/output_1_fate__series___jeanne_d_arc__fate___fate__series__155.webp"}, {"prompt": "kemono_friends, serva<PERSON> (kemono friends), kemono friends", "chinese_prompt": "薮猫 (动物朋友)", "image_path": "assets/output_1_kemono_friends__serval__kemono_friends___kemono_friends_156.webp"}, {"prompt": "sousou_no_frieren, frieren, sousou no frieren", "chinese_prompt": "芙莉莲 (葬送的芙莉莲)", "image_path": "assets/output_1_sousou_no_frieren__frieren__sousou_no_frieren_157.webp"}, {"prompt": "genshin_impact, mona (genshin impact), genshin impact", "chinese_prompt": "莫娜 (原神)", "image_path": "assets/output_1_genshin_impact__mona__genshin_impact___genshin_impact_158.webp"}, {"prompt": "jojo_no_kimyou_na_bouken, jose<PERSON> j<PERSON><PERSON>, jojo no kimyou na bouken", "chinese_prompt": "乔瑟夫·乔斯达 (二乔) (JOJO的奇妙冒险)", "image_path": "assets/output_1_jojo_no_kimyou_na_bouken__joseph_joestar__jojo_no_kimyou_na_bouken_159.webp"}, {"prompt": "umam<PERSON><PERSON>, trainer (umamusume), umamusume", "chinese_prompt": "培训师 (赛马娘)", "image_path": "assets/output_1_umamusume__trainer__umamusume___umamusume_160.webp"}, {"prompt": "blue_archive, hoshino (blue archive), blue archive", "chinese_prompt": "小鸟游星野 (蔚蓝档案)", "image_path": "assets/output_1_blue_archive__hoshino__blue_archive___blue_archive_161.webp"}, {"prompt": "blue_archive, mika (blue archive), blue archive", "chinese_prompt": "圣园弥香 (蔚蓝档案)", "image_path": "assets/output_1_blue_archive__mika__blue_archive___blue_archive_162.webp"}, {"prompt": "blue_archive, toki (blue archive), blue archive", "chinese_prompt": "飞鸟马时 (蔚蓝档案)", "image_path": "assets/output_1_blue_archive__toki__blue_archive___blue_archive_163.webp"}, {"prompt": "pokemon, dawn (pokemon), pokemon", "chinese_prompt": "小光 (宝可梦)", "image_path": "assets/output_1_pokemon__dawn__pokemon___pokemon_164.webp"}, {"prompt": "genshin_impact, keqing (genshin impact), genshin impact", "chinese_prompt": "刻晴 (原神)", "image_path": "assets/output_1_genshin_impact__keqing__genshin_impact___genshin_impact_165.webp"}, {"prompt": "touh<PERSON>, him<PERSON><PERSON><PERSON> hatate, touhou", "chinese_prompt": "姬海棠羽立 (东方)", "image_path": "assets/output_1_touh<PERSON>__him<PERSON><PERSON><PERSON>_hatate__touhou_166.webp"}, {"prompt": "hololive, nekomata <PERSON>u, hololive", "chinese_prompt": "猫又小粥 (Hololive)", "image_path": "assets/output_1_hololive__nekomata_okayu__hololive_167.webp"}, {"prompt": "love_live!, nishi<PERSON>o maki, love live!", "chinese_prompt": "西木野真姬 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_1_love_live___nishikino_maki__love_live__168.webp"}, {"prompt": "kantai_collection, amatsukaze (kancolle), kantai collection", "chinese_prompt": "天津风 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__amatsukaze__kancolle___kantai_collection_169.webp"}, {"prompt": "voiceroid, yuzuki yukari, voiceroid", "chinese_prompt": "结月缘 (Vocaloid)", "image_path": "assets/output_1_voiceroid__yuzuki_yukari__voiceroid_170.webp"}, {"prompt": "kantai_collection, murakumo (kancolle), kantai collection", "chinese_prompt": "丛云 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__muraku<PERSON>__kancolle___kantai_collection_171.webp"}, {"prompt": "hololive, nanashi mumei, hololive", "chinese_prompt": "七诗无铭 (Hololive)", "image_path": "assets/output_1_hololive__nanashi_mumei__hololive_172.webp"}, {"prompt": "fate_(series), jeanne d'arc alter (avenger) (fate), fate (series)", "chinese_prompt": "黑贞德 (复仇者) (Fate)", "image_path": "assets/output_1_fate__series___jeanne_d_arc_alter__avenger___fate___fate__series__173.webp"}, {"prompt": "k-on!, tainaka ritsu, k-on!", "chinese_prompt": "田井中律 (K-ON! 轻音部)", "image_path": "assets/output_1_k-on___tainaka_ritsu__k-on__174.webp"}, {"prompt": "pokemon, lillie (pokemon), pokemon", "chinese_prompt": "莉莉艾 (宝可梦)", "image_path": "assets/output_1_pokemon__lillie__pokemon___pokemon_175.webp"}, {"prompt": "honkai_(series), firefly (honkai: star rail), honkai (series)", "chinese_prompt": "流萤 (崩坏：星穹铁道) (崩坏)", "image_path": "assets/output_1_honkai__series___firefly__honkai__star_rail___honkai__series__176.webp"}, {"prompt": "touhou, kaku seiga, touhou", "chinese_prompt": "霍 青娥 (东方)", "image_path": "assets/output_1_touhou__kaku_seiga__touhou_177.webp"}, {"prompt": "arknights, amiya (arknights), arknights", "chinese_prompt": "阿米娅 阿米驴(明日方舟)", "image_path": "assets/output_1_arknights__amiya__arknights___arknights_178.webp"}, {"prompt": "gochuumon_wa_usagi_desu_ka?, kafuu chino, gochuumon wa usagi desu ka?", "chinese_prompt": "香风智乃 (请问您今天要来点兔子吗?)", "image_path": "assets/output_1_gochuumon_wa_usagi_desu_ka___kafuu_chino__gochuumon_wa_usagi_desu_ka__179.webp"}, {"prompt": "love_live!, sonoda umi, love live!", "chinese_prompt": "园田海未 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_1_love_live___sonoda_umi__love_live__180.webp"}, {"prompt": "fate_(series), okita souji (fate), fate (series)", "chinese_prompt": "冲田总司 (Fate)", "image_path": "assets/output_1_fate__series___okita_souji__fate___fate__series__181.webp"}, {"prompt": "chainsaw_man, makima (chainsaw man), chainsaw man", "chinese_prompt": "真纪真 (电锯人)", "image_path": "assets/output_1_chainsaw_man__makima__chainsaw_man___chainsaw_man_182.webp"}, {"prompt": "kantai_collection, ushio (kancolle), kantai collection", "chinese_prompt": "潮 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__ushio__kancolle___kantai_collection_183.webp"}, {"prompt": "xenoblade_chronicles_(series), pyra (xenoblade), xenoblade chronicles (series)", "chinese_prompt": "焰 (异度神剑)", "image_path": "assets/output_1_xenoblade_chronicles__series___pyra__xenoblade___xenoblade_chronicles__series__184.webp"}, {"prompt": "idolmaster, shi<PERSON><PERSON> rin, idolmaster", "chinese_prompt": "涩谷凛 (灰姑娘) (偶像大师)", "image_path": "assets/output_1_idolmaster__shi<PERSON>ya_rin__idolmaster_185.webp"}, {"prompt": "hololive, toko<PERSON>i towa, hololive", "chinese_prompt": "常暗永远 (Hololive)", "image_path": "assets/output_1_hololive__to<PERSON><PERSON>i_towa__hololive_186.webp"}, {"prompt": "vocaloid, kaito (vocaloid), vocaloid", "chinese_prompt": "KAITO (Vocaloid)", "image_path": "assets/output_1_vocaloid__kaito__vocaloid___vocaloid_187.webp"}, {"prompt": "touh<PERSON>, murasa minamitsu, touhou", "chinese_prompt": "村纱水蜜 (东方)", "image_path": "assets/output_1_touhou__murasa_minamitsu__touhou_188.webp"}, {"prompt": "hololive, ta<PERSON><PERSON> kiara, hololive", "chinese_prompt": "小鸟游琪亚拉 (Hololive)", "image_path": "assets/output_1_hololive__ta<PERSON><PERSON>_k<PERSON>__hololive_189.webp"}, {"prompt": "final_fantasy, aerith gainsborough, final fantasy", "chinese_prompt": "艾莉丝·盖恩兹博罗 (ff7) (最终幻想)", "image_path": "assets/output_1_final_fantasy__aerith_gainsborough__final_fantasy_190.webp"}, {"prompt": "kantai_collection, yukikaze (kancolle), kantai collection", "chinese_prompt": "雪风 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__yukikaze__kancolle___kantai_collection_191.webp"}, {"prompt": "cardcaptor_sakura, kinomoto sakura, cardcaptor sakura", "chinese_prompt": "木之本樱 (库洛魔法使)", "image_path": "assets/output_1_cardcaptor_sakura__kinomoto_sakura__cardcaptor_sakura_192.webp"}, {"prompt": "fire_emblem, byleth (fire emblem), fire emblem", "chinese_prompt": "贝雷丝 (风花雪月) (圣火降魔录)", "image_path": "assets/output_1_fire_emblem__byleth__fire_emblem___fire_emblem_193.webp"}, {"prompt": "love_live!, tou<PERSON> no<PERSON>, love live!", "chinese_prompt": "东条希 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_1_love_live___toujou_nozomi__love_live__194.webp"}, {"prompt": "kantai_collection, yamato (kancolle), kantai collection", "chinese_prompt": "大和 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__yamato__kancolle___kantai_collection_195.webp"}, {"prompt": "street_fighter, chun-li, street fighter", "chinese_prompt": "春丽 (快打旋风)", "image_path": "assets/output_1_street_fighter__chun-li__street_fighter_196.webp"}, {"prompt": "genshin_impact, nahida (genshin impact), genshin impact", "chinese_prompt": "纳西妲 (原神)", "image_path": "assets/output_1_genshin_impact__nahida__genshin_impact___genshin_impact_197.webp"}, {"prompt": "hololive, ouro kronii, hololive", "chinese_prompt": "奥罗·克洛尼 (Hololive)", "image_path": "assets/output_1_hololive__ouro_kronii__hololive_198.webp"}, {"prompt": "jojo_no_kimyou_na_bouken, kujo jotaro, jojo no kimyou na bouken", "chinese_prompt": "空条承太郎 (JOJO的奇妙冒险)", "image_path": "assets/output_1_jojo_no_kimyou_na_bouken__kujo_jotaro__jojo_no_kimyou_na_bouken_199.webp"}, {"prompt": "kantai_collection, atago (kancolle), kantai collection", "chinese_prompt": "爱宕 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__atago__kancolle___kantai_collection_200.webp"}, {"prompt": "love_live!, ya<PERSON> nico, love live!", "chinese_prompt": "矢泽日香 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_1_love_live___ya<PERSON>_nico__love_live__201.webp"}, {"prompt": "kirby_(series), kirby, kirby (series)", "chinese_prompt": "卡比 (星之卡比)", "image_path": "assets/output_1_kirby__series___kirby__kirby__series__202.webp"}, {"prompt": "pokemon, may (pokemon), pokemon", "chinese_prompt": "小遥 (宝可梦)", "image_path": "assets/output_1_pokemon__may__pokemon___pokemon_203.webp"}, {"prompt": "kantai_collection, shoukaku (kancolle), kantai collection", "chinese_prompt": "翔鹤 (舰队收藏)", "image_path": "assets/output_1_kantai_collection__shoukaku__kancolle___kantai_collection_204.webp"}, {"prompt": "dragon_ball, trunks (dragon ball), dragon ball", "chinese_prompt": "特兰克斯 (七龙珠)", "image_path": "assets/output_10_dragon_ball__trunks__dragon_ball___dragon_ball_0.webp"}, {"prompt": "sonic_(series), tails (sonic), sonic (series)", "chinese_prompt": "尾巴 (音速小子)", "image_path": "assets/output_10_sonic__series___tails__sonic___sonic__series__1.webp"}, {"prompt": "final_fantasy, zidane tribal, final fantasy", "chinese_prompt": "吉坦・特莱巴 (ff9) (最终幻想)", "image_path": "assets/output_10_final_fantasy__zidane_tribal__final_fantasy_2.webp"}, {"prompt": "kantai_collection, ta-class battleship, kantai collection", "chinese_prompt": "塔级战舰 (舰队收藏)", "image_path": "assets/output_10_kantai_collection__ta-class_battleship__kantai_collection_3.webp"}, {"prompt": "umamusume, mejiro a<PERSON> (umamusume), umamusume", "chinese_prompt": "目白阿尔丹 (赛马娘)", "image_path": "assets/output_10_umamusume__mejiro_ardan__umamusume___umamusume_4.webp"}, {"prompt": "zombie_land_saga, mizuno ai, zombie land saga", "chinese_prompt": "水野爱 (佐贺偶像是传奇)", "image_path": "assets/output_10_zombie_land_saga__mizuno_ai__zombie_land_saga_5.webp"}, {"prompt": "umamusume, air shakur (umamusume), umamusume", "chinese_prompt": "空中神宫 (赛马娘)", "image_path": "assets/output_10_umamusume__air_shakur__umamusume___umamusume_6.webp"}, {"prompt": "spy_x_family, dam<PERSON>, spy x family", "chinese_prompt": "达米安·德斯蒙德 (SPY x FAMILY 间谍家家酒)", "image_path": "assets/output_10_spy_x_family__dam<PERSON>_desmond__spy_x_family_7.webp"}, {"prompt": "project_sekai, kusa<PERSON>gi nene, project sekai", "chinese_prompt": "草薙宁音(Project Sekai)", "image_path": "assets/output_10_project_sekai__kusana<PERSON>_nene__project_sekai_8.webp"}, {"prompt": "kantai_collection, kako (kancolle), kantai collection", "chinese_prompt": "加古 (舰队收藏)", "image_path": "assets/output_10_kantai_collection__kako__kancolle___kantai_collection_9.webp"}, {"prompt": "fire_emblem, marth (fire emblem), fire emblem", "chinese_prompt": "马尔斯 (圣火降魔录)", "image_path": "assets/output_10_fire_emblem__marth__fire_emblem___fire_emblem_10.webp"}, {"prompt": "to_love-ru, yuuki mikan, to love-ru", "chinese_prompt": "结城美柑 (出包王女)", "image_path": "assets/output_10_to_love-ru__yuuki_mikan__to_love-ru_11.webp"}, {"prompt": "blue_archive, fubuki (blue archive), blue archive", "chinese_prompt": "合欢垣吹雪 (蔚蓝档案)", "image_path": "assets/output_10_blue_archive__fubuki__blue_archive___blue_archive_12.webp"}, {"prompt": "bishoujo_senshi_sailor_moon, sailor jupiter, bishoujo senshi sailor moon", "chinese_prompt": "水手木星 (美少女战士)", "image_path": "assets/output_10_bishoujo_senshi_sailor_moon__sailor_jupiter__bishoujo_senshi_sailor_moon_13.webp"}, {"prompt": "arknights, utage (arknights), arknights", "chinese_prompt": "宴 (明日方舟)", "image_path": "assets/output_10_arknights__utage__arknights___arknights_14.webp"}, {"prompt": "axis_powers_hetalia, america (hetalia), axis powers hetalia", "chinese_prompt": "美国 (义呆利)", "image_path": "assets/output_10_axis_powers_hetalia__america__hetalia___axis_powers_hetalia_15.webp"}, {"prompt": "idolmaster, ma<PERSON><PERSON> mizuki, idolmaster", "chinese_prompt": "真壁瑞希 (百万现场) (偶像大师)", "image_path": "assets/output_10_idolmaster__makabe_mizuki__idolmaster_16.webp"}, {"prompt": "kemono_friends, captain (kemono friends), kemono friends", "chinese_prompt": "captain (动物朋友)", "image_path": "assets/output_10_kemono_friends__captain__kemono_friends___kemono_friends_17.webp"}, {"prompt": "fate_(series), frank<PERSON>'s monster (fate), fate (series)", "chinese_prompt": "弗兰肯斯坦 (Fate)", "image_path": "assets/output_10_fate__series___frankenstein_s_monster__fate___fate__series__18.webp"}, {"prompt": "magia_record:_mahou_shoujo_madoka_magica_gaiden, al<PERSON> gray, magia record: mahou shoujo madoka magica gaiden", "chinese_prompt": "阿莉娜·格雷 (魔法纪录 魔法少女小圆外传)", "image_path": "assets/output_10_magia_record__mahou_shoujo_madoka_magica_gaiden__alina_gray__magia_record__mahou_shoujo_madoka_magica_gaiden_19.webp"}, {"prompt": "pokemon, grusha (pokemon), pokemon", "chinese_prompt": "古鲁夏 (宝可梦)", "image_path": "assets/output_10_pokemon__grusha__pokemon___pokemon_20.webp"}, {"prompt": "blue_archive, aris (maid) (blue archive), blue archive", "chinese_prompt": "天童爱丽丝 (女仆) (蔚蓝档案)", "image_path": "assets/output_10_blue_archive__aris__maid___blue_archive___blue_archive_21.webp"}, {"prompt": "touqi_guaitan, ziche fuzhao, touqi guaitan", "chinese_prompt": "子车甫昭 (头七怪谈)", "image_path": "assets/output_10_touqi_guaitan__ziche_fuzhao__touqi_guaitan_22.webp"}, {"prompt": "one_piece, portgas d. ace, one piece", "chinese_prompt": "艾斯 (海贼王)", "image_path": "assets/output_10_one_piece__portgas_d__ace__one_piece_23.webp"}, {"prompt": "touken_ranbu, saniwa (touken ranbu), touken ranbu", "chinese_prompt": "审神者 (刀剑乱舞)", "image_path": "assets/output_10_touken_ranbu__saniwa__touken_ranbu___touken_ranbu_24.webp"}, {"prompt": "azur_lane, yamash<PERSON> (azur lane), azur lane", "chinese_prompt": "山城 (碧蓝航线)", "image_path": "assets/output_10_azur_lane__yamashiro__azur_lane___azur_lane_25.webp"}, {"prompt": "idolmaster, sajo yuki<PERSON>, idolmaster", "chinese_prompt": "佐城雪美 (灰姑娘) (偶像大师)", "image_path": "assets/output_10_idolmaster__sajo_yuki<PERSON>__idolmaster_26.webp"}, {"prompt": "arknights, platinum (arknights), arknights", "chinese_prompt": "白金 (明日方舟)", "image_path": "assets/output_10_arknights__platinum__arknights___arknights_27.webp"}, {"prompt": "hololive, aki rosenthal, hololive", "chinese_prompt": "亚绮·罗森塔尔 (Hololive)", "image_path": "assets/output_10_hololive__aki_rose<PERSON><PERSON>__hololive_28.webp"}, {"prompt": "umamusume, hokko tarumae (umamusume), umamusume", "chinese_prompt": "北幸樽前 (赛马娘)", "image_path": "assets/output_10_umamusume__hokko_tarumae__umamusume___umamusume_29.webp"}, {"prompt": "arknights, ho'olheyak (arknights), arknights", "chinese_prompt": "霍尔海雅 (明日方舟)", "image_path": "assets/output_10_arknights__ho_olheyak__arknights___arknights_30.webp"}, {"prompt": "bang_dream!, mitake ran, bang dream!", "chinese_prompt": "美竹兰 (Bang Dream!)", "image_path": "assets/output_10_bang_dream___mitake_ran__bang_dream__31.webp"}, {"prompt": "pokemon, goh (pokemon), pokemon", "chinese_prompt": "小豪 (宝可梦)", "image_path": "assets/output_10_pokemon__goh__pokemon___pokemon_32.webp"}, {"prompt": "the_legend_of_zelda, toon link, the legend of zelda", "chinese_prompt": "卡通林克 (塞尔达传说)", "image_path": "assets/output_10_the_legend_of_zelda__toon_link__the_legend_of_zelda_33.webp"}, {"prompt": "arknights, ifrit (arknights), arknights", "chinese_prompt": "伊芙利特 (明日方舟)", "image_path": "assets/output_10_arknights__ifrit__arknights___arknights_34.webp"}, {"prompt": "girls_und_panzer, car<PERSON><PERSON><PERSON> (girls und panzer), girls und panzer", "chinese_prompt": "卡尔帕乔 (少女与战车)", "image_path": "assets/output_10_girls_und_panzer__carpa<PERSON>o__girls_und_panzer___girls_und_panzer_35.webp"}, {"prompt": "arknights, swire (arknights), arknights", "chinese_prompt": "诗怀雅 (明日方舟)", "image_path": "assets/output_10_arknights__swire__arknights___arknights_36.webp"}, {"prompt": "blue_archive, kirara (blue archive), blue archive", "chinese_prompt": "夜樱绮罗罗 (蔚蓝档案)", "image_path": "assets/output_10_blue_archive__kirara__blue_archive___blue_archive_37.webp"}, {"prompt": "omori, aubre<PERSON> (faraway) (omori), omori", "chinese_prompt": "AUBREY (faraway) (Omori)", "image_path": "assets/output_10_omori__aubrey__faraway___omori___omori_38.webp"}, {"prompt": "kono_subarashii_sekai_ni_shukufuku_wo!, yun<PERSON> (konosuba), kono subarashii sekai ni shukufuku wo!", "chinese_prompt": "芸芸 (为美好的世界献上祝福)", "image_path": "assets/output_10_kono_subarashii_sekai_ni_shukufuku_wo___yunyun__konosuba___kono_subarashii_sekai_ni_shukufuku_wo__39.webp"}, {"prompt": "undertale, chara (undertale), undertale", "chinese_prompt": "<PERSON><PERSON> (undertale)", "image_path": "assets/output_10_undertale__chara__undertale___undertale_40.webp"}, {"prompt": "kimi_no_na_wa., mi<PERSON><PERSON>u mitsuha, kimi no na wa.", "chinese_prompt": "宫水三叶 (你的名字)", "image_path": "assets/output_10_kimi_no_na_wa___miyamizu_mitsuha__kimi_no_na_wa__41.webp"}, {"prompt": "arknights, nearl (arknights), arknights", "chinese_prompt": "临光 (明日方舟)", "image_path": "assets/output_10_arknights__nearl__arknights___arknights_42.webp"}, {"prompt": "twisted_wonderland, floyd leech, twisted wonderland", "chinese_prompt": "<PERSON> (扭曲仙境)", "image_path": "assets/output_10_twisted_wonderland__floyd_leech__twisted_wonderland_43.webp"}, {"prompt": "honkai_(series), himeko (honkai: star rail), honkai (series)", "chinese_prompt": "姬子 (崩坏: 星穹铁道) (崩坏)", "image_path": "assets/output_10_honkai__series___himeko__honkai__star_rail___honkai__series__44.webp"}, {"prompt": "kantai_collection, nowaki (kancolle), kantai collection", "chinese_prompt": "野分 (舰队收藏)", "image_path": "assets/output_10_kantai_collection__nowaki__kancolle___kantai_collection_45.webp"}, {"prompt": "project_sekai, ka<PERSON><PERSON> rui, project sekai", "chinese_prompt": "Project Sekai(神代类)", "image_path": "assets/output_10_project_sekai__ka<PERSON><PERSON>_rui__project_sekai_46.webp"}, {"prompt": "kantai_collection, ru-class battleship, kantai collection", "chinese_prompt": "Ru级战舰 (舰队收藏)", "image_path": "assets/output_10_kantai_collection__ru-class_battleship__kantai_collection_47.webp"}, {"prompt": "ni<PERSON><PERSON><PERSON>, finana ryugu, ni<PERSON><PERSON><PERSON>", "chinese_prompt": "龙宫Finana (彩虹社)", "image_path": "assets/output_10_niji<PERSON><PERSON>__finana_ryugu__niji<PERSON>ji_48.webp"}, {"prompt": "berserk, guts (berserk), berserk", "chinese_prompt": "凯兹 (烙印勇士)", "image_path": "assets/output_10_berserk__guts__berserk___berserk_49.webp"}, {"prompt": "pokemon, cheren (pokemon), pokemon", "chinese_prompt": "黑连 (宝可梦)", "image_path": "assets/output_10_pokemon__cheren__pokemon___pokemon_50.webp"}, {"prompt": "helltaker, justice (helltaker), helltaker", "chinese_prompt": "正义 (Hell<PERSON>)", "image_path": "assets/output_10_helltaker__justice__helltaker___helltaker_51.webp"}, {"prompt": "saibou_shinkyoku, theodore riddle, saibou shinkyoku", "chinese_prompt": "<PERSON> (细胞神曲)", "image_path": "assets/output_10_saibou_shinkyoku__theodore_riddle__saibou_shinkyoku_52.webp"}, {"prompt": "fate_(series), x<PERSON><PERSON><PERSON> sanzang (fate), fate (series)", "chinese_prompt": "玄奘三藏 (Fate)", "image_path": "assets/output_10_fate__series___x<PERSON>zang_sanzang__fate___fate__series__53.webp"}, {"prompt": "kantai_collection, littorio (kancolle), kantai collection", "chinese_prompt": "利托里奥 (舰队收藏)", "image_path": "assets/output_10_kantai_collection__littorio__kancolle___kantai_collection_54.webp"}, {"prompt": "ni<PERSON><PERSON><PERSON>, enna al<PERSON>ette, ni<PERSON><PERSON><PERSON>", "chinese_prompt": "<PERSON><PERSON> (彩虹社)", "image_path": "assets/output_10_niji<PERSON><PERSON>__enna_alouette__niji<PERSON>ji_55.webp"}, {"prompt": "fate_(series), sieg (fate), fate (series)", "chinese_prompt": "齐格 (命运系列)", "image_path": "assets/output_10_fate__series___sieg__fate___fate__series__56.webp"}, {"prompt": "kantai_collection, intrepid (kancolle), kantai collection", "chinese_prompt": "无畏 (舰队收藏)", "image_path": "assets/output_10_kantai_collection__intrepid__kancolle___kantai_collection_57.webp"}, {"prompt": "the_legend_of_zelda, purah, the legend of zelda", "chinese_prompt": "普尔亚 (塞尔达传说)", "image_path": "assets/output_10_the_legend_of_zelda__purah__the_legend_of_zelda_58.webp"}, {"prompt": "little_nuns_(diva), clumsy nun (diva), little nuns (diva)", "chinese_prompt": "笨拙的修女 (小修女)", "image_path": "assets/output_10_little_nuns__diva___clumsy_nun__diva___little_nuns__diva__59.webp"}, {"prompt": "ib, ib (ib), ib", "chinese_prompt": "ib", "image_path": "assets/output_10_ib__ib__ib___ib_60.webp"}, {"prompt": "senki_zesshou_symphogear, maria cadenza<PERSON>na eve, senki zesshou symphogear", "chinese_prompt": "玛莉亚·卡登扎夫娜·伊芙 (战姬绝唱SYMPHOGEAR)", "image_path": "assets/output_10_senki_zess<PERSON>_symphogear__maria_cadenzavna_eve__senki_zesshou_symphogear_61.webp"}, {"prompt": "azur_lane, nagato (azur lane), azur lane", "chinese_prompt": "长门 (碧蓝航线)", "image_path": "assets/output_10_azur_lane__nagato__azur_lane___azur_lane_62.webp"}, {"prompt": "fire_emblem, doroth<PERSON> arna<PERSON>, fire emblem", "chinese_prompt": "多洛缇雅·雅尔诺尔德 (风花雪月) (圣火降魔录)", "image_path": "assets/output_10_fire_emblem__dorothea_arna<PERSON>__fire_emblem_63.webp"}, {"prompt": "azur_lane, amagi (azur lane), azur lane", "chinese_prompt": "天城 (碧蓝航线)", "image_path": "assets/output_10_azur_lane__amagi__azur_lane___azur_lane_64.webp"}, {"prompt": "pokemon, arven (pokemon), pokemon", "chinese_prompt": "派帕 (宝可梦)", "image_path": "assets/output_10_pokemon__arven__pokemon___pokemon_65.webp"}, {"prompt": "umamusume, gentildonna (umamusume), umamusume", "chinese_prompt": "贵妇人 (赛马娘)", "image_path": "assets/output_10_umamusume__gentildonna__umamusume___umamusume_66.webp"}, {"prompt": "sousou_no_frieren, him<PERSON> (sousou no frieren), sousou no frieren", "chinese_prompt": "欣梅尔 (葬送的芙莉莲)", "image_path": "assets/output_10_sousou_no_frieren__himmel__sousou_no_frieren___sousou_no_frieren_67.webp"}, {"prompt": "project_moon, ryoshu (project moon), project moon", "chinese_prompt": "良秀 (Project Moon)", "image_path": "assets/output_10_project_moon__ryoshu__project_moon___project_moon_68.webp"}, {"prompt": "blue_archive, miyu (swimsuit) (blue archive), blue archive", "chinese_prompt": "霞泽美游 (泳装) (蔚蓝档案)", "image_path": "assets/output_10_blue_archive__miyu__swimsuit___blue_archive___blue_archive_69.webp"}, {"prompt": "resident_evil, jill valentine, resident evil", "chinese_prompt": "吉儿·瓦伦丁(恶灵古堡)", "image_path": "assets/output_10_resident_evil__jill_valentine__resident_evil_70.webp"}, {"prompt": "pokemon, chikorita, pokemon", "chinese_prompt": "菊草叶 (宝可梦)", "image_path": "assets/output_10_pokemon__chikorita__pokemon_71.webp"}, {"prompt": "tokyo_ghoul, kaneki ken, tokyo ghoul", "chinese_prompt": "金木研 (东京喰种)", "image_path": "assets/output_10_tokyo_ghoul__kaneki_ken__tokyo_ghoul_72.webp"}, {"prompt": "idolmaster, yukoku kiriko, idolmaster", "chinese_prompt": "幽谷雾子 (闪耀色彩) (偶像大师)", "image_path": "assets/output_10_idolmaster__yuk<PERSON>_kiri<PERSON>__idolmaster_73.webp"}, {"prompt": "pokemon, me<PERSON>carada, pokemon", "chinese_prompt": "魔幻假面喵 (宝可梦)", "image_path": "assets/output_10_pokemon__me<PERSON>carada__pokemon_74.webp"}, {"prompt": "guilty_gear, millia rage, guilty gear", "chinese_prompt": "米莉亚=蕾姬 (圣骑士之战)", "image_path": "assets/output_10_guilty_gear__millia_rage__guilty_gear_75.webp"}, {"prompt": "pokemon, mudkip, pokemon", "chinese_prompt": "水跃鱼 (宝可梦)", "image_path": "assets/output_10_pokemon__mudkip__pokemon_76.webp"}, {"prompt": "golden_kamuy, sugi<PERSON> saichi, golden kamuy", "chinese_prompt": "杉元佐一 (黄金神威)", "image_path": "assets/output_10_golden_kamuy__sugi<PERSON>_sa<PERSON>__golden_kamuy_77.webp"}, {"prompt": "pokemon, pichu, pokemon", "chinese_prompt": "皮丘(宝可梦)", "image_path": "assets/output_10_pokemon__pichu__pokemon_78.webp"}, {"prompt": "persona, ok<PERSON><PERSON> haru, persona", "chinese_prompt": "奥村春 (P5) (女神异闻录)", "image_path": "assets/output_10_persona__ok<PERSON><PERSON>_haru__persona_79.webp"}, {"prompt": "guilty_gear, jack-o' valentine, guilty gear", "chinese_prompt": "杰克·奥瓦伦丁 (圣骑士之战)", "image_path": "assets/output_10_guilty_gear__jack-o__valentine__guilty_gear_80.webp"}, {"prompt": "hololive, nakiri ayame (1st costume), hololive", "chinese_prompt": "百鬼绫目 (1st服) (Hololive)", "image_path": "assets/output_10_hololive__nakiri_ayame__1st_costume___hololive_81.webp"}, {"prompt": "project_moon, employee (project moon), project moon", "chinese_prompt": "职员 (Project Moon)", "image_path": "assets/output_10_project_moon__employee__project_moon___project_moon_82.webp"}, {"prompt": "project_moon, angela (project moon), project moon", "chinese_prompt": "<PERSON> (Project Moon)", "image_path": "assets/output_10_project_moon__angela__project_moon___project_moon_83.webp"}, {"prompt": "kantai_collection, murasame kai ni (kancolle), kantai collection", "chinese_prompt": "村雨改二 (舰队收藏)", "image_path": "assets/output_10_kantai_collection__murasame_kai_ni__kancolle___kantai_collection_84.webp"}, {"prompt": "call_of_duty, ghost (modern warfare 2), call of duty", "chinese_prompt": "幽灵 (决胜时刻：现代战争2)", "image_path": "assets/output_10_call_of_duty__ghost__modern_warfare_2___call_of_duty_85.webp"}, {"prompt": "umamusume, winning ticket (umamusume), umamusume", "chinese_prompt": "胜利奖券 (赛马娘)", "image_path": "assets/output_10_umamusume__winning_ticket__umamusume___umamusume_86.webp"}, {"prompt": "arknights, male doctor (arknights), arknights", "chinese_prompt": "男性博士 (明日方舟)", "image_path": "assets/output_10_arknights__male_doctor__arknights___arknights_87.webp"}, {"prompt": "fate_(series), medjed (fate), fate (series)", "chinese_prompt": "梅杰德 (Fate)", "image_path": "assets/output_10_fate__series___medjed__fate___fate__series__88.webp"}, {"prompt": "girls'_frontline, ro635 (girls' frontline), girls' frontline", "chinese_prompt": "RO635 (少女前线)", "image_path": "assets/output_10_girls__frontline__ro635__girls__frontline___girls__frontline_89.webp"}, {"prompt": "soul_eater, maka albarn, soul eater", "chinese_prompt": "玛卡·亚邦 (SOUL EATER 噬魂者)", "image_path": "assets/output_10_soul_eater__maka_albarn__soul_eater_90.webp"}, {"prompt": "kantai_collection, takanami (kancolle), kantai collection", "chinese_prompt": "高波 (舰队收藏)", "image_path": "assets/output_10_kantai_collection__takan<PERSON>__kancolle___kantai_collection_91.webp"}, {"prompt": "kobayashi-san_chi_no_maidragon, il<PERSON> (maidragon), kobayashi-san chi no maidragon", "chinese_prompt": "伊露露 (小林家的龙女仆)", "image_path": "assets/output_10_kobayashi-san_chi_no_maidragon__ilulu__maidragon___kobayashi-san_chi_no_maidragon_92.webp"}, {"prompt": "yu-gi-oh!, tenjou<PERSON> asuka, yu-gi-oh!", "chinese_prompt": "天上院明日香 (游戏王)", "image_path": "assets/output_10_yu-gi-oh___tenjouin_asuka__yu-gi-oh__93.webp"}, {"prompt": "elsword, eve (elsword), elsword", "chinese_prompt": "伊芙 (艾尔之光)", "image_path": "assets/output_10_elsword__eve__elsword___elsword_94.webp"}, {"prompt": "kemono_friends, dhole (kemono friends), kemono friends", "chinese_prompt": "豺狗 (动物朋友)", "image_path": "assets/output_10_kemono_friends__dhole__kemono_friends___kemono_friends_95.webp"}, {"prompt": "tsu<PERSON><PERSON><PERSON>, to<PERSON>o shiki, tsuki<PERSON>e", "chinese_prompt": "远野志贵 (真月谭－月姬)", "image_path": "assets/output_10_tsukihime__tohno_shiki__tsukihime_96.webp"}, {"prompt": "final_fantasy, y'shtola rhul, final fantasy", "chinese_prompt": "雅·修特拉 (ff14) (最终幻想)", "image_path": "assets/output_10_final_fantasy__y_shtola_rhul__final_fantasy_97.webp"}, {"prompt": "blue_archive, hina (dress) (blue archive), blue archive", "chinese_prompt": "空崎阳奈 (礼服) (蔚蓝档案)", "image_path": "assets/output_10_blue_archive__hina__dress___blue_archive___blue_archive_98.webp"}, {"prompt": "idolmaster, r<PERSON><PERSON> ka<PERSON><PERSON>, idolmaster", "chinese_prompt": "龙崎薰 (灰姑娘) (偶像大师)", "image_path": "assets/output_10_idolmaster__r<PERSON><PERSON>_ka<PERSON><PERSON>__idolmaster_99.webp"}, {"prompt": "overwatch, pharah (overwatch), overwatch", "chinese_prompt": "法拉 (斗阵特攻)", "image_path": "assets/output_10_overwatch__pharah__overwatch___overwatch_100.webp"}, {"prompt": "among_us, crewmate (among us), among us", "chinese_prompt": "船员 (太空狼人杀)", "image_path": "assets/output_10_among_us__crewmate__among_us___among_us_101.webp"}, {"prompt": "kantai_collection, mochizuki (kancolle), kantai collection", "chinese_prompt": "望月 (舰队收藏)", "image_path": "assets/output_10_kantai_collection__mochizuki__kancolle___kantai_collection_102.webp"}, {"prompt": "darker_than_black, yin (darker than black), darker than black", "chinese_prompt": "银 (黑之契约者)", "image_path": "assets/output_10_darker_than_black__yin__darker_than_black___darker_than_black_103.webp"}, {"prompt": "azur_lane, new jersey (azur lane), azur lane", "chinese_prompt": "纽泽西 (碧蓝航线)", "image_path": "assets/output_10_azur_lane__new_jersey__azur_lane___azur_lane_104.webp"}, {"prompt": "honkai_(series), murata himeko, honkai (series)", "chinese_prompt": "无量塔姬子 (崩坏)", "image_path": "assets/output_10_honkai__series___murata_himeko__honkai__series__105.webp"}, {"prompt": "final_fantasy, garnet til alexandros xvii, final fantasy", "chinese_prompt": "佳妮德・迪尔・亚历山大17世 (ff9) (最终幻想)", "image_path": "assets/output_10_final_fantasy__garnet_til_alexandros_xvii__final_fantasy_106.webp"}, {"prompt": "xenosaga, kos-mos, xenosaga", "chinese_prompt": "KOS-MOS (异度神剑)", "image_path": "assets/output_10_xenosaga__kos-mos__xenosaga_107.webp"}, {"prompt": "little_witch_academia, sucy man<PERSON><PERSON>an, little witch academia", "chinese_prompt": "苏西·曼芭芭拉 (小魔女学院)", "image_path": "assets/output_10_little_witch_academia__sucy_man<PERSON><PERSON><PERSON>__little_witch_academia_108.webp"}, {"prompt": "precure, cure blossom, precure", "chinese_prompt": "花咲蕾 cure blossom (光之美少女)", "image_path": "assets/output_10_precure__cure_blossom__precure_109.webp"}, {"prompt": "seishun_buta_yarou, sa<PERSON><PERSON> mai, seishun buta yarou", "chinese_prompt": "樱岛麻衣 (青春猪头少年)", "image_path": "assets/output_10_seishun_buta_yarou__sakurajima_mai__seishun_buta_yarou_110.webp"}, {"prompt": "arknights, virtuosa (arknights), arknights", "chinese_prompt": "塑心 (明日方舟)", "image_path": "assets/output_10_arknights__virtuosa__arknights___arknights_111.webp"}, {"prompt": "mother_2, ness (mother 2), mother 2", "chinese_prompt": "奈斯 (地球冒险2)", "image_path": "assets/output_10_mother_2__ness__mother_2___mother_2_112.webp"}, {"prompt": "puyopuyo, arle nadja, puyopuyo", "chinese_prompt": "阿尔露·纳吉亚 (魔法气泡)", "image_path": "assets/output_10_puyopuyo__arle_nadja__puyopuyo_113.webp"}, {"prompt": "kantai_collection, jingei (kancolle), kantai collection", "chinese_prompt": "迅鲸 (舰队收藏)", "image_path": "assets/output_10_kantai_collection__jingei__kancolle___kantai_collection_114.webp"}, {"prompt": "saibou_shinkyoku, kanou aogu, saibou shinkyoku", "chinese_prompt": "嘉纳扇 (细胞神曲)", "image_path": "assets/output_10_saibou_shinkyoku__kanou_aogu__saibou_shinkyoku_115.webp"}, {"prompt": "genshin_impact, thoma (genshin impact), genshin impact", "chinese_prompt": "托马 (原神)", "image_path": "assets/output_10_genshin_impact__thoma__genshin_impact___genshin_impact_116.webp"}, {"prompt": "xenoblade_chronicles_(series), e<PERSON><PERSON> (xenoblade), xenoblade chronicles (series)", "chinese_prompt": "优妮 (异度神剑)", "image_path": "assets/output_10_xenoblade_chronicles__series___eunie__xenoblade___xenoblade_chronicles__series__117.webp"}, {"prompt": "vocaloid, brazilian miku, vocaloid", "chinese_prompt": "巴西初音 (Vocaloid)", "image_path": "assets/output_10_vocaloid__brazilian_miku__vocaloid_118.webp"}, {"prompt": "league_of_legends, lux (league of legends), league of legends", "chinese_prompt": "拉克丝 (英雄联盟 LOL)", "image_path": "assets/output_10_league_of_legends__lux__league_of_legends___league_of_legends_119.webp"}, {"prompt": "blue_archive, izuna (swimsuit) (blue archive), blue archive", "chinese_prompt": "久田伊树菜 (泳装) (蔚蓝档案)", "image_path": "assets/output_10_blue_archive__i<PERSON>na__swimsuit___blue_archive___blue_archive_120.webp"}, {"prompt": "the_legend_of_zelda, ganondo<PERSON>, the legend of zelda", "chinese_prompt": "伽农 (萨尔达传说)", "image_path": "assets/output_10_the_legend_of_zelda__ganondorf__the_legend_of_zelda_121.webp"}, {"prompt": "monogatari_(series), o<PERSON>o ougi, monogatari (series)", "chinese_prompt": "忍野扇 (化物语)", "image_path": "assets/output_10_monogatari__series___oshino_ougi__monogatari__series__122.webp"}, {"prompt": "saenai_heroine_no_sodatekata, ka<PERSON>u megumi, saenai heroine no sodatekata", "chinese_prompt": "加藤惠 (不起眼女主角培育法)", "image_path": "assets/output_10_saenai_heroine_no_sodatekata__katou_megumi__saenai_heroine_no_sodatekata_123.webp"}, {"prompt": "blazblue, nu-13, blazblue", "chinese_prompt": "NU-13 (苍翼默示录)", "image_path": "assets/output_10_blazblue__nu-13__blazblue_124.webp"}, {"prompt": "precure, cure sunshine, precure", "chinese_prompt": "明堂院树 Cure Sunshine (光之美少女)", "image_path": "assets/output_10_precure__cure_sunshine__precure_125.webp"}, {"prompt": "touken_ranbu, i<PERSON><PERSON><PERSON><PERSON>-kami kanesada, touken ranbu", "chinese_prompt": "泉之守兼定 (刀剑乱舞)", "image_path": "assets/output_10_touken_ranbu__i<PERSON><PERSON>-no-kami_kanesada__touken_ranbu_126.webp"}, {"prompt": "tiger_&_bunny, huang baoling, tiger & bunny", "chinese_prompt": "黄宝玲 (TIGER & BUNNY)", "image_path": "assets/output_10_tiger___bunny__huang_baoling__tiger___bunny_127.webp"}, {"prompt": "sword_art_online, yuuki (sao), sword art online", "chinese_prompt": "有纪 绀野木绵季 (刀剑神域)", "image_path": "assets/output_10_sword_art_online__yuuki__sao___sword_art_online_128.webp"}, {"prompt": "precure, cure beauty, precure", "chinese_prompt": "青木丽华 cure beauty (光之美少女)", "image_path": "assets/output_10_precure__cure_beauty__precure_129.webp"}, {"prompt": "golden_kamuy, ogata hyaku<PERSON>, golden kamuy", "chinese_prompt": "尾形百之助 (黄金神威)", "image_path": "assets/output_10_golden_kamuy__ogata_hyaku<PERSON>uke__golden_kamuy_130.webp"}, {"prompt": "genshin_impact, gorou (genshin impact), genshin impact", "chinese_prompt": "五郎 (原神)", "image_path": "assets/output_10_genshin_impact__gorou__genshin_impact___genshin_impact_131.webp"}, {"prompt": "hololive, shi<PERSON> novella (1st costume), hololive", "chinese_prompt": "诗织·诺维拉 (1st服) (Hololive)", "image_path": "assets/output_10_hololive__shi<PERSON>_novella__1st_costume___hololive_132.webp"}, {"prompt": "the_king_of_fighters, kula diamond, the king of fighters", "chinese_prompt": "库拉・黛雅门度 (KOF)", "image_path": "assets/output_10_the_king_of_fighters__kula_diamond__the_king_of_fighters_133.webp"}, {"prompt": "doki_doki_literature_club, natsuki (doki doki literature club), doki doki literature club", "chinese_prompt": "夏树 (心跳文学部)", "image_path": "assets/output_10_doki_doki_literature_club__natsuki__doki_doki_literature_club___doki_doki_literature_club_134.webp"}, {"prompt": "idolmaster, tada riina, idolmaster", "chinese_prompt": "多田李衣菜 (灰姑娘) (偶像大师)", "image_path": "assets/output_10_idolmaster__tada_riina__idolmaster_135.webp"}, {"prompt": "aikatsu!_(series), kiriya aoi, aikatsu! (series)", "chinese_prompt": "雾矢葵 (偶像活动！)", "image_path": "assets/output_10_aikatsu___series___kiriya_aoi__aikatsu___series__136.webp"}, {"prompt": "genshin_impact, crystalfly (genshin impact), genshin impact", "chinese_prompt": "水晶蝶 (原神)", "image_path": "assets/output_10_genshin_impact__crystalfly__genshin_impact___genshin_impact_137.webp"}, {"prompt": "hololive, mori calliope (streetwear), hololive", "chinese_prompt": "森美声 (街头服饰) (Hololive)", "image_path": "assets/output_10_hololive__mori_calliope__streetwear___hololive_138.webp"}, {"prompt": "fate_(series), merlin (fate/prototype), fate (series)", "chinese_prompt": "梅林 (Fate/Prototype) (Fate)", "image_path": "assets/output_10_fate__series___merlin__fate_prototype___fate__series__139.webp"}, {"prompt": "love_live!, heanna sumire, love live!", "chinese_prompt": "平安名堇 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_10_love_live___heanna_sumire__love_live__140.webp"}, {"prompt": "umamusume, narita top road (umamusume), umamusume", "chinese_prompt": "成田路 (赛马娘)", "image_path": "assets/output_10_umamusume__narita_top_road__umamusume___umamusume_141.webp"}, {"prompt": "ace_attorney, phoenix wright, ace attorney", "chinese_prompt": "成步堂龙一 (逆转裁判)", "image_path": "assets/output_10_ace_attorney__phoenix_wright__ace_attorney_142.webp"}, {"prompt": "<PERSON><PERSON><PERSON><PERSON>, sister claire, ni<PERSON><PERSON><PERSON>", "chinese_prompt": "修女·克蕾雅 (彩虹社)", "image_path": "assets/output_10_niji<PERSON><PERSON>__sister_claire__niji<PERSON><PERSON>_143.webp"}, {"prompt": "love_live!, tang keke, love live!", "chinese_prompt": "唐可可 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_10_love_live___tang_keke__love_live__144.webp"}, {"prompt": "honkai_(series), brony<PERSON> (silverwing: n-ex), honkai (series)", "chinese_prompt": "布洛妮娅 次生银翼 (崩坏)", "image_path": "assets/output_10_honkai__series___bronya_z<PERSON><PERSON><PERSON>__silverwing__n-ex___honkai__series__145.webp"}, {"prompt": "fate_(series), as<PERSON><PERSON><PERSON> (sailor paladin) (fate), fate (series)", "chinese_prompt": "阿斯托尔福 (水手服) (Fate)", "image_path": "assets/output_10_fate__series___astolfo__sailor_paladin___fate___fate__series__146.webp"}, {"prompt": "princess_connect!, yui (princess connect!), princess connect!", "chinese_prompt": "草野优衣 (公主连结)", "image_path": "assets/output_10_princess_connect___yui__princess_connect____princess_connect__147.webp"}, {"prompt": "to<PERSON><PERSON>, t<PERSON><PERSON><PERSON>, to<PERSON><PERSON>", "chinese_prompt": "九十九八桥 (东方)", "image_path": "assets/output_10_touh<PERSON>__t<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>__touhou_148.webp"}, {"prompt": "azur_lane, prinz eugen (unfading smile) (azur lane), azur lane", "chinese_prompt": "欧根亲王 (永不退去的笑容) (碧蓝航线)", "image_path": "assets/output_10_azur_lane__prinz_eugen__unfading_smile___azur_lane___azur_lane_149.webp"}, {"prompt": "tensei_shitara_slime_datta_ken, rimuru tempest, tensei shitara slime datta ken", "chinese_prompt": "利姆鲁·坦佩斯特 (转生成为史莱姆)", "image_path": "assets/output_10_tensei_shitara_slime_datta_ken__rimuru_tempest__tensei_shitara_slime_datta_ken_150.webp"}, {"prompt": "kantai_collection, hornet (kancolle), kantai collection", "chinese_prompt": "大黄蜂 (舰队收藏)", "image_path": "assets/output_10_kantai_collection__hornet__kancolle___kantai_collection_151.webp"}, {"prompt": "fate_(series), berserker (fate/zero), fate (series)", "chinese_prompt": "狂战士 (Fate)", "image_path": "assets/output_10_fate__series___berserker__fate_zero___fate__series__152.webp"}, {"prompt": "yu-gi-oh!, izayoi aki, yu-gi-oh!", "chinese_prompt": "十六夜亚纪 (游戏王)", "image_path": "assets/output_10_yu-gi-oh___izayoi_aki__yu-gi-oh__153.webp"}, {"prompt": "aldnoah.zero, slaine troyard, aldnoah.zero", "chinese_prompt": "斯雷因·特洛耶特 (Aldnoah.Zero)", "image_path": "assets/output_10_aldnoah_zero__slaine_troyard__aldnoah_zero_154.webp"}, {"prompt": "fate/grand_order, scathach (swimsuit assassin) (fate), fate/grand order", "chinese_prompt": "斯卡哈·斯卡蒂（泳装）(Fate/Grand Order)", "image_path": "assets/output_10_fate_grand_order__scathach__swimsuit_assassin___fate___fate_grand_order_155.webp"}, {"prompt": "tales_of_(series), estellise sidos he<PERSON>, tales of (series)", "chinese_prompt": "艾丝缇莉洁 (TOV) (传说系列)", "image_path": "assets/output_10_tales_of__series___estellise_sidos_heurassein__tales_of__series__156.webp"}, {"prompt": "arknights, pramanix (arknights), arknights", "chinese_prompt": "初雪 (明日方舟)", "image_path": "assets/output_10_arknights__pramanix__arknights___arknights_157.webp"}, {"prompt": "hololive, nerissa r<PERSON> (1st costume), hololive", "chinese_prompt": "纳瑞莎·雷文克罗夫特 (1st服) (Hololive)", "image_path": "assets/output_10_hololive__nerissa_raven<PERSON>__1st_costume___hololive_158.webp"}, {"prompt": "dragon's_crown, sorceress (dragon's crown), dragon's crown", "chinese_prompt": "女巫 (魔龙宝冠)", "image_path": "assets/output_10_dragon_s_crown__sorceress__dragon_s_crown___dragon_s_crown_159.webp"}, {"prompt": "blue_archive, iori (swimsuit) (blue archive), blue archive", "chinese_prompt": "银镜伊织 (泳装) (蔚蓝档案)", "image_path": "assets/output_10_blue_archive__iori__swimsuit___blue_archive___blue_archive_160.webp"}, {"prompt": "hibike!_euphonium, ka<PERSON>, hibike! euphonium", "chinese_prompt": "伞木希美 (吹响吧！上低音号)", "image_path": "assets/output_10_hibike__euphonium__ka<PERSON>_no<PERSON>mi__hibike__euphonium_161.webp"}, {"prompt": "hololive, sakura miko (casual), hololive", "chinese_prompt": "樱巫女 (便服) (Hololive)", "image_path": "assets/output_10_hololive__sakura_miko__casual___hololive_162.webp"}, {"prompt": "final_fantasy, chocobo, final fantasy", "chinese_prompt": "陆行鸟 (最终幻想)", "image_path": "assets/output_10_final_fantasy__chocobo__final_fantasy_163.webp"}, {"prompt": "boku_no_kokoro_no_yabai_yatsu, yamada anna, boku no kokoro no yabai yatsu", "chinese_prompt": "山田杏奈 (我内心的糟糕念头)", "image_path": "assets/output_10_boku_no_kokoro_no_yabai_yatsu__yamada_anna__boku_no_kokoro_no_yabai_yatsu_164.webp"}, {"prompt": "idolmaster, na<PERSON><PERSON>, idolmaster", "chinese_prompt": "七尾百合子 (百万现场) (偶像大师)", "image_path": "assets/output_10_idolmaster__nana<PERSON>_y<PERSON><PERSON>__idolmaster_165.webp"}, {"prompt": "violet_evergarden_(series), violet evergarden, violet evergarden (series)", "chinese_prompt": "薇尔莉特·伊芙加登 (紫罗兰永恒花园)", "image_path": "assets/output_10_violet_evergarden__series___violet_evergarden__violet_evergarden__series__166.webp"}, {"prompt": "mahou_shoujo_madoka_magica, momoe nagisa, mahou shoujo madoka magica", "chinese_prompt": "百江渚 (魔法少女小圆)", "image_path": "assets/output_10_mahou_shoujo_madoka_magica__momoe_nagisa__mahou_shoujo_madoka_magica_167.webp"}, {"prompt": "trigun, vash the stampede, trigun", "chinese_prompt": "威席‧史坦毕特 (枪神)", "image_path": "assets/output_10_trigun__vash_the_stampede__trigun_168.webp"}, {"prompt": "girls_und_panzer, sawa azu<PERSON>, girls und panzer", "chinese_prompt": "泽和梓 (少女与战车)", "image_path": "assets/output_10_girls_und_panzer__sawa_a<PERSON>sa__girls_und_panzer_169.webp"}, {"prompt": "marvel, gwen stacy, marvel", "chinese_prompt": "关·史黛西 (Marvel)", "image_path": "assets/output_10_marvel__gwen_stacy__marvel_170.webp"}, {"prompt": "fullmetal_alchemist, winry rockbell, fullmetal alchemist", "chinese_prompt": "温莉·洛克贝尔 (钢之炼金术师)", "image_path": "assets/output_10_fullmetal_alchemist__winry_rockbell__fullmetal_alchemist_171.webp"}, {"prompt": "kantai_collection, roma (kancolle), kantai collection", "chinese_prompt": "罗马 (舰队收藏)", "image_path": "assets/output_10_kantai_collection__roma__kancolle___kantai_collection_172.webp"}, {"prompt": "mushoku_tensei, sylphiette (mushoku tensei), mushoku tensei", "chinese_prompt": "希露菲叶特 (无职转生)", "image_path": "assets/output_10_mushoku_tensei__sylphiette__mushoku_tensei___mushoku_tensei_173.webp"}, {"prompt": "tokyo_afterschool_summoners, protagonist 3 (<PERSON><PERSON><PERSON>), tokyo afterschool summoners", "chinese_prompt": "", "image_path": "assets/output_10_tokyo_afterschool_summoners__protagonist_3__housamo___tokyo_afterschool_summoners_174.webp"}, {"prompt": "kagerou_project, k<PERSON><PERSON>i shin<PERSON>, kagerou project", "chinese_prompt": "如月伸太郎 (阳炎计划)", "image_path": "assets/output_10_kagerou_project__k<PERSON><PERSON><PERSON>_shin<PERSON><PERSON>__kagerou_project_175.webp"}, {"prompt": "splatoon_(series), frye (splatoon), splatoon (series)", "chinese_prompt": "莎莎 (斯普拉遁)", "image_path": "assets/output_10_splatoon__series___frye__splatoon___splatoon__series__176.webp"}, {"prompt": "steins;gate, okabe rintarou, steins;gate", "chinese_prompt": "冈部伦太郎 (命运石之门)", "image_path": "assets/output_10_steins_gate__okabe_rintarou__steins_gate_177.webp"}, {"prompt": "kino_no_tabi, kino (kino no tabi), kino no tabi", "chinese_prompt": "奇诺 (奇诺之旅)", "image_path": "assets/output_10_kino_no_tabi__kino__kino_no_tabi___kino_no_tabi_178.webp"}, {"prompt": "pokemon, ingo (pokemon), pokemon", "chinese_prompt": "北<PERSON> (宝可梦)", "image_path": "assets/output_10_pokemon__ingo__pokemon___pokemon_179.webp"}, {"prompt": "fullmetal_alchemist, alphonse <PERSON>, fullmetal alchemist", "chinese_prompt": "阿尔冯斯·爱力克 (钢之炼金术师)", "image_path": "assets/output_10_fullmetal_alchemist__alphonse_el<PERSON>__fullmetal_alchemist_180.webp"}, {"prompt": "axis_powers_hetalia, united kingdom (hetalia), axis powers hetalia", "chinese_prompt": "英国 (义呆利)", "image_path": "assets/output_10_axis_powers_hetalia__united_kingdom__hetalia___axis_powers_hetalia_181.webp"}, {"prompt": "jojo_no_kimyou_na_bouken, kars (jojo), jojo no kimyou na bouken", "chinese_prompt": "卡兹 (JOJO的奇妙冒险)", "image_path": "assets/output_10_jojo_no_kimyou_na_bouken__kars__jojo___jojo_no_kimyou_na_bouken_182.webp"}, {"prompt": "pokemon, penny (pokemon), pokemon", "chinese_prompt": "牡丹 (宝可梦)", "image_path": "assets/output_10_pokemon__penny__pokemon___pokemon_183.webp"}, {"prompt": "rozen_maiden, hi<PERSON><PERSON><PERSON>, rozen maiden", "chinese_prompt": "雏苺 (蔷薇少女)", "image_path": "assets/output_10_rozen_maiden__hi<PERSON><PERSON><PERSON>__rozen_maiden_184.webp"}, {"prompt": "touhou, okazaki yumemi, touhou", "chinese_prompt": "冈崎梦美 (东方)", "image_path": "assets/output_10_touh<PERSON>__okaz<PERSON>_yume<PERSON>__touhou_185.webp"}, {"prompt": "hololive, shiro<PERSON>e noel (1st costume), hololive", "chinese_prompt": "白银诺艾尔 (1st服) (Hololive)", "image_path": "assets/output_10_hololive__shirogane_noel__1st_costume___hololive_186.webp"}, {"prompt": "honkai_(series), raiden mei (herrscher of thunder), honkai (series)", "chinese_prompt": "雷电芽衣 (雷之律者) (崩坏)", "image_path": "assets/output_10_honkai__series___raiden_mei__herrscher_of_thunder___honkai__series__187.webp"}, {"prompt": "granblue_fantasy, ferry (granblue fantasy), granblue fantasy", "chinese_prompt": "费莉 (碧蓝幻想)", "image_path": "assets/output_10_granblue_fantasy__ferry__granblue_fantasy___granblue_fantasy_188.webp"}, {"prompt": "kimetsu_no_yaiba, re<PERSON><PERSON> kyo<PERSON><PERSON><PERSON>, kimetsu no yaiba", "chinese_prompt": "炼狱杏寿郎 (鬼灭之刃)", "image_path": "assets/output_10_kimetsu_no_yaiba__rengoku_kyoujurou__kimetsu_no_yaiba_189.webp"}, {"prompt": "ni<PERSON><PERSON><PERSON>, yo<PERSON>i rena, ni<PERSON><PERSON><PERSON>", "chinese_prompt": "夜见蕾娜 (彩虹社)", "image_path": "assets/output_10_niji<PERSON><PERSON>__yo<PERSON><PERSON>_rena__niji<PERSON>ji_190.webp"}, {"prompt": "maria-sama_ga_miteru, f<PERSON><PERSON> yumi, maria-sama ga miteru", "chinese_prompt": "福泽祐美 (圣母在上)", "image_path": "assets/output_10_maria-sama_ga_miteru__fukuzawa_yumi__maria-sama_ga_miteru_191.webp"}, {"prompt": "tsuki<PERSON>e, <PERSON><PERSON> (tsuki<PERSON>e), tsuki<PERSON>e", "chinese_prompt": "翡翠 (真月谭－月姬)", "image_path": "assets/output_10_tsukihime__hisui__tsukihime___tsukihime_192.webp"}, {"prompt": "fate_(series), florence nightingale (trick or treatment) (fate), fate (series)", "chinese_prompt": "南丁格尔 (万圣节) (Fate)", "image_path": "assets/output_10_fate__series___florence_nightingale__trick_or_treatment___fate___fate__series__193.webp"}, {"prompt": "arknights, la pluma (arknights), arknights", "chinese_prompt": "羽毛笔 (明日方舟)", "image_path": "assets/output_10_arknights__la_pluma__arknights___arknights_194.webp"}, {"prompt": "limbus_company, dante (limbus company), limbus company", "chinese_prompt": "但丁(边狱公司)", "image_path": "assets/output_10_limbus_company__dante__limbus_company___limbus_company_195.webp"}, {"prompt": "fate/grand_order, mi<PERSON><PERSON> musa<PERSON> (swimsuit berserker) (second ascension) (fate), fate/grand order", "chinese_prompt": "宫本武藏 (泳装狂战士) (第二灵基) (Fate/Grand Order)", "image_path": "assets/output_10_fate_grand_order__mi<PERSON>oto_musashi__swimsuit_berserker___second_ascension___fate___fate_grand_order_196.webp"}, {"prompt": "fate_(series), u<PERSON><PERSON><PERSON> r<PERSON><PERSON>, fate (series)", "chinese_prompt": "雨生龙之介 (Fate)", "image_path": "assets/output_10_fate__series___uryuu_r<PERSON><PERSON><PERSON>__fate__series__197.webp"}, {"prompt": "bang_dream!, wakaba mutsumi, bang dream!", "chinese_prompt": "若叶睦 (Bang Dream!)", "image_path": "assets/output_10_bang_dream___wakaba_mutsumi__bang_dream__198.webp"}, {"prompt": "granblue_fantasy, lyria (granblue fantasy), granblue fantasy", "chinese_prompt": "莉莉亚 (碧蓝幻想)", "image_path": "assets/output_10_granblue_fantasy__lyria__granblue_fantasy___granblue_fantasy_199.webp"}, {"prompt": "fate/grand_order, abigail williams (traveling outfit) (fate), fate/grand order", "chinese_prompt": "艾比盖儿·威廉斯 (旅行服) (Fate/Grand Order)", "image_path": "assets/output_10_fate_grand_order__abigail_williams__traveling_outfit___fate___fate_grand_order_200.webp"}, {"prompt": "fire_emblem, ingrid brandl galatea, fire emblem", "chinese_prompt": "英谷莉特·布兰多尔·贾拉提雅 (风花雪月) (圣火降魔录)", "image_path": "assets/output_10_fire_emblem__ingrid_brandl_galatea__fire_emblem_201.webp"}, {"prompt": "guilty_gear, sol badguy, guilty gear", "chinese_prompt": "索尔=巴得凯 (圣骑士之战)", "image_path": "assets/output_10_guilty_gear__sol_badguy__guilty_gear_202.webp"}, {"prompt": "accel world, blood leopard, accel world", "chinese_prompt": "黑羽早雪 黑雪姬 (加速世界)", "image_path": "assets/output_11_accel_world__blood_leopard__accel_world_0.jpeg"}, {"prompt": "acchi kocchi, mini<PERSON> tsumiki, acchi kocchi", "chinese_prompt": "御庭摘希 (一起一起这里那里)", "image_path": "assets/output_11_acchi_kocchi__miniwa_tsu<PERSON>ki__acchi_kocchi_1.jpeg"}, {"prompt": "adventure time, <PERSON><PERSON> the Vampire Queen, adventure time", "chinese_prompt": "吸血鬼女王艾薇尔 (探险活宝)", "image_path": "assets/output_11_adventure_time__<PERSON><PERSON>_the_Vampire_Queen__adventure_time_2.jpeg"}, {"prompt": "adventure time, <PERSON><PERSON>, adventure time", "chinese_prompt": "泡泡糖公主 (探险活宝)", "image_path": "assets/output_11_adventure_time__Princess_Bubblegum__adventure_time_3.jpeg"}, {"prompt": "aldnoah.zero, Asseylum Vers Allusia, aldnoah.zero", "chinese_prompt": "娅赛兰·沃斯·艾露西亚 (Aldnoah.Zero) ", "image_path": "assets/output_11_aldnoah_zero__<PERSON><PERSON><PERSON>_Vers_Allusia__aldnoah_zero_4.jpeg"}, {"prompt": "aldnoah.zero, <PERSON><PERSON>, aldnoah.zero", "chinese_prompt": "莱艾·阿里亚修 (Aldnoah.Zero) ", "image_path": "assets/output_11_aldnoah_zero__Rayet_<PERSON>h__aldnoah_zero_5.jpeg"}, {"prompt": "amagi brilliant park, Latifah Fleuranza, amagi brilliant park", "chinese_prompt": "拉媞珐·芙尔兰札 (甘城辉煌乐园救世主)", "image_path": "assets/output_11_amagi_brilliant_park__<PERSON><PERSON><PERSON><PERSON>_Fleuranza__amagi_brilliant_park_6.jpeg"}, {"prompt": "animal_crossing, ankha (animal crossing),animal crossing", "chinese_prompt": "艳后 Ankha (动物之森)", "image_path": "assets/output_11_animal_crossing__ankha__animal_crossing__animal_crossing_7.jpeg"}, {"prompt": "animal_crossing, audie (animal crossing), animal crossing", "chinese_prompt": "莫妮卡 (动物之森)", "image_path": "assets/output_11_animal_crossing__audie__animal_crossing___animal_crossing_8.jpeg"}, {"prompt": "animal_crossing, tom nook (animal crossing), animal crossing", "chinese_prompt": "狸克 (动物之森)", "image_path": "assets/output_11_animal_crossing__tom_nook__animal_crossing___animal_crossing_9.jpeg"}, {"prompt": "azur_lane, aegir (azur lane), azur lane", "chinese_prompt": "埃格妮丝 (碧蓝航线)", "image_path": "assets/output_11_azur_lane__aegir__azur_lane___azur_lane_10.png"}, {"prompt": "azur_lane, akashi (azur lane), azur lane", "chinese_prompt": "明<PERSON> (碧蓝航线)", "image_path": "assets/output_11_azur_lane__akashi__azur_lane___azur_lane_11.png"}, {"prompt": "azur_lane, aquila (azur lane), azur lane", "chinese_prompt": "天鹰 (碧蓝航线)", "image_path": "assets/output_11_azur_lane__aquila__azur_lane___azur_lane_12.png"}, {"prompt": "azur_lane, ark royal (azur lane), azur lane", "chinese_prompt": "皇家方舟 (碧蓝航线)", "image_path": "assets/output_11_azur_lane__ark_royal__azur_lane___azur_lane_13.png"}, {"prompt": "azur_lane, august von par<PERSON>, azur lane", "chinese_prompt": "奥古斯特·冯·帕塞瓦尔 (碧蓝航线)", "image_path": "assets/output_11_azur_lane__august_von_parseval__azur_lane_14.png"}, {"prompt": "azur_lane, avrora (azur lane), azur lane", "chinese_prompt": "曙光 (碧蓝航线)", "image_path": "assets/output_11_azur_lane__avrora__azur_lane___azur_lane_15.png"}, {"prompt": "azur_lane, bache (azur lane), azur lane", "chinese_prompt": "贝奇 (碧蓝航线)", "image_path": "assets/output_11_azur_lane__bache__azur_lane___azur_lane_16.png"}, {"prompt": "azur_lane, bismarck (azur lane), azur lane", "chinese_prompt": "俾斯麦 (碧蓝航线)", "image_path": "assets/output_11_azur_lane__bismarck__azur_lane___azur_lane_17.png"}, {"prompt": "azur_lane, centaur (azur lane), azur lane", "chinese_prompt": "半人马 (碧蓝航线)", "image_path": "assets/output_11_azur_lane__centaur__azur_lane___azur_lane_18.png"}, {"prompt": "azur_lane, cleveland (azur lane), azur lane", "chinese_prompt": "克利夫兰 (碧蓝航线)", "image_path": "assets/output_11_azur_lane__cleveland__azur_lane___azur_lane_19.png"}, {"prompt": "azur_lane, dunkerque (azur lane), azur lane", "chinese_prompt": "敦克尔克 (碧蓝航线)", "image_path": "assets/output_11_azur_lane__dunkerque__azur_lane___azur_lane_20.png"}, {"prompt": "azur_lane, fumizuki (azur lane), azur lane", "chinese_prompt": "文月 (碧蓝航线)", "image_path": "assets/output_11_azur_lane__fumizuki__azur_lane___azur_lane_21.png"}, {"prompt": "azur_lane, fusou (azur lane), azur lane", "chinese_prompt": "扶桑 (碧蓝航线)", "image_path": "assets/output_11_azur_lane__fusou__azur_lane___azur_lane_22.png"}, {"prompt": "azur_lane, i-19 (azur lane), azur lane", "chinese_prompt": "i-19 (碧蓝航线)", "image_path": "assets/output_11_azur_lane__i-19__azur_lane___azur_lane_23.png"}, {"prompt": "azur_lane, isokaze (azur lane), azur lane", "chinese_prompt": "矶风 (碧蓝航线)", "image_path": "assets/output_11_azur_lane__isokaze__azur_lane___azur_lane_24.png"}, {"prompt": "azur_lane, kawakaze (azur lane), azur lane", "chinese_prompt": "江风 (碧蓝航线)", "image_path": "assets/output_11_azur_lane__kawakaze__azur_lane___azur_lane_25.png"}, {"prompt": "azur_lane, kisaragi (azur lane), azur lane", "chinese_prompt": "如月 (碧蓝航线)", "image_path": "assets/output_11_azur_lane__kisaragi__azur_lane___azur_lane_26.png"}, {"prompt": "azur_lane, musashi (azur lane), azur lane", "chinese_prompt": "武藏 (碧蓝航线)", "image_path": "assets/output_11_azur_lane__musashi__azur_lane___azur_lane_27.png"}, {"prompt": "azur_lane, mutsuki (azur lane), azur lane", "chinese_prompt": "睦月 (碧蓝航线)", "image_path": "assets/output_11_azur_lane__mutsuki__azur_lane___azur_lane_28.png"}, {"prompt": "azur_lane, sims (azur lane), azur lane", "chinese_prompt": "西姆斯 (碧蓝航线)", "image_path": "assets/output_11_azur_lane__sims__azur_lane___azur_lane_29.png"}, {"prompt": "azur_lane, yukikaze (azur lane), azur lane", "chinese_prompt": "雪风 (碧蓝航线)", "image_path": "assets/output_11_azur_lane__yukikaze__azur_lane___azur_lane_30.png"}, {"prompt": "azur_lane, yu<PERSON>chi (azur lane), azur lane", "chinese_prompt": "夕立 (碧蓝航线)", "image_path": "assets/output_11_azur_lane__yuudachi__azur_lane___azur_lane_31.png"}, {"prompt": "genshin_impact, baizhu (genshin impact), genshin impact", "chinese_prompt": "白术 (原神)", "image_path": "assets/output_11_genshin_impact__baizhu__genshin_impact___genshin_impact_32.jpeg"}, {"prompt": "genshin_impact, bennett (genshin impact), genshin impact", "chinese_prompt": "班尼特 (原神)", "image_path": "assets/output_11_genshin_impact__bennett__genshin_impact___genshin_impact_33.jpeg"}, {"prompt": "genshin_impact, chevreuse (genshin impact), genshin impact", "chinese_prompt": "夏沃蕾 (原神)", "image_path": "assets/output_11_genshin_impact__chevreuse__genshin_impact___genshin_impact_34.jpeg"}, {"prompt": "genshin_impact, citlali (genshin impact), genshin impact", "chinese_prompt": "茜特菈莉 (原神)", "image_path": "assets/output_11_genshin_impact__citlali__genshin_impact___genshin_impact_35.jpeg"}, {"prompt": "genshin_impact, dori (genshin impact), genshin impact", "chinese_prompt": "多莉 (原神)", "image_path": "assets/output_11_genshin_impact__dori__genshin_impact___genshin_impact_36.jpeg"}, {"prompt": "genshin_impact, guoba (genshin impact), genshin impact", "chinese_prompt": "锅巴 (原神)", "image_path": "assets/output_11_genshin_impact__guoba__genshin_impact___genshin_impact_37.jpeg"}, {"prompt": "genshin_impact, kachina (genshin impact), genshin impact", "chinese_prompt": "卡齐娜 (原神)", "image_path": "assets/output_11_genshin_impact__kachina__genshin_impact___genshin_impact_38.jpeg"}, {"prompt": "genshin_impact, kinich (genshin impact), genshin impact", "chinese_prompt": "基尼奇 (原神)", "image_path": "assets/output_11_genshin_impact__kinich__genshin_impact___genshin_impact_39.png"}, {"prompt": "genshin_impact, mavuika (genshin impact), genshin impact", "chinese_prompt": "玛薇卡 (原神)", "image_path": "assets/output_11_genshin_impact__mavuika__genshin_impact___genshin_impact_40.jpeg"}, {"prompt": "genshin_impact, mualani (genshin impact), genshin impact", "chinese_prompt": "玛拉妮 (原神)", "image_path": "assets/output_11_genshin_impact__mualani__genshin_impact___genshin_impact_41.jpeg"}, {"prompt": "genshin_impact, ororon (genshin impact), genshin impact", "chinese_prompt": "欧洛伦 (原神)", "image_path": "assets/output_11_genshin_impact__ororon__genshin_impact___genshin_impact_42.jpeg"}, {"prompt": "genshin_impact, sigewinne (genshin impact), genshin impact", "chinese_prompt": "希格雯 (原神)", "image_path": "assets/output_11_genshin_impact__sigewinne__genshin_impact___genshin_impact_43.jpeg"}, {"prompt": "genshin_impact, xianyun (genshin impact), genshin impact", "chinese_prompt": "闲云 (原神)", "image_path": "assets/output_11_genshin_impact__xianyun__genshin_impact___genshin_impact_44.jpeg"}, {"prompt": "genshin_impact, xilonen (genshin impact), genshin impact", "chinese_prompt": "希诺宁 (原神)", "image_path": "assets/output_11_genshin_impact__xilonen__genshin_impact___genshin_impact_45.jpeg"}, {"prompt": "zenless_zone_zero, <PERSON><PERSON> (zzz), zenless zone zero", "chinese_prompt": "安比 (绝区零)", "image_path": "assets/output_11_zenless_zone_zero__Anby__zzz___zenless_zone_zero_46.jpeg"}, {"prompt": "zenless_zone_zero, <PERSON><PERSON> (zzz), zenless zone zero", "chinese_prompt": "耀嘉音 (绝区零)", "image_path": "assets/output_11_zenless_zone_zero__<PERSON><PERSON>_<PERSON>__zzz___zenless_zone_zero_47.jpeg"}, {"prompt": "zenless_zone_zero, <PERSON><PERSON> (zzz), zenless zone zero", "chinese_prompt": "柏妮思 (绝区零)", "image_path": "assets/output_11_zenless_zone_zero__Burnice__zzz___zenless_zone_zero_48.jpeg"}, {"prompt": "zenless_zone_zero, <PERSON><PERSON><PERSON> (zzz), zenless zone zero", "chinese_prompt": "珂蕾妲 (绝区零)", "image_path": "assets/output_11_zenless_zone_zero__<PERSON><PERSON>a__zzz___zenless_zone_zero_49.jpeg"}, {"prompt": "zenless_zone_zero, Lycaon (zzz), zenless zone zero", "chinese_prompt": "莱卡恩 (绝区零)", "image_path": "assets/output_11_zenless_zone_zero__Lycaon__zzz___zenless_zone_zero_50.jpeg"}, {"prompt": "zenless_zone_zero, <PERSON><PERSON> (zzz), zenless zone zero", "chinese_prompt": "青衣 (绝区零)", "image_path": "assets/output_11_zenless_zone_zero__<PERSON><PERSON>__zzz___zenless_zone_zero_51.jpeg"}, {"prompt": "honkai:_star_rail, Argenti (honkai: star rail), honkai: star rail", "chinese_prompt": "银枝 (崩坏: 星穹铁道)", "image_path": "assets/output_11_honkai__star_rail__Argenti__honkai__star_rail___honkai__star_rail_52.png"}, {"prompt": "honkai:_star_rail, As<PERSON> (honkai: star rail), honkai: star rail", "chinese_prompt": "艾丝妲 (崩坏: 星穹铁道)", "image_path": "assets/output_11_honkai__star_rail__Asta__honkai__star_rail___honkai__star_rail_53.png"}, {"prompt": "honkai:_star_rail, Bailu (honkai: star rail), honkai: star rail", "chinese_prompt": "白露 (崩坏: 星穹铁道)", "image_path": "assets/output_11_honkai__star_rail__Bailu__honkai__star_rail___honkai__star_rail_54.png"}, {"prompt": "honkai:_star_rail, <PERSON> (honkai: star rail), honkai: star rail", "chinese_prompt": "克拉拉 (崩坏: 星穹铁道)", "image_path": "assets/output_11_honkai__star_rail__Clara__honkai__star_rail___honkai__star_rail_55.png"}, {"prompt": "honkai:_star_rail, <PERSON><PERSON><PERSON><PERSON> (honkai: star rail), honkai: star rail", "chinese_prompt": "飞霄 (崩坏: 星穹铁道)", "image_path": "assets/output_11_honkai__star_rail__Fe<PERSON>iao__honkai__star_rail___honkai__star_rail_56.png"}, {"prompt": "honkai:_star_rail, <PERSON> (honkai: star rail), honkai: star rail", "chinese_prompt": "符玄 (崩坏: 星穹铁道)", "image_path": "assets/output_11_honkai__star_rail__<PERSON>_<PERSON><PERSON>__honkai__star_rail___honkai__star_rail_57.png"}, {"prompt": "honkai:_star_rail, Guinaifen (honkai: star rail), honkai: star rail", "chinese_prompt": "桂乃芬 (崩坏: 星穹铁道)", "image_path": "assets/output_11_honkai__star_rail__Guinaifen__honkai__star_rail___honkai__star_rail_58.png"}, {"prompt": "honkai:_star_rail, <PERSON><PERSON> (honkai: star rail), honkai: star rail", "chinese_prompt": "寒鸦 (崩坏: 星穹铁道)", "image_path": "assets/output_11_honkai__star_rail__<PERSON><PERSON>__honkai__star_rail___honkai__star_rail_59.png"}, {"prompt": "honkai:_star_rail, <PERSON><PERSON> (honkai: star rail), honkai: star rail", "chinese_prompt": "黑塔 (崩坏: 星穹铁道)", "image_path": "assets/output_11_honkai__star_rail__Herta__honkai__star_rail___honkai__star_rail_60.png"}, {"prompt": "honkai:_star_rail, <PERSON><PERSON><PERSON> (honkai: star rail), honkai: star rail", "chinese_prompt": "藿藿 (崩坏: 星穹铁道)", "image_path": "assets/output_11_honkai__star_rail__Hu<PERSON><PERSON>__honkai__star_rail___honkai__star_rail_61.png"}, {"prompt": "honkai:_star_rail, Jing <PERSON> (honkai: star rail), honkai: star rail", "chinese_prompt": "景元 (崩坏: 星穹铁道)", "image_path": "assets/output_11_honkai__star_rail__Jing_Yuan__honkai__star_rail___honkai__star_rail_62.png"}, {"prompt": "honkai:_star_rail, <PERSON><PERSON> (honkai: star rail), honkai: star rail", "chinese_prompt": "灵砂 (崩坏: 星穹铁道)", "image_path": "assets/output_11_honkai__star_rail__<PERSON>sha__honkai__star_rail___honkai__star_rail_63.png"}, {"prompt": "honkai:_star_rail, <PERSON> (honkai: star rail), honkai: star rail", "chinese_prompt": "娜塔莎 (崩坏: 星穹铁道)", "image_path": "assets/output_11_honkai__star_rail__Natasha__honkai__star_rail___honkai__star_rail_64.png"}, {"prompt": "honkai:_star_rail, <PERSON><PERSON> (honkai: star rail), honkai: star rail", "chinese_prompt": "佩拉 (崩坏: 星穹铁道)", "image_path": "assets/output_11_honkai__star_rail__Pela__honkai__star_rail___honkai__star_rail_65.png"}, {"prompt": "honkai:_star_rail, Qingque (honkai: star rail), honkai: star rail", "chinese_prompt": "青雀 (崩坏: 星穹铁道)", "image_path": "assets/output_11_honkai__star_rail__Qingque__honkai__star_rail___honkai__star_rail_66.png"}, {"prompt": "honkai:_star_rail, <PERSON><PERSON> (honkai: star rail), honkai: star rail", "chinese_prompt": "乱破 (崩坏: 星穹铁道)", "image_path": "assets/output_11_honkai__star_rail__Rappa__honkai__star_rail___honkai__star_rail_67.png"}, {"prompt": "honkai:_star_rail, <PERSON><PERSON> (honkai: star rail), honkai: star rail", "chinese_prompt": "阮梅 (崩坏: 星穹铁道)", "image_path": "assets/output_11_honkai__star_rail__R<PERSON>_<PERSON>__honkai__star_rail___honkai__star_rail_68.png"}, {"prompt": "honkai:_star_rail, <PERSON><PERSON>g (honkai: star rail), honkai: star rail", "chinese_prompt": "素裳 (崩坏: 星穹铁道)", "image_path": "assets/output_11_honkai__star_rail__Sushang__honkai__star_rail___honkai__star_rail_69.png"}, {"prompt": "honkai:_star_rail, <PERSON><PERSON><PERSON> (honkai: star rail), honkai: star rail", "chinese_prompt": "停云 (崩坏: 星穹铁道)", "image_path": "assets/output_11_honkai__star_rail__Tingyun__honkai__star_rail___honkai__star_rail_70.png"}, {"prompt": "honkai:_star_rail, <PERSON><PERSON><PERSON> (honkai: star rail), honkai: star rail", "chinese_prompt": "雪衣 (崩坏: 星穹铁道)", "image_path": "assets/output_11_honkai__star_rail__Xueyi__honkai__star_rail___honkai__star_rail_71.png"}, {"prompt": "honkai:_star_rail, Yukong (honkai: star rail), honkai: star rail", "chinese_prompt": "驭空 (崩坏: 星穹铁道)", "image_path": "assets/output_11_honkai__star_rail__Yukong__honkai__star_rail___honkai__star_rail_72.png"}, {"prompt": "honkai:_star_rail, <PERSON><PERSON> (honkai: star rail), honkai: star rail", "chinese_prompt": "云璃 (崩坏: 星穹铁道)", "image_path": "assets/output_11_honkai__star_rail__Yunli__honkai__star_rail___honkai__star_rail_73.png"}, {"prompt": "goddess_of_victory:_nikke, <PERSON> (nikke), goddess of victory: nikke", "chinese_prompt": "爱丽丝 (胜利女神：妮姬)", "image_path": "assets/output_11_goddess_of_victory__nikke__Alice__nikke___goddess_of_victory__nikke_74.png"}, {"prompt": "goddess_of_victory:_nikke, <PERSON> (bunny) (nikke), goddess of victory: nikke", "chinese_prompt": "布兰儿 (胜利女神：妮姬)", "image_path": "assets/output_11_goddess_of_victory__nikke__<PERSON>__bunny___nikke___goddess_of_victory__nikke_75.png"}, {"prompt": "goddess_of_victory:_nikke, <PERSON> (nikke), goddess of victory: nikke", "chinese_prompt": "桃乐丝 (胜利女神：妮姬)", "image_path": "assets/output_11_goddess_of_victory__nikke__Dorothy__nikke___goddess_of_victory__nikke_76.png"}, {"prompt": "goddess_of_victory:_nikke, <PERSON><PERSON><PERSON> (nikke), goddess of victory: nikke", "chinese_prompt": "伊莱格 (胜利女神：妮姬)", "image_path": "assets/output_11_goddess_of_victory__nikke__Elegg__nikke___goddess_of_victory__nikke_77.png"}, {"prompt": "goddess_of_victory:_nikke, <PERSON><PERSON> (nikke), goddess of victory: nikke", "chinese_prompt": "艾可希雅 (胜利女神：妮姬)", "image_path": "assets/output_11_goddess_of_victory__nikke__Exia__nikke___goddess_of_victory__nikke_78.png"}, {"prompt": "goddess_of_victory:_nikke, <PERSON><PERSON> (nikke), goddess of victory: nikke", "chinese_prompt": "富克旺 (胜利女神：妮姬)", "image_path": "assets/output_11_goddess_of_victory__nikke__Folkwang__nikke___goddess_of_victory__nikke_79.png"}, {"prompt": "goddess_of_victory:_nikke, <PERSON><PERSON> (nikke), goddess of victory: nikke", "chinese_prompt": "海伦 (胜利女神：妮姬)", "image_path": "assets/output_11_goddess_of_victory__nikke__Helm__nikke___goddess_of_victory__nikke_80.png"}, {"prompt": "goddess_of_victory:_nikke, <PERSON><PERSON> (nikke), goddess of victory: nikke", "chinese_prompt": "拉普拉斯 (胜利女神：妮姬)", "image_path": "assets/output_11_goddess_of_victory__nikke__La<PERSON>__nikke___goddess_of_victory__nikke_81.png"}, {"prompt": "goddess_of_victory:_nikke, <PERSON><PERSON><PERSON><PERSON> (nikke), goddess of victory: nikke", "chinese_prompt": "鲁德米拉 (胜利女神：妮姬)", "image_path": "assets/output_11_goddess_of_victory__nikke__<PERSON><PERSON><PERSON><PERSON>__nikke___goddess_of_victory__nikke_82.png"}, {"prompt": "goddess_of_victory:_nikke, Maiden (nikke), goddess of victory: nikke", "chinese_prompt": "梅登 (胜利女神：妮姬)", "image_path": "assets/output_11_goddess_of_victory__nikke__Maiden__nikke___goddess_of_victory__nikke_83.png"}, {"prompt": "goddess_of_victory:_nikke, <PERSON><PERSON> (nikke), goddess of victory: nikke", "chinese_prompt": "玛律恰那 (胜利女神：妮姬)", "image_path": "assets/output_11_goddess_of_victory__nikke__<PERSON><PERSON>__nikke___goddess_of_victory__nikke_84.png"}, {"prompt": "goddess_of_victory:_nikke, Ma<PERSON> (nikke), goddess of victory: nikke", "chinese_prompt": "马斯特 (胜利女神：妮姬)", "image_path": "assets/output_11_goddess_of_victory__nikke__Mast__nikke___goddess_of_victory__nikke_85.png"}, {"prompt": "goddess_of_victory:_nikke, <PERSON> (nikke), goddess of victory: nikke", "chinese_prompt": "麦斯威尔 (胜利女神：妮姬)", "image_path": "assets/output_11_goddess_of_victory__nikke__<PERSON>__nikke___goddess_of_victory__nikke_86.png"}, {"prompt": "goddess_of_victory:_nikke, <PERSON><PERSON> (nikke), goddess of victory: nikke", "chinese_prompt": "米哈拉 (胜利女神：妮姬)", "image_path": "assets/output_11_goddess_of_victory__nikke__<PERSON><PERSON>__nikke___goddess_of_victory__nikke_87.png"}, {"prompt": "goddess_of_victory:_nikke, <PERSON><PERSON> (nikke), goddess of victory: nikke", "chinese_prompt": "神罚 (胜利女神：妮姬)", "image_path": "assets/output_11_goddess_of_victory__nikke__Modernia__nikke___goddess_of_victory__nikke_88.png"}, {"prompt": "goddess_of_victory:_nikke, <PERSON><PERSON><PERSON> (nikke), goddess of victory: nikke", "chinese_prompt": "尼希利斯塔 (胜利女神：妮姬)", "image_path": "assets/output_11_goddess_of_victory__nikke__<PERSON><PERSON><PERSON>__nikke___goddess_of_victory__nikke_89.png"}, {"prompt": "goddess_of_victory:_nikke, <PERSON><PERSON><PERSON><PERSON> (nikke), goddess of victory: nikke", "chinese_prompt": "普丽瓦蒂 (胜利女神：妮姬)", "image_path": "assets/output_11_goddess_of_victory__nikke__Privaty__nikke___goddess_of_victory__nikke_90.png"}, {"prompt": "goddess_of_victory:_nikke, <PERSON><PERSON> (nikke), goddess of victory: nikke", "chinese_prompt": "拉毗 (胜利女神：妮姬)", "image_path": "assets/output_11_goddess_of_victory__nikke__Rapi__nikke___goddess_of_victory__nikke_91.png"}, {"prompt": "goddess_of_victory:_nikke, <PERSON> (nikke), goddess of victory: nikke", "chinese_prompt": "小红帽 (胜利女神：妮姬)", "image_path": "assets/output_11_goddess_of_victory__nikke__<PERSON>_<PERSON>__nikke___goddess_of_victory__nikke_92.png"}, {"prompt": "goddess_of_victory:_nikke, Rupee (nikke), goddess of victory: nikke", "chinese_prompt": "露菲 (胜利女神：妮姬)", "image_path": "assets/output_11_goddess_of_victory__nikke__Rupee__nikke___goddess_of_victory__nikke_93.png"}, {"prompt": "goddess_of_victory:_nikke, <PERSON><PERSON> (nikke), goddess of victory: nikke", "chinese_prompt": "索达 (胜利女神：妮姬)", "image_path": "assets/output_11_goddess_of_victory__nikke__Soda__nikke___goddess_of_victory__nikke_94.png"}, {"prompt": "goddess_of_victory:_nikke, <PERSON><PERSON> (nikke), goddess of victory: nikke", "chinese_prompt": "托比 (胜利女神：妮姬)", "image_path": "assets/output_11_goddess_of_victory__nikke__Tove__nikke___goddess_of_victory__nikke_95.png"}, {"prompt": "goddess_of_victory:_nikke, <PERSON> (nikke), goddess of victory: nikke", "chinese_prompt": "毒蛇 (胜利女神：妮姬)", "image_path": "assets/output_11_goddess_of_victory__nikke__Viper__nikke___goddess_of_victory__nikke_96.png"}, {"prompt": "kantai_collection, northern ocean princess, kantai collection", "chinese_prompt": "北方栖姬 (舰队收藏)", "image_path": "assets/output_2_kantai_collection__northern_ocean_princess__kantai_collection_0.webp"}, {"prompt": "love_live!, ayase eli, love live!", "chinese_prompt": "绚濑绘里 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_2_love_live___ayase_eli__love_live__1.webp"}, {"prompt": "touhou, hata no kokoro, touhou", "chinese_prompt": "秦心 (东方)", "image_path": "assets/output_2_touhou__hata_no_kokoro__touhou_2.webp"}, {"prompt": "fate_(series), tamamo no mae (fate/extra), fate (series)", "chinese_prompt": "玉藻前 (Fate/Extra), (Fate)", "image_path": "assets/output_2_fate__series___tamamo_no_mae__fate_extra___fate__series__3.webp"}, {"prompt": "touhou, nagae iku, touhou", "chinese_prompt": "永江衣玖 (东方)", "image_path": "assets/output_2_touhou__nagae_iku__touhou_4.webp"}, {"prompt": "kantai_collection, akebono (kancolle), kantai collection", "chinese_prompt": "曙 (舰队收藏)", "image_path": "assets/output_2_kantai_collection__a<PERSON><PERSON><PERSON>__kancolle___kantai_collection_5.webp"}, {"prompt": "touhou, i<PERSON>ki kasen, touhou", "chinese_prompt": "茨木华扇 (东方)", "image_path": "assets/output_2_touh<PERSON>__i<PERSON><PERSON>_kasen__touhou_6.webp"}, {"prompt": "genshin_impact, venti (genshin impact), genshin impact", "chinese_prompt": "温迪 (原神)", "image_path": "assets/output_2_genshin_impact__venti__genshin_impact___genshin_impact_7.webp"}, {"prompt": "hololive, sakura miko, hololive", "chinese_prompt": "樱巫女 (Hololive)", "image_path": "assets/output_2_hololive__sakura_miko__hololive_8.webp"}, {"prompt": "pokemon, rosa (pokemon), pokemon", "chinese_prompt": "鸣依 宝可梦", "image_path": "assets/output_2_pokemon__rosa__pokemon___pokemon_9.webp"}, {"prompt": "fate_(series), shuten douji (fate), fate (series)", "chinese_prompt": "酒吞童子 (Fate)", "image_path": "assets/output_2_fate__series___shuten_douji__fate___fate__series__10.webp"}, {"prompt": "hololive, watson amelia, hololive", "chinese_prompt": "华生·艾米莉亚 (Hololive)", "image_path": "assets/output_2_hololive__watson_amelia__hololive_11.webp"}, {"prompt": "jojo_no_kimyou_na_bouken, j<PERSON><PERSON> (young), jojo no kimyou na bouken", "chinese_prompt": "乔瑟夫·乔斯达 (年轻) (二乔) (JOJO的奇妙冒险)", "image_path": "assets/output_2_jojo_no_kimyou_na_bouken__joseph_joestar__young___jojo_no_kimyou_na_bouken_12.webp"}, {"prompt": "fate_(series), astolf<PERSON> (fate), fate (series)", "chinese_prompt": "阿斯托尔福 (Fate)", "image_path": "assets/output_2_fate__series___astolfo__fate___fate__series__13.webp"}, {"prompt": "blue_archive, aris (blue archive), blue archive", "chinese_prompt": "天童爱丽丝 (蔚蓝档案)", "image_path": "assets/output_2_blue_archive__aris__blue_archive___blue_archive_14.webp"}, {"prompt": "touhou, soga no tojiko, touhou", "chinese_prompt": "苏我屠自古 (东方)", "image_path": "assets/output_2_touhou__soga_no_tojiko__touhou_15.webp"}, {"prompt": "xenoblade_chronicles_(series), mythra (xenoblade), xenoblade chronicles (series)", "chinese_prompt": "光 (异度神剑)", "image_path": "assets/output_2_xenoblade_chronicles__series___mythra__xenoblade___xenoblade_chronicles__series__16.webp"}, {"prompt": "pokemon, marnie (pokemon), pokemon", "chinese_prompt": "玛俐 (宝可梦)", "image_path": "assets/output_2_pokemon__marnie__pokemon___pokemon_17.webp"}, {"prompt": "pokemon, hilda (pokemon), pokemon", "chinese_prompt": "斗子 (宝可梦)", "image_path": "assets/output_2_pokemon__hilda__pokemon___pokemon_18.webp"}, {"prompt": "dangan<PERSON><PERSON>_(series), <PERSON><PERSON> k<PERSON>, dangan<PERSON><PERSON> (series)", "chinese_prompt": "王马小吉 (弹丸论破)", "image_path": "assets/output_2_danganronpa__series___oma_kokichi__danganronpa__series__19.webp"}, {"prompt": "mario_(series), princess peach, mario (series)", "chinese_prompt": "碧姬公主 (超级玛利欧)", "image_path": "assets/output_2_mario__series___princess_peach__mario__series__20.webp"}, {"prompt": "blue_archive, koharu (blue archive), blue archive", "chinese_prompt": "下江小春 (蔚蓝档案)", "image_path": "assets/output_2_blue_archive__koharu__blue_archive___blue_archive_21.webp"}, {"prompt": "fate_(series), gilgamesh (fate), fate (series)", "chinese_prompt": "吉尔伽美什 (Fate)", "image_path": "assets/output_2_fate__series___gilgamesh__fate___fate__series__22.webp"}, {"prompt": "kantai_collection, kitakami (kancolle), kantai collection", "chinese_prompt": "北上 (舰队收藏)", "image_path": "assets/output_2_kantai_collection__kitakami__kancolle___kantai_collection_23.webp"}, {"prompt": "arknights, texas (arknights), arknights", "chinese_prompt": "德克萨斯 (明日方舟)", "image_path": "assets/output_2_arknights__texas__arknights___arknights_24.webp"}, {"prompt": "genshin_impact, kamisato ayaka, genshin impact", "chinese_prompt": "神里绫华 (原神)", "image_path": "assets/output_2_genshin_impact__kamisato_ayaka__genshin_impact_25.webp"}, {"prompt": "fate_(series), matou sakura, fate (series)", "chinese_prompt": "间桐樱 (Fate)", "image_path": "assets/output_2_fate__series___matou_sakura__fate__series__26.webp"}, {"prompt": "touh<PERSON>, miyako yoshika, touhou", "chinese_prompt": "宫古芳香 (东方)", "image_path": "assets/output_2_touh<PERSON>__miya<PERSON>_yoshika__touhou_27.webp"}, {"prompt": "touh<PERSON>, kuro<PERSON>i yamame, touhou", "chinese_prompt": "黑谷山女 (东方)", "image_path": "assets/output_2_touh<PERSON>__k<PERSON><PERSON><PERSON>_yamame__touhou_28.webp"}, {"prompt": "touh<PERSON>, ka<PERSON><PERSON>i kyo<PERSON>, touhou", "chinese_prompt": "幽谷响子 (东方)", "image_path": "assets/output_2_touh<PERSON>__ka<PERSON><PERSON><PERSON>_kyouko__touhou_29.webp"}, {"prompt": "metroid, samus aran, metroid", "chinese_prompt": "萨姆斯·亚兰 (银河战士)", "image_path": "assets/output_2_metroid__samus_aran__metroid_30.webp"}, {"prompt": "umamusume, agnes tachy<PERSON> (umamusume), umamusume", "chinese_prompt": "爱丽速子 (赛马娘)", "image_path": "assets/output_2_umamusume__agnes_tachyon__umamusume___umamusume_31.webp"}, {"prompt": "kantai_collection, ooyodo (kancolle), kantai collection", "chinese_prompt": "大淀 (舰队收藏)", "image_path": "assets/output_2_kantai_collection__ooyodo__kancolle___kantai_collection_32.webp"}, {"prompt": "touh<PERSON>, kumoi i<PERSON>, touhou", "chinese_prompt": "云居一轮 (东方)", "image_path": "assets/output_2_touh<PERSON>__kum<PERSON>_i<PERSON><PERSON>__touhou_33.webp"}, {"prompt": "girls_und_panzer, <PERSON><PERSON><PERSON><PERSON> (girls und panzer), girls und panzer", "chinese_prompt": "大吉岭 (少女与战车)", "image_path": "assets/output_2_girls_und_panzer__da<PERSON><PERSON>ling__girls_und_panzer___girls_und_panzer_34.webp"}, {"prompt": "genshin_impact, paimon (genshin impact), genshin impact", "chinese_prompt": "派蒙 (原神)", "image_path": "assets/output_2_genshin_impact__paimon__genshin_impact___genshin_impact_35.webp"}, {"prompt": "genshin_impact, klee (genshin impact), genshin impact", "chinese_prompt": "可莉 (原神)", "image_path": "assets/output_2_genshin_impact__klee__genshin_impact___genshin_impact_36.webp"}, {"prompt": "kantai_collection, rensouhou-chan, kantai collection", "chinese_prompt": "连装砲酱 (舰队收藏)", "image_path": "assets/output_2_kantai_collection__renso<PERSON><PERSON>-chan__kantai_collection_37.webp"}, {"prompt": "touhou, clownpiece, touhou", "chinese_prompt": "克劳恩皮丝 (东方)", "image_path": "assets/output_2_touhou__clownpiece__touhou_38.webp"}, {"prompt": "umamusume, rice shower (umamusume), umamusume", "chinese_prompt": "米浴 (赛马娘)", "image_path": "assets/output_2_umamusume__rice_shower__umamusume___umamusume_39.webp"}, {"prompt": "genshin_impact, tartaglia (genshin impact), genshin impact", "chinese_prompt": "达达利亚 (原神)", "image_path": "assets/output_2_genshin_impact__tartaglia__genshin_impact___genshin_impact_40.webp"}, {"prompt": "idolmaster, ho<PERSON>i miki, idolmaster", "chinese_prompt": "星井美希 (偶像大师)", "image_path": "assets/output_2_idolmaster__ho<PERSON><PERSON>_miki__idolmaster_41.webp"}, {"prompt": "danga<PERSON><PERSON><PERSON>_(series), k<PERSON><PERSON> na<PERSON>, dangan<PERSON><PERSON> (series)", "chinese_prompt": "狛枝凪斗 (弹丸论破)", "image_path": "assets/output_2_danganronpa__series___komaeda_nagito__danganronpa__series__42.webp"}, {"prompt": "one_piece, nami (one piece), one piece", "chinese_prompt": "娜美 (海贼王)", "image_path": "assets/output_2_one_piece__nami__one_piece___one_piece_43.webp"}, {"prompt": "lycoris_recoil, nishi<PERSON><PERSON> chisato, lycoris recoil", "chinese_prompt": "锦木千束 (莉可丽丝)", "image_path": "assets/output_2_lycoris_recoil__nishiki<PERSON>_chisato__lycoris_recoil_44.webp"}, {"prompt": "lucky_star, hi<PERSON><PERSON> kagami, lucky star", "chinese_prompt": "柊镜 (幸运星)", "image_path": "assets/output_2_lucky_star__hi<PERSON><PERSON>_kagami__lucky_star_45.webp"}, {"prompt": "touhou, kijin seija, touhou", "chinese_prompt": "鬼人正邪 (东方)", "image_path": "assets/output_2_touh<PERSON>__kijin_seija__touhou_46.webp"}, {"prompt": "fate_(series), cu chulainn (fate), fate (series)", "chinese_prompt": "库丘林 (Fate)", "image_path": "assets/output_2_fate__series___cu_chulainn__fate___fate__series__47.webp"}, {"prompt": "kantai_collection, prinz eugen (kancolle), kantai collection", "chinese_prompt": "尤金亲王 (舰队收藏)", "image_path": "assets/output_2_kantai_collection__prinz_eugen__kancolle___kantai_collection_48.webp"}, {"prompt": "fate_(series), mordred (fate), fate (series)", "chinese_prompt": "莫德雷德 (Fate)", "image_path": "assets/output_2_fate__series___mordred__fate___fate__series__49.webp"}, {"prompt": "bocchi_the_rock!, kita ikuyo, bocchi the rock!", "chinese_prompt": "喜多郁代 (孤独摇滚)", "image_path": "assets/output_2_bocchi_the_rock___kita_ikuyo__bocchi_the_rock__50.webp"}, {"prompt": "kantai_collection, as<PERSON>o (kancolle), kantai collection", "chinese_prompt": "朝潮 (舰队收藏)", "image_path": "assets/output_2_kantai_collection__asashio__kancolle___kantai_collection_51.webp"}, {"prompt": "umamusume, mejiro mc<PERSON> (umamusume), umamusume", "chinese_prompt": "目白麦昆 (赛马娘)", "image_path": "assets/output_2_umamusume__mejiro_mcqueen__umamusume___umamusume_52.webp"}, {"prompt": "love_live!, watanabe you, love live!", "chinese_prompt": "渡边曜 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_2_love_live___wa<PERSON>be_you__love_live__53.webp"}, {"prompt": "girls_und_panzer, ni<PERSON><PERSON><PERSON> maho, girls und panzer", "chinese_prompt": "西住真穗 (少女与战车)", "image_path": "assets/output_2_girls_und_panzer__ni<PERSON><PERSON><PERSON>_maho__girls_und_panzer_54.webp"}, {"prompt": "fate_(series), minamoto no raikou (fate), fate (series)", "chinese_prompt": "源赖光 (Fate)", "image_path": "assets/output_2_fate__series___minamoto_no_raikou__fate___fate__series__55.webp"}, {"prompt": "danga<PERSON><PERSON><PERSON>_(series), sa<PERSON><PERSON> s<PERSON>, dangan<PERSON><PERSON> (series)", "chinese_prompt": "最原终一 (弹丸论破)", "image_path": "assets/output_2_danganronpa__series___saihara_shuichi__danganronpa__series__56.webp"}, {"prompt": "touhou, sekibanki, touhou", "chinese_prompt": "赤蛮奇 (东方)", "image_path": "assets/output_2_touh<PERSON>__sekibanki__touhou_57.webp"}, {"prompt": "kantai_collection, mutsu (kancolle), kantai collection", "chinese_prompt": "陆奥 (舰队收藏)", "image_path": "assets/output_2_kantai_collection__mutsu__kancolle___kantai_collection_58.webp"}, {"prompt": "umamusume, daiwa scarlet (umamusume), umamusume", "chinese_prompt": "大和赤骥 (赛马娘)", "image_path": "assets/output_2_umamusume__daiwa_scarlet__umamusume___umamusume_59.webp"}, {"prompt": "k-on!, koto<PERSON><PERSON> tsumugi, k-on!", "chinese_prompt": "琴吹䌷 (K-ON! 轻音部)", "image_path": "assets/output_2_k-on___kotobuki_tsumugi__k-on__60.webp"}, {"prompt": "hololive, shirogane noel, hololive", "chinese_prompt": "白银诺艾尔 (Hololive)", "image_path": "assets/output_2_hololive__shirogane_noel__hololive_61.webp"}, {"prompt": "vocaloid, meiko (vocaloid), vocaloid", "chinese_prompt": "MEIKO (Vocaloid)", "image_path": "assets/output_2_vocaloid__meiko__vocaloid___vocaloid_62.webp"}, {"prompt": "kemono_friends, kaban (kemono friends), kemono friends", "chinese_prompt": "小背包 (动物朋友)", "image_path": "assets/output_2_kemono_friends__kaban__kemono_friends___kemono_friends_63.webp"}, {"prompt": "genshin_impact, eula (genshin impact), genshin impact", "chinese_prompt": "优菈 (原神)", "image_path": "assets/output_2_genshin_impact__eula__genshin_impact___genshin_impact_64.webp"}, {"prompt": "fate_(series), emiya shirou, fate (series)", "chinese_prompt": "卫宫士郎 (Fate)", "image_path": "assets/output_2_fate__series___emiya_shirou__fate__series__65.webp"}, {"prompt": "girls_und_panzer, its<PERSON> erika, girls und panzer", "chinese_prompt": "逸见艾丽卡 (少女与战车)", "image_path": "assets/output_2_girls_und_panzer__itsumi_erika__girls_und_panzer_66.webp"}, {"prompt": "blue_archive, karin (blue archive), blue archive", "chinese_prompt": "角楯花凛 (蔚蓝档案)", "image_path": "assets/output_2_blue_archive__karin__blue_archive___blue_archive_67.webp"}, {"prompt": "honkai_(series), stelle (honkai: star rail), honkai (series)", "chinese_prompt": "星 (崩坏: 星穹铁道) (崩坏)", "image_path": "assets/output_2_honkai__series___stelle__honkai__star_rail___honkai__series__68.webp"}, {"prompt": "touhou, junko (touhou), touhou", "chinese_prompt": "纯狐 (东方)", "image_path": "assets/output_2_touhou__junko__touhou___touhou_69.webp"}, {"prompt": "genshin_impact, shenhe (genshin impact), genshin impact", "chinese_prompt": "申鹤 (原神)", "image_path": "assets/output_2_genshin_impact__shenhe__genshin_impact___genshin_impact_70.webp"}, {"prompt": "hololive, inugami korone, hololive", "chinese_prompt": "戌神沁音 (Hololive)", "image_path": "assets/output_2_hololive__inugami_korone__hololive_71.webp"}, {"prompt": "touhou, aki <PERSON><PERSON>, touhou", "chinese_prompt": "秋穰子 (东方)", "image_path": "assets/output_2_touh<PERSON>__aki_minor<PERSON>__touhou_72.webp"}, {"prompt": "jojo_no_kimyou_na_bouken, dio brando, jojo no kimyou na bouken", "chinese_prompt": "迪奥·布朗度 (JOJO的奇妙冒险)", "image_path": "assets/output_2_jojo_no_kimyou_na_bouken__dio_brando__jojo_no_kimyou_na_bouken_73.webp"}, {"prompt": "umamusume, gold ship (umamusume), umamusume", "chinese_prompt": "黄金船 (赛马娘)", "image_path": "assets/output_2_umamusume__gold_ship__umamusume___umamusume_74.webp"}, {"prompt": "hololive, oozora subaru, hololive", "chinese_prompt": "大空昴 (Hololive)", "image_path": "assets/output_2_hololive__oozora_subaru__hololive_75.webp"}, {"prompt": "gundam, suletta mercury, gundam", "chinese_prompt": "苏雷塔·墨丘利 (水星魔女) (钢弹)", "image_path": "assets/output_2_gundam__suletta_mercury__gundam_76.webp"}, {"prompt": "kantai_collection, tatsuta (kancolle), kantai collection", "chinese_prompt": "龙田 (舰队收藏)", "image_path": "assets/output_2_kantai_collection__tatsuta__kancolle___kantai_collection_77.webp"}, {"prompt": "touh<PERSON>, hieda no akyuu, touhou", "chinese_prompt": "稗田阿求 (东方)", "image_path": "assets/output_2_touhou__hieda_no_akyuu__touhou_78.webp"}, {"prompt": "kantai_collection, female admiral (kancolle), kantai collection", "chinese_prompt": "女性提督 (舰队收藏)", "image_path": "assets/output_2_kantai_collection__female_admiral__ka<PERSON><PERSON>___kantai_collection_79.webp"}, {"prompt": "lucky_star, i<PERSON><PERSON> k<PERSON>, lucky star", "chinese_prompt": "泉此方 (幸运星)", "image_path": "assets/output_2_lucky_star__i<PERSON><PERSON>_konata__lucky_star_80.webp"}, {"prompt": "hololive, amane kanata, hololive", "chinese_prompt": "天音彼方 (Hololive)", "image_path": "assets/output_2_hololive__amane_kanata__hololive_81.webp"}, {"prompt": "hololive, ookami mio, hololive", "chinese_prompt": "大神澪 (Hololive)", "image_path": "assets/output_2_hololive__o<PERSON><PERSON>_mio__hololive_82.webp"}, {"prompt": "utau, kasane teto, utau", "chinese_prompt": "重音Teto UTAU", "image_path": "assets/output_2_utau__kasane_teto__utau_83.webp"}, {"prompt": "pokemon, gloria (pokemon), pokemon", "chinese_prompt": "小优 (宝可梦)", "image_path": "assets/output_2_pokemon__gloria__pokemon___pokemon_84.webp"}, {"prompt": "love_live!, minami kotori, love live!", "chinese_prompt": "南小鸟 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_2_love_live___minami_kotori__love_live__85.webp"}, {"prompt": "code_geass, c.c., code geass", "chinese_prompt": "C.C. (Code Geass)", "image_path": "assets/output_2_code_geass__c_c___code_geass_86.webp"}, {"prompt": "bishoujo_senshi_sailor_moon, tsu<PERSON><PERSON> usagi, bishoujo senshi sailor moon", "chinese_prompt": "月野兔 水手月亮 (美少女战士)", "image_path": "assets/output_2_bishoujo_senshi_sailor_moon__tsukino_usagi__bishoujo_senshi_sailor_moon_87.webp"}, {"prompt": "blue_archive, asuna (bunny) (blue archive), blue archive", "chinese_prompt": "一之濑明日奈 (兔子装) (蔚蓝档案)", "image_path": "assets/output_2_blue_archive__asuna__bunny___blue_archive___blue_archive_88.webp"}, {"prompt": "blue_archive, kisaki (blue archive), blue archive", "chinese_prompt": "龙华妃咲 (蔚蓝档案)", "image_path": "assets/output_2_blue_archive__kisaki__blue_archive___blue_archive_89.webp"}, {"prompt": "onii-chan_wa_oshimai!, o<PERSON> mahiro, onii-chan wa oshimai!", "chinese_prompt": "绪山真寻 (不当哥哥了)", "image_path": "assets/output_2_onii-chan_wa_o<PERSON>i___oyama_mahiro__onii-chan_wa_oshimai__90.webp"}, {"prompt": "fire_emblem, byleth (female) (fire emblem), fire emblem", "chinese_prompt": "贝雷丝 (女性) (风花雪月) (圣火降魔录)", "image_path": "assets/output_2_fire_emblem__byleth__female___fire_emblem___fire_emblem_91.webp"}, {"prompt": "hololive, sakamata chloe, hololive", "chinese_prompt": "沙花叉克萝伊 (Hololive)", "image_path": "assets/output_2_hololive__sakamata_chloe__hololive_92.webp"}, {"prompt": "fate_(series), archer (fate), fate (series)", "chinese_prompt": "archer 英灵卫宫 命运系列", "image_path": "assets/output_2_fate__series___archer__fate___fate__series__93.webp"}, {"prompt": "girls_und_panzer, anchovy (girls und panzer), girls und panzer", "chinese_prompt": "安斋千代美 (少女与战车)", "image_path": "assets/output_2_girls_und_panzer__anchovy__girls_und_panzer___girls_und_panzer_94.webp"}, {"prompt": "blue_archive, mari (blue archive), blue archive", "chinese_prompt": "伊落玛丽 (蔚蓝档案)", "image_path": "assets/output_2_blue_archive__mari__blue_archive___blue_archive_95.webp"}, {"prompt": "danga<PERSON><PERSON><PERSON>_(series), hi<PERSON>a haji<PERSON>, dangan<PERSON><PERSON> (series)", "chinese_prompt": "日向创 (弹丸论破)", "image_path": "assets/output_2_danganronpa__series___hinata_hajime__danganronpa__series__96.webp"}, {"prompt": "kantai_collection, musashi (kancolle), kantai collection", "chinese_prompt": "武藏 (舰队收藏)", "image_path": "assets/output_2_kantai_collection__musashi__kancolle___kantai_collection_97.webp"}, {"prompt": "arknights, skadi (arknights), arknights", "chinese_prompt": "斯卡哈 (明日方舟)", "image_path": "assets/output_2_arknights__skadi__arknights___arknights_98.webp"}, {"prompt": "vampire_(game), morrigan a<PERSON>, vampire (game)", "chinese_prompt": "莫莉卡·安斯兰特 (魔域幽灵)", "image_path": "assets/output_2_vampire__game___morrigan_<PERSON><PERSON>land__vampire__game__99.webp"}, {"prompt": "kantai_collection, sendai (kancolle), kantai collection", "chinese_prompt": "川内 (舰队收藏)", "image_path": "assets/output_2_kantai_collection__sendai__kancolle___kantai_collection_100.webp"}, {"prompt": "kantai_collection, bismarck (kancolle), kantai collection", "chinese_prompt": "俾斯麦 (舰队收藏)", "image_path": "assets/output_2_kantai_collection__bisma<PERSON>k__kancolle___kantai_collection_101.webp"}, {"prompt": "kantai_collection, ooi (kancolle), kantai collection", "chinese_prompt": "大井 (舰队收藏)", "image_path": "assets/output_2_kantai_collection__ooi__kancolle___kantai_collection_102.webp"}, {"prompt": "girls'_frontline, hk416 (girls' frontline), girls' frontline", "chinese_prompt": "HK416 (少女前线)", "image_path": "assets/output_2_girls__frontline__hk416__girls__frontline___girls__frontline_103.webp"}, {"prompt": "kantai_collection, takao (kancolle), kantai collection", "chinese_prompt": "高雄 (舰队收藏)", "image_path": "assets/output_2_kantai_collection__takao__kancolle___kantai_collection_104.webp"}, {"prompt": "kantai_collection, akashi (kancolle), kantai collection", "chinese_prompt": "明<PERSON> (舰队收藏)", "image_path": "assets/output_2_kantai_collection__akashi__kancolle___kantai_collection_105.webp"}, {"prompt": "blue_archive, rio (blue archive), blue archive", "chinese_prompt": "调月莉央 (蔚蓝档案)", "image_path": "assets/output_2_blue_archive__rio__blue_archive___blue_archive_106.webp"}, {"prompt": "sword_art_online, asuna (sao), sword art online", "chinese_prompt": "亚丝娜 (SAO) (刀剑神域)", "image_path": "assets/output_2_sword_art_online__asuna__sao___sword_art_online_107.webp"}, {"prompt": "blue_archive, kazusa (blue archive), blue archive", "chinese_prompt": "杏山千纱 (蔚蓝档案)", "image_path": "assets/output_2_blue_archive__ka<PERSON><PERSON>__blue_archive___blue_archive_108.webp"}, {"prompt": "honkai_(series), kafka (honkai: star rail), honkai (series)", "chinese_prompt": "卡夫卡 (崩坏：星穹铁道) (崩坏)", "image_path": "assets/output_2_honkai__series___kafka__honkai__star_rail___honkai__series__109.webp"}, {"prompt": "genshin_impact, xiao (genshin impact), genshin impact", "chinese_prompt": "魈 (原神)", "image_path": "assets/output_2_genshin_impact__xiao__genshin_impact___genshin_impact_110.webp"}, {"prompt": "jojo_no_kimyou_na_bouken, caesar anthonio zep<PERSON>, jojo no kimyou na bouken", "chinese_prompt": "西撒·安东尼奥·齐贝林 (JOJO的奇妙冒险)", "image_path": "assets/output_2_jojo_no_kimyou_na_bouken__caesar_anthonio_zeppeli__jojo_no_kimyou_na_bouken_111.webp"}, {"prompt": "fate_(series), medusa (fate), fate (series)", "chinese_prompt": "美杜莎 (Fate)", "image_path": "assets/output_2_fate__series___medusa__fate___fate__series__112.webp"}, {"prompt": "idolmaster, amami haruka, idolmaster", "chinese_prompt": "天海春香 (偶像大师)", "image_path": "assets/output_2_idolmaster__amami_haruka__idolmaster_113.webp"}, {"prompt": "blue_archive, kayoko (blue archive), blue archive", "chinese_prompt": "鬼方佳世子 (蔚蓝档案)", "image_path": "assets/output_2_blue_archive__kayoko__blue_archive___blue_archive_114.webp"}, {"prompt": "sousou_no_frieren, fern (sousou no frieren), sousou no frieren", "chinese_prompt": "费伦 (葬送的芙莉莲)", "image_path": "assets/output_2_sousou_no_frieren__fern__sousou_no_frieren___sousou_no_frieren_115.webp"}, {"prompt": "pokemon, cynthia (pokemon), pokemon", "chinese_prompt": "竹兰 (宝可梦)", "image_path": "assets/output_2_pokemon__cynthia__pokemon___pokemon_116.webp"}, {"prompt": "touh<PERSON>, sukuna s<PERSON>, touhou", "chinese_prompt": "少名针妙丸 (东方)", "image_path": "assets/output_2_touh<PERSON>__sukuna_shin<PERSON><PERSON><PERSON><PERSON>__touhou_117.webp"}, {"prompt": "azur_lane, taihou (azur lane), azur lane", "chinese_prompt": "大凤 (碧蓝航线)", "image_path": "assets/output_2_azur_lane__taihou__azur_lane___azur_lane_118.webp"}, {"prompt": "touh<PERSON>, futa<PERSON><PERSON><PERSON> ma<PERSON>, touhou", "chinese_prompt": "二岩猯藏 (东方)", "image_path": "assets/output_2_touh<PERSON>__futa<PERSON><PERSON><PERSON>_mamizou__touhou_119.webp"}, {"prompt": "kono_subarashii_sekai_ni_shukufuku_wo!, aqua (konosuba), kono subarashii sekai ni shukufuku wo!", "chinese_prompt": "阿库娅 (为美好的世界献上祝福)", "image_path": "assets/output_2_kono_subarashii_sekai_ni_shukufuku_wo___aqua__konosuba___kono_subarashii_sekai_ni_shukufuku_wo__120.webp"}, {"prompt": "kantai_collection, shiranui (kancolle), kantai collection", "chinese_prompt": "不知火 (舰队收藏)", "image_path": "assets/output_2_kantai_collection__shiranui__kancolle___kantai_collection_121.webp"}, {"prompt": "idolmaster, y<PERSON><PERSON> r<PERSON>, idolmaster", "chinese_prompt": "梦见璃亚梦 (灰姑娘) (偶像大师)", "image_path": "assets/output_2_idolmaster__yume<PERSON>_riamu__idolmaster_122.webp"}, {"prompt": "tengen_toppa_gurren_lagann, yoko littner, tengen toppa gurren lagann", "chinese_prompt": "庸子‧利坦拿 (天元突破 红莲螺岩)", "image_path": "assets/output_2_tengen_toppa_gurren_lagann__yoko_littner__tengen_toppa_gurren_lagann_123.webp"}, {"prompt": "fate_(series), bb (fate), fate (series)", "chinese_prompt": "BB (Fate)", "image_path": "assets/output_2_fate__series___bb__fate___fate__series__124.webp"}, {"prompt": "overwatch, d.va (overwatch), overwatch", "chinese_prompt": "<PERSON><PERSON> (斗阵特攻)", "image_path": "assets/output_2_overwatch__d_va__overwatch___overwatch_125.webp"}, {"prompt": "kantai_collection, sazanami (kancolle), kantai collection", "chinese_prompt": "涟 (舰队收藏)", "image_path": "assets/output_2_kantai_collection__sazanami__kancolle___kantai_collection_126.webp"}, {"prompt": "genshin_impact, boo tao (genshin impact), genshin impact", "chinese_prompt": "胡桃 (鬼) (原神)", "image_path": "assets/output_2_genshin_impact__boo_tao__genshin_impact___genshin_impact_127.webp"}, {"prompt": "fate_(series), okita souji (koha-ace), fate (series)", "chinese_prompt": "冲田总司 (帝都圣杯奇谭) (Fate)", "image_path": "assets/output_2_fate__series___okita_souji__koha-ace___fate__series__128.webp"}, {"prompt": "pokemon, ash ketchum, pokemon", "chinese_prompt": "小智 (宝可梦)", "image_path": "assets/output_2_pokemon__ash_ketchum__pokemon_129.webp"}, {"prompt": "kantai_collection, souryuu (kancolle), kantai collection", "chinese_prompt": "苍龙 (舰队收藏)", "image_path": "assets/output_2_kantai_collection__souryuu__kancolle___kantai_collection_130.webp"}, {"prompt": "hololive, ho<PERSON><PERSON> marine (1st costume), hololive", "chinese_prompt": "宝钟玛琳 (1st服), (Hololive)", "image_path": "assets/output_2_hololive__houshou_marine__1st_costume___hololive_131.webp"}, {"prompt": "girls_und_panzer, a<PERSON><PERSON> yukari, girls und panzer", "chinese_prompt": "秋山优花里 (少女与战车)", "image_path": "assets/output_2_girls_und_panzer__a<PERSON><PERSON>_yuka<PERSON>__girls_und_panzer_132.webp"}, {"prompt": "umamusume, manhattan cafe (umamusume), umamusume", "chinese_prompt": "曼城茶座 (赛马娘)", "image_path": "assets/output_2_umamusume__manhattan_cafe__umamusume___umamusume_133.webp"}, {"prompt": "touhou, aki shi<PERSON>ha, touhou", "chinese_prompt": "秋静叶 (东方)", "image_path": "assets/output_2_touhou__aki_shi<PERSON><PERSON>__touhou_134.webp"}, {"prompt": "dragon_ball, son goku, dragon ball", "chinese_prompt": "孙悟空 (七龙珠)", "image_path": "assets/output_2_dragon_ball__son_goku__dragon_ball_135.webp"}, {"prompt": "boku_no_hero_academia, <PERSON><PERSON><PERSON> i<PERSON>, boku no hero academia", "chinese_prompt": "绿谷出久 (我的英雄学院)", "image_path": "assets/output_2_boku_no_hero_academia__midoriya_i<PERSON><PERSON>__boku_no_hero_academia_136.webp"}, {"prompt": "spy_x_family, anya (spy x family), spy x family", "chinese_prompt": "安妮亚•佛杰 (SPY x FAMILY 间谍家家酒)", "image_path": "assets/output_2_spy_x_family__anya__spy_x_family___spy_x_family_137.webp"}, {"prompt": "kantai_collection, samidare (kancolle), kantai collection", "chinese_prompt": "五月雨 (舰队收藏)", "image_path": "assets/output_2_kantai_collection__samidare__kancolle___kantai_collection_138.webp"}, {"prompt": "fate_(series), nero claudius (fate/extra), fate (series)", "chinese_prompt": "尼禄 (Fate/Extra), (Fate)", "image_path": "assets/output_2_fate__series___nero_claudius__fate_extra___fate__series__139.webp"}, {"prompt": "hololive, shishiro botan, hololive", "chinese_prompt": "狮白牡丹 (Hololive)", "image_path": "assets/output_2_hololive__s<PERSON><PERSON>_botan__hololive_140.webp"}, {"prompt": "idolmaster, shi<PERSON> ta<PERSON>e, idolmaster", "chinese_prompt": "四条贵音 (百万现场) (偶像大师)", "image_path": "assets/output_2_idolmaster__shi<PERSON>_takane__idolmaster_141.webp"}, {"prompt": "vocaloid, gumi, vocaloid", "chinese_prompt": "GUMI  (Vocaloid)", "image_path": "assets/output_2_vocaloid__gumi__vocaloid_142.webp"}, {"prompt": "gundam, miorine rembran, gundam", "chinese_prompt": "米奥琳涅·连布兰 (水星魔女) (钢弹)", "image_path": "assets/output_2_gundam__miorine_rembran__gundam_143.webp"}, {"prompt": "idolmaster, shir<PERSON><PERSON> koume, idolmaster", "chinese_prompt": "白坂小梅 (灰姑娘) (偶像大师)", "image_path": "assets/output_2_idolmaster__shir<PERSON><PERSON>_kou<PERSON>__idolmaster_144.webp"}, {"prompt": "touhou, letty whiterock, touhou", "chinese_prompt": "蕾蒂·霍瓦特洛克 (东方)", "image_path": "assets/output_2_touhou__letty_whiterock__touhou_145.webp"}, {"prompt": "kill_la_kill, senketsu, kill la kill", "chinese_prompt": "缠流子 (鲜血) (斩服少女)", "image_path": "assets/output_2_kill_la_kill__senketsu__kill_la_kill_146.webp"}, {"prompt": "touh<PERSON>, hecatia lapis<PERSON>, touhou", "chinese_prompt": "赫卡提亚·拉碧斯拉祖利 (东方)", "image_path": "assets/output_2_touhou__hecatia_lapislazuli__touhou_147.webp"}, {"prompt": "hololive, yuki<PERSON> lamy, hololive", "chinese_prompt": "雪花菈米 (Hololive)", "image_path": "assets/output_2_hololive__yuki<PERSON>_lamy__hololive_148.webp"}, {"prompt": "fate_(series), mordred (fate/apocrypha), fate (series)", "chinese_prompt": "莫德雷德 (Fate/Apocrypha), (Fate)", "image_path": "assets/output_2_fate__series___mordred__fate_apocrypha___fate__series__149.webp"}, {"prompt": "pokemon, red (pokemon), pokemon", "chinese_prompt": "赤红 (宝可梦)", "image_path": "assets/output_2_pokemon__red__pokemon___pokemon_150.webp"}, {"prompt": "kantai_collection, yamashiro (kancolle), kantai collection", "chinese_prompt": "山城 (舰队收藏)", "image_path": "assets/output_2_kantai_collection__ya<PERSON><PERSON>__kancolle___kantai_collection_151.webp"}, {"prompt": "blue_archive, aru (blue archive), blue archive", "chinese_prompt": "陆八魔亚瑠 (蔚蓝档案)", "image_path": "assets/output_2_blue_archive__aru__blue_archive___blue_archive_152.webp"}, {"prompt": "lycoris_recoil, inoue takina, lycoris recoil", "chinese_prompt": "井之上泷奈 (莉可丽丝)", "image_path": "assets/output_2_lycoris_recoil__inoue_takina__lycoris_recoil_153.webp"}, {"prompt": "umamusume, tokai teio (umamusume), umamusume", "chinese_prompt": "东海帝王 (赛马娘)", "image_path": "assets/output_2_umamusume__tokai_teio__umamusume___umamusume_154.webp"}, {"prompt": "blue_archive, arona (blue archive), blue archive", "chinese_prompt": "阿洛娜 (蔚蓝档案)", "image_path": "assets/output_2_blue_archive__arona__blue_archive___blue_archive_155.webp"}, {"prompt": "idolmaster, hi<PERSON> madoka, idolmaster", "chinese_prompt": "樋口圆香 (闪耀色彩) (偶像大师)", "image_path": "assets/output_2_idolmaster__hi<PERSON>_madoka__idolmaster_156.webp"}, {"prompt": "monogatari_(series), o<PERSON><PERSON> shinobu, monogatari (series)", "chinese_prompt": "忍野忍 (化物语)", "image_path": "assets/output_2_monogatari__series___oshino_shinobu__monogatari__series__157.webp"}, {"prompt": "rozen_maiden, su<PERSON><PERSON><PERSON>, rozen maiden", "chinese_prompt": "水银灯 (蔷薇少女)", "image_path": "assets/output_2_rozen_maiden__suigintou__rozen_maiden_158.webp"}, {"prompt": "azur_lane, bremerton (azur lane), azur lane", "chinese_prompt": "布雷默顿 (碧蓝航线)", "image_path": "assets/output_2_azur_lane__bremerton__azur_lane___azur_lane_159.webp"}, {"prompt": "kill_la_kill, k<PERSON><PERSON><PERSON>, kill la kill", "chinese_prompt": "鬼龙院皐月 (斩服少女)", "image_path": "assets/output_2_kill_la_kill__kir<PERSON><PERSON>_satsuki__kill_la_kill_160.webp"}, {"prompt": "kantai_collection, iowa (kancolle), kantai collection", "chinese_prompt": "爱荷华 (舰队收藏)", "image_path": "assets/output_2_kantai_collection__iowa__kancolle___kantai_collection_161.webp"}, {"prompt": "chainsaw_man, power (chainsaw man), chainsaw man", "chinese_prompt": "帕瓦 (电锯人)", "image_path": "assets/output_2_chainsaw_man__power__chainsaw_man___chainsaw_man_162.webp"}, {"prompt": "genshin_impact, wanderer (genshin impact), genshin impact", "chinese_prompt": "流浪者 (原神)", "image_path": "assets/output_2_genshin_impact__wanderer__genshin_impact___genshin_impact_163.webp"}, {"prompt": "kantai_collection, hiei (kancolle), kantai collection", "chinese_prompt": "比叡 (舰队收藏)", "image_path": "assets/output_2_kantai_collection__hiei__kancolle___kantai_collection_164.webp"}, {"prompt": "idolmaster, k<PERSON><PERSON><PERSON> chih<PERSON>, idolmaster", "chinese_prompt": "如月千早 (偶像大师)", "image_path": "assets/output_2_idolmaster__k<PERSON><PERSON><PERSON>_chih<PERSON>__idolmaster_165.webp"}, {"prompt": "kantai_collection, kasumi (kancolle), kantai collection", "chinese_prompt": "霞 (舰队收藏)", "image_path": "assets/output_2_kantai_collection__kasu<PERSON>__kancolle___kantai_collection_166.webp"}, {"prompt": "bocchi_the_rock!, ijichi niji<PERSON>, bocchi the rock!", "chinese_prompt": "伊地知虹夏 (孤独摇滚)", "image_path": "assets/output_2_bocchi_the_rock___ijichi_nijika__bocchi_the_rock__167.webp"}, {"prompt": "kantai_collection, ro-500 (kancolle), kantai collection", "chinese_prompt": "RO-500 (舰队收藏)", "image_path": "assets/output_2_kantai_collection__ro-500__kancolle___kantai_collection_168.webp"}, {"prompt": "hololive, gawr g<PERSON> (1st costume), hololive", "chinese_prompt": "噶呜·古拉 (鲨鲨) (1st服) (Hololive)", "image_path": "assets/output_2_hololive__gawr_gura__1st_costume___hololive_169.webp"}, {"prompt": "honkai_(series), acheron (honkai: star rail), honkai (series)", "chinese_prompt": "黄泉 (崩坏: 星穹铁道) (崩坏)", "image_path": "assets/output_2_honkai__series___acheron__honkai__star_rail___honkai__series__170.webp"}, {"prompt": "idolmaster, kik<PERSON> makoto, idolmaster", "chinese_prompt": "菊池真 (偶像大师)", "image_path": "assets/output_2_idolmaster__kik<PERSON>_makoto__idolmaster_171.webp"}, {"prompt": "love_live!, kousaka honoka, love live!", "chinese_prompt": "高坂穗乃果 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_2_love_live___k<PERSON><PERSON>_honoka__love_live__172.webp"}, {"prompt": "blue_archive, hanako (blue archive), blue archive", "chinese_prompt": "浦和花子 (蔚蓝档案)", "image_path": "assets/output_2_blue_archive__hanako__blue_archive___blue_archive_173.webp"}, {"prompt": "street_fighter, cammy white, street fighter", "chinese_prompt": "倩咪 (快打旋风)", "image_path": "assets/output_2_street_fighter__cammy_white__street_fighter_174.webp"}, {"prompt": "kantai_collection, wo-class aircraft carrier, kantai collection", "chinese_prompt": "WO级航母 (舰队收藏)", "image_path": "assets/output_2_kantai_collection__wo-class_aircraft_carrier__kantai_collection_175.webp"}, {"prompt": "mario_(series), mario, mario (series)", "chinese_prompt": "玛利欧娘 (超级玛利欧)", "image_path": "assets/output_2_mario__series___mario__mario__series__176.webp"}, {"prompt": "honkai_(series), kiana kaslana, honkai (series)", "chinese_prompt": "琪亚娜·卡斯兰娜 (崩坏)", "image_path": "assets/output_2_honkai__series___kiana_kaslana__honkai__series__177.webp"}, {"prompt": "idolmaster, minase i<PERSON>, idolmaster", "chinese_prompt": "水瀬伊织 (偶像大师)", "image_path": "assets/output_2_idolmaster__minase_iori__idolmaster_178.webp"}, {"prompt": "kantai_collection, kirishima (kancolle), kantai collection", "chinese_prompt": "雾岛 (舰队收藏)", "image_path": "assets/output_2_kantai_collection__kirishima__kancolle___kantai_collection_179.webp"}, {"prompt": "kantai_collection, verniy (kancolle), kantai collection", "chinese_prompt": "响 Верный (舰队收藏)", "image_path": "assets/output_2_kantai_collection__verniy__kancolle___kantai_collection_180.webp"}, {"prompt": "one_piece, monkey d. luffy, one piece", "chinese_prompt": "鲁夫 (海贼王)", "image_path": "assets/output_2_one_piece__monkey_d__luffy__one_piece_181.webp"}, {"prompt": "azur_lane, atago (azur lane), azur lane", "chinese_prompt": "爱宕 (碧蓝航线)", "image_path": "assets/output_2_azur_lane__atago__azur_lane___azur_lane_182.webp"}, {"prompt": "blue_archive, toki (bunny) (blue archive), blue archive", "chinese_prompt": "飞鸟马时 (兔子装) (蔚蓝档案)", "image_path": "assets/output_2_blue_archive__toki__bunny___blue_archive___blue_archive_183.webp"}, {"prompt": "love_live!, hoshizora rin, love live!", "chinese_prompt": "星空凛 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_2_love_live___hoshizora_rin__love_live__184.webp"}, {"prompt": "genshin_impact, nilou (genshin impact), genshin impact", "chinese_prompt": "妮露 (原神)", "image_path": "assets/output_2_genshin_impact__nilou__genshin_impact___genshin_impact_185.webp"}, {"prompt": "fate_(series), saber alter, fate (series)", "chinese_prompt": "黑Saber (Fate)", "image_path": "assets/output_2_fate__series___saber_alter__fate__series__186.webp"}, {"prompt": "azur_lane, formidable (azur lane), azur lane", "chinese_prompt": "可畏 (碧蓝航线)", "image_path": "assets/output_2_azur_lane__formidable__azur_lane___azur_lane_187.webp"}, {"prompt": "blue_archive, ako (blue archive), blue archive", "chinese_prompt": "天雨亚子 (蔚蓝档案)", "image_path": "assets/output_2_blue_archive__ako__blue_archive___blue_archive_188.webp"}, {"prompt": "idolmaster, s<PERSON><PERSON>, idolmaster", "chinese_prompt": "岛村卯月 (灰姑娘) (偶像大师)", "image_path": "assets/output_2_idolmaster__s<PERSON><PERSON>_<PERSON><PERSON>__idolmaster_189.webp"}, {"prompt": "azur_lane, prinz eugen (azur lane), azur lane", "chinese_prompt": "欧根亲王 (碧蓝航线)", "image_path": "assets/output_2_azur_lane__prinz_eugen__azur_lane___azur_lane_190.webp"}, {"prompt": "fate_(series), melt<PERSON><PERSON> (fate), fate (series)", "chinese_prompt": "喵吹丽斯 溶解莉莉丝 (Fate)", "image_path": "assets/output_2_fate__series___meltryllis__fate___fate__series__191.webp"}, {"prompt": "pokemon, serena (pokemon), pokemon", "chinese_prompt": "莎莉娜 (宝可梦)", "image_path": "assets/output_2_pokemon__serena__pokemon___pokemon_192.webp"}, {"prompt": "fate_(series), eresh<PERSON><PERSON> (fate), fate (series)", "chinese_prompt": "埃列什基伽勒 (Fate)", "image_path": "assets/output_2_fate__series___ereshkigal__fate___fate__series__193.webp"}, {"prompt": "hololive, uruha rushia, hololive", "chinese_prompt": "润羽露西娅 (Hololive)", "image_path": "assets/output_2_hololive__uruha_rushia__hololive_194.webp"}, {"prompt": "blue_archive, momoi (blue archive), blue archive", "chinese_prompt": "才羽桃井 (蔚蓝档案)", "image_path": "assets/output_2_blue_archive__momoi__blue_archive___blue_archive_195.webp"}, {"prompt": "idolmaster, taka<PERSON><PERSON> kaede, idolmaster", "chinese_prompt": "高垣枫 (灰姑娘) (偶像大师)", "image_path": "assets/output_2_idolmaster__taka<PERSON><PERSON>_ka<PERSON>__idolmaster_196.webp"}, {"prompt": "neon_genesis_evangelion, i<PERSON> shinji, neon genesis evangelion", "chinese_prompt": "碇真嗣 (新世纪福音战士)", "image_path": "assets/output_2_neon_genesis_evangelion__i<PERSON>_shinji__neon_genesis_evangelion_197.webp"}, {"prompt": "blue_archive, plana (blue archive), blue archive", "chinese_prompt": "普拉娜 (蔚蓝档案)", "image_path": "assets/output_2_blue_archive__plana__blue_archive___blue_archive_198.webp"}, {"prompt": "hololive, hakos baelz, hololive", "chinese_prompt": "哈珂斯·贝尔丝 (Hololive)", "image_path": "assets/output_2_hololive__hakos_baelz__hololive_199.webp"}, {"prompt": "to<PERSON><PERSON>, ex-keine, touhou", "chinese_prompt": "ex-上白泽慧音 (东方)", "image_path": "assets/output_2_touhou__ex-keine__touhou_200.webp"}, {"prompt": "idolmaster, sa<PERSON><PERSON> fumika, idolmaster", "chinese_prompt": "鹭泽文香 (灰姑娘) (偶像大师)", "image_path": "assets/output_2_idolmaster__sagis<PERSON>_fumika__idolmaster_201.webp"}, {"prompt": "fate_(series), jeanne d'arc (ruler) (fate), fate (series)", "chinese_prompt": "贞德 (裁定者) (Fate)", "image_path": "assets/output_2_fate__series___jeanne_d_arc__ruler___fate___fate__series__202.webp"}, {"prompt": "hololive, la+ darknesss, hololive", "chinese_prompt": "拉普拉斯·暗黑 (Hololive)", "image_path": "assets/output_2_hololive__la__darknesss__hololive_203.webp"}, {"prompt": "sono_bisque_doll_wa_koi_wo_suru, kit<PERSON><PERSON> marin, sono bisque doll wa koi wo suru", "chinese_prompt": "喜多川海梦 (恋上换装娃娃)", "image_path": "assets/output_2_sono_bisque_doll_wa_koi_wo_suru__kitaga<PERSON>_marin__sono_bisque_doll_wa_koi_wo_suru_204.webp"}, {"prompt": "kantai_collection, yuubari (kancolle), kantai collection", "chinese_prompt": "夕张 (舰队收藏)", "image_path": "assets/output_3_kantai_collection__yuuba<PERSON>__kancolle___kantai_collection_0.webp"}, {"prompt": "granblue_fantasy, djeeta (granblue fantasy), granblue fantasy", "chinese_prompt": "姬塔 (碧蓝幻想)", "image_path": "assets/output_3_granblue_fantasy__djeeta__granblue_fantasy___granblue_fantasy_1.webp"}, {"prompt": "splatoon_(series), octoling player character, splatoon (series)", "chinese_prompt": "章鱼玩家角色 (斯普拉遁)", "image_path": "assets/output_3_splatoon__series___octoling_player_character__splatoon__series__2.webp"}, {"prompt": "boku_no_hero_academia, mirko, boku no hero academia", "chinese_prompt": "兔山露美 (我的英雄学院)", "image_path": "assets/output_3_boku_no_hero_academia__mirko__boku_no_hero_academia_3.webp"}, {"prompt": "fate_(series), mi<PERSON><PERSON> musashi (fate), fate (series)", "chinese_prompt": "宫本武藏 (Fate)", "image_path": "assets/output_3_fate__series___mi<PERSON>oto_musashi__fate___fate__series__4.webp"}, {"prompt": "idolmaster, g<PERSON><PERSON> hibiki, idolmaster", "chinese_prompt": "我那霸响 (偶像大师)", "image_path": "assets/output_3_idolmaster__gana<PERSON>_hibiki__idolmaster_5.webp"}, {"prompt": "mahou_shoujo_madoka_magica, ultimate madoka, mahou shoujo madoka magica", "chinese_prompt": "终极小圆 圆神 (魔法少女小圆)", "image_path": "assets/output_3_mahou_shoujo_madoka_magica__ultimate_madoka__mahou_shoujo_madoka_magica_6.webp"}, {"prompt": "honkai_(series), march 7th (honkai: star rail), honkai (series)", "chinese_prompt": "三月七 (崩坏：星穹铁道) (崩坏)", "image_path": "assets/output_3_honkai__series___march_7th__honkai__star_rail___honkai__series__7.webp"}, {"prompt": "hololive, mura<PERSON> shion, hololive", "chinese_prompt": "紫咲诗音 (Hololive)", "image_path": "assets/output_3_hololive__m<PERSON><PERSON>_shion__hololive_8.webp"}, {"prompt": "arknights, lappland (arknights), arknights", "chinese_prompt": "拉普兰德 (明日方舟)", "image_path": "assets/output_3_arknights__lappland__arknights___arknights_9.webp"}, {"prompt": "pokemon, eevee, pokemon", "chinese_prompt": "伊布 (宝可梦)", "image_path": "assets/output_3_pokemon__eevee__pokemon_10.webp"}, {"prompt": "honkai_(series), fu hua, honkai (series)", "chinese_prompt": "符华 (崩坏)", "image_path": "assets/output_3_honkai__series___fu_hua__honkai__series__11.webp"}, {"prompt": "fire_emblem, lucina (fire emblem), fire emblem", "chinese_prompt": "露琪娜 (觉醒) (圣火降魔录)", "image_path": "assets/output_3_fire_emblem__lucina__fire_emblem___fire_emblem_12.webp"}, {"prompt": "<PERSON><PERSON><PERSON>_haru<PERSON>_no_yu<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> mi<PERSON>, suzu<PERSON> haruhi no yuuutsu", "chinese_prompt": "朝比奈实玖瑠 (凉宫春日的忧郁)", "image_path": "assets/output_3_su<PERSON><PERSON>_haru<PERSON>_no_yuuutsu__as<PERSON><PERSON>_miku<PERSON>__suzu<PERSON>_haruhi_no_yuuutsu_13.webp"}, {"prompt": "princess_connect!, ka<PERSON> (princess connect!), princess connect!", "chinese_prompt": "凯留 (公主连结)", "image_path": "assets/output_3_princess_connect___karyl__princess_connect____princess_connect__14.webp"}, {"prompt": "touhou, wa<PERSON><PERSON><PERSON><PERSON>, touhou", "chinese_prompt": "若鹭姬 (东方)", "image_path": "assets/output_3_touh<PERSON>__wa<PERSON><PERSON><PERSON><PERSON>__touhou_15.webp"}, {"prompt": "fire_emblem, corrin (fire emblem), fire emblem", "chinese_prompt": "神威 (if) (圣火降魔录)", "image_path": "assets/output_3_fire_emblem__corrin__fire_emblem___fire_emblem_16.webp"}, {"prompt": "pokemon, selene (pokemon), pokemon", "chinese_prompt": "美月 (宝可梦)", "image_path": "assets/output_3_pokemon__selene__pokemon___pokemon_17.webp"}, {"prompt": "kantai_collection, kiso (kancolle), kantai collection", "chinese_prompt": "木曾 (舰队收藏)", "image_path": "assets/output_3_kantai_collection__kiso__kancolle___kantai_collection_18.webp"}, {"prompt": "darling_in_the_franxx, zero two (darling in the franxx), darling in the franxx", "chinese_prompt": "02 (<PERSON> in the Franxx)", "image_path": "assets/output_3_darling_in_the_franxx__zero_two__darling_in_the_franxx___darling_in_the_franxx_19.webp"}, {"prompt": "boku_no_hero_academia, u<PERSON><PERSON>, boku no hero academia", "chinese_prompt": "丽日御茶子 (我的英雄学院)", "image_path": "assets/output_3_boku_no_hero_academia__uraraka_ochako__boku_no_hero_academia_20.webp"}, {"prompt": "touhou, kishin sagume, touhou", "chinese_prompt": "稀神探女 (东方)", "image_path": "assets/output_3_touhou__kishin_sagume__touhou_21.webp"}, {"prompt": "kantai_collection, hiryuu (kancolle), kantai collection", "chinese_prompt": "飞龙 (舰队收藏)", "image_path": "assets/output_3_kantai_collection__hiryuu__kancolle___kantai_collection_22.webp"}, {"prompt": "umamusume, silence suzuka (umamusume), umamusume", "chinese_prompt": "无声铃鹿 (赛马娘)", "image_path": "assets/output_3_umamusume__silence_suzuka__umamusume___umamusume_23.webp"}, {"prompt": "chainsaw_man, den<PERSON> (chainsaw man), chainsaw man", "chinese_prompt": "淀治 (电锯人)", "image_path": "assets/output_3_chainsaw_man__denji__chainsaw_man___chainsaw_man_24.webp"}, {"prompt": "hololive, ceres fauna, hololive", "chinese_prompt": "塞莱希·法娜 (Hololive)", "image_path": "assets/output_3_hololive__ceres_fauna__hololive_25.webp"}, {"prompt": "bocchi_the_rock!, yamada ryo, bocchi the rock!", "chinese_prompt": "山田凉 (孤独摇滚)", "image_path": "assets/output_3_bocchi_the_rock___yamada_ryo__bocchi_the_rock__26.webp"}, {"prompt": "watashi_ga_motenai_no_wa_dou_kangaetemo_omaera_ga_warui!, k<PERSON><PERSON> to<PERSON>, watashi ga motenai no wa dou kangaetemo omaera ga warui!", "chinese_prompt": "黑木智子 (我不受欢迎，怎么想都是你们的错)", "image_path": "assets/output_3_watashi_ga_motenai_no_wa_dou_kangaetemo_omaera_ga_warui___kuroki_tomoko__watashi_ga_motenai_no_wa_dou_kangaetemo_omaera_ga_warui__27.webp"}, {"prompt": "fate_(series), ishtar (fate), fate (series)", "chinese_prompt": "伊什塔尔 (Fate)", "image_path": "assets/output_3_fate__series___ishtar__fate___fate__series__28.webp"}, {"prompt": "hololive, nakiri ayame, hololive", "chinese_prompt": "百鬼绫目 (Hololive)", "image_path": "assets/output_3_hololive__nakiri_ayame__hololive_29.webp"}, {"prompt": "honkai_(series), elysia (honkai impact), honkai (series)", "chinese_prompt": "爱莉希雅 (崩坏)", "image_path": "assets/output_3_honkai__series___elysia__honkai_impact___honkai__series__30.webp"}, {"prompt": "love_live!, tsus<PERSON> yoshiko, love live!", "chinese_prompt": "津岛善子 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_3_love_live___tsus<PERSON>_yoshi<PERSON>__love_live__31.webp"}, {"prompt": "hololive, usada pek<PERSON> (1st costume), hololive", "chinese_prompt": "兔田佩克拉 (1st服), (Hololive)", "image_path": "assets/output_3_hololive__usada_pekora__1st_costume___hololive_32.webp"}, {"prompt": "genshin_impact, yelan (genshin impact), genshin impact", "chinese_prompt": "夜兰 (原神)", "image_path": "assets/output_3_genshin_impact__yelan__genshin_impact___genshin_impact_33.webp"}, {"prompt": "fate_(series), medusa (rider) (fate), fate (series)", "chinese_prompt": "美杜莎 (骑) (Fate)", "image_path": "assets/output_3_fate__series___medusa__rider___fate___fate__series__34.webp"}, {"prompt": "kantai_collection, graf zeppelin (kancolle), kantai collection", "chinese_prompt": "齐柏林伯爵 (舰队收藏)", "image_path": "assets/output_3_kantai_collection__graf_zeppelin__kancolle___kantai_collection_35.webp"}, {"prompt": "hololive, shiranui flare, hololive", "chinese_prompt": "不知火芙蕾雅 (Hololive)", "image_path": "assets/output_3_hololive__shir<PERSON>i_flare__hololive_36.webp"}, {"prompt": "one_piece, nico robin, one piece", "chinese_prompt": "妮可·罗宾 (海贼王)", "image_path": "assets/output_3_one_piece__nico_robin__one_piece_37.webp"}, {"prompt": "naru<PERSON>_(series), haruno sakura, naru<PERSON> (series)", "chinese_prompt": "春野樱 (火影忍者)", "image_path": "assets/output_3_naruto__series___haruno_sakura__naruto__series__38.webp"}, {"prompt": "dungeon_meshi, marcille donato, dungeon meshi", "chinese_prompt": "玛露西尔 (迷宫饭)", "image_path": "assets/output_3_dungeon_meshi__marcille_donato__dungeon_meshi_39.webp"}, {"prompt": "league_of_legends, ahri (league of legends), league of legends", "chinese_prompt": "阿璃 (英雄联盟 LOL)", "image_path": "assets/output_3_league_of_legends__ahri__league_of_legends___league_of_legends_40.webp"}, {"prompt": "touhou, lily white, touhou", "chinese_prompt": "莉莉霍瓦特 (东方)", "image_path": "assets/output_3_touhou__lily_white__touhou_41.webp"}, {"prompt": "jujutsu_kaisen, gojou sator<PERSON>, jujutsu kaisen", "chinese_prompt": "五条悟 (咒术回战)", "image_path": "assets/output_3_jujutsu_kaisen__gojou_satoru__jujutsu_kaisen_42.webp"}, {"prompt": "black_rock_shooter, black rock shooter (character), black rock shooter", "chinese_prompt": "黑岩射手 (黑岩射手)", "image_path": "assets/output_3_black_rock_shooter__black_rock_shooter__character___black_rock_shooter_43.webp"}, {"prompt": "fire_emblem, ed<PERSON><PERSON>, fire emblem", "chinese_prompt": "艾黛尔贾特·冯·弗雷斯贝尔古 (风花雪月) (圣火降魔录)", "image_path": "assets/output_3_fire_emblem__ed<PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>g__fire_emblem_44.webp"}, {"prompt": "azur_lane, sirius (azur lane), azur lane", "chinese_prompt": "天狼星 (碧蓝航线)", "image_path": "assets/output_3_azur_lane__sirius__azur_lane___azur_lane_45.webp"}, {"prompt": "azur_lane, belfast (azur lane), azur lane", "chinese_prompt": "贝尔法斯特 (碧蓝航线)", "image_path": "assets/output_3_azur_lane__belfast__azur_lane___azur_lane_46.webp"}, {"prompt": "one_piece, roronoa zoro, one piece", "chinese_prompt": "罗罗亚·索隆 (海贼王)", "image_path": "assets/output_3_one_piece__roronoa_zoro__one_piece_47.webp"}, {"prompt": "bishoujo_senshi_sailor_moon, sailor moon, bishoujo senshi sailor moon", "chinese_prompt": "水手月亮 (美少女战士)", "image_path": "assets/output_3_bishoujo_senshi_sailor_moon__sailor_moon__bishoujo_senshi_sailor_moon_48.webp"}, {"prompt": "blue_archive, noa (blue archive), blue archive", "chinese_prompt": "生盐乃爱 (蔚蓝档案)", "image_path": "assets/output_3_blue_archive__noa__blue_archive___blue_archive_49.webp"}, {"prompt": "love_live!, koi<PERSON>mi hanayo, love live!", "chinese_prompt": "小泉花阳 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_3_love_live___koizu<PERSON>_hanayo__love_live__50.webp"}, {"prompt": "idolmaster, j<PERSON><PERSON><PERSON> mika, idolmaster", "chinese_prompt": "城崎美嘉 (灰姑娘) (偶像大师)", "image_path": "assets/output_3_idolmaster__j<PERSON><PERSON><PERSON>_mika__idolmaster_51.webp"}, {"prompt": "idolmaster, ta<PERSON><PERSON><PERSON> yayoi, idolmaster", "chinese_prompt": "高槻弥生 (偶像大师)", "image_path": "assets/output_3_idolmaster__ta<PERSON><PERSON><PERSON>_yayoi__idolmaster_52.webp"}, {"prompt": "boku_no_hero_academia, bakug<PERSON> katsuki, boku no hero academia", "chinese_prompt": "爆豪胜己 (我的英雄学院)", "image_path": "assets/output_3_boku_no_hero_academia__bakugou_katsuki__boku_no_hero_academia_53.webp"}, {"prompt": "su<PERSON><PERSON>_haruhi_no_yuu<PERSON><PERSON>, k<PERSON>, suzumiya haruhi no yuuutsu", "chinese_prompt": "阿虚 (凉宫春日的忧郁)", "image_path": "assets/output_3_su<PERSON><PERSON>_haru<PERSON>_no_yuuutsu__kyon__suzu<PERSON>_haruhi_no_yuuutsu_54.webp"}, {"prompt": "higurashi_no_naku_koro_ni, furude rika, higurashi no naku koro ni", "chinese_prompt": "古手梨花 (暮蝉悲鸣时)", "image_path": "assets/output_3_higurashi_no_naku_koro_ni__furude_rika__higurashi_no_naku_koro_ni_55.webp"}, {"prompt": "hololive, kiryu coco, hololive", "chinese_prompt": "桐生可可 (Hololive)", "image_path": "assets/output_3_hololive__kiryu_coco__hololive_56.webp"}, {"prompt": "gridman_universe, takarada rikka, gridman universe", "chinese_prompt": "宝多六花 (Gridman Universe)", "image_path": "assets/output_3_gridman_universe__takarada_rikka__gridman_universe_57.webp"}, {"prompt": "zenless_zone_zero, ellen joe, zenless zone zero", "chinese_prompt": "艾莲·乔 (绝区零)", "image_path": "assets/output_3_zenless_zone_zero__ellen_joe__zenless_zone_zero_58.webp"}, {"prompt": "dungeon_ni_deai_wo_motomeru_no_wa_machigatteiru_darou_ka, he<PERSON><PERSON> (da<PERSON><PERSON><PERSON>), dungeon ni deai wo motomeru no wa machigatteiru darou ka", "chinese_prompt": "赫斯缇雅 (在地下城寻求邂逅是否搞错了什么)", "image_path": "assets/output_3_dungeon_ni_deai_wo_motomeru_no_wa_machigatteiru_darou_ka__hestia__danmachi___dungeon_ni_deai_wo_motomeru_no_wa_machigatteiru_darou_ka_59.webp"}, {"prompt": "blue_archive, miyu (blue archive), blue archive", "chinese_prompt": "霞泽美游 (蔚蓝档案)", "image_path": "assets/output_3_blue_archive__miyu__blue_archive___blue_archive_60.webp"}, {"prompt": "mario_(series), bowsette, mario (series)", "chinese_prompt": "库巴公主 (超级玛利欧)", "image_path": "assets/output_3_mario__series___bowsette__mario__series__61.webp"}, {"prompt": "persona, narukami yuu, persona", "chinese_prompt": "鸣上悠 (P4) (女神异闻录)", "image_path": "assets/output_3_persona__naruk<PERSON>_yuu__persona_62.webp"}, {"prompt": "azur_lane, commander (azur lane), azur lane", "chinese_prompt": "指挥官 (碧蓝航线)", "image_path": "assets/output_3_azur_lane__commander__azur_lane___azur_lane_63.webp"}, {"prompt": "panty_&_stocking_with_garterbelt, stocking (psg), panty & stocking with garterbelt", "chinese_prompt": "Stocking (吊带袜天使)", "image_path": "assets/output_3_panty___stocking_with_garterbelt__stocking__psg___panty___stocking_with_garterbelt_64.webp"}, {"prompt": "idolmaster, kanzaki ranko, idolmaster", "chinese_prompt": "神崎兰子 (灰姑娘) (偶像大师)", "image_path": "assets/output_3_idolmaster__kanzaki_ranko__idolmaster_65.webp"}, {"prompt": "girls_und_panzer, takebe saori, girls und panzer", "chinese_prompt": "武部沙织 (少女与战车)", "image_path": "assets/output_3_girls_und_panzer__takebe_saori__girls_und_panzer_66.webp"}, {"prompt": "blue_archive, midori (blue archive), blue archive", "chinese_prompt": "才羽绿 (蔚蓝档案)", "image_path": "assets/output_3_blue_archive__midori__blue_archive___blue_archive_67.webp"}, {"prompt": "genshin_impact, sang<PERSON><PERSON> kokomi, genshin impact", "chinese_prompt": "珊瑚宫心海 (原神)", "image_path": "assets/output_3_genshin_impact__sangonomiya_kokomi__genshin_impact_68.webp"}, {"prompt": "blue_archive, saori (blue archive), blue archive", "chinese_prompt": "锭前纱织 (蔚蓝档案)", "image_path": "assets/output_3_blue_archive__saori__blue_archive___blue_archive_69.webp"}, {"prompt": "arknights, suzuran (arknights), arknights", "chinese_prompt": "铃兰 (明日方舟)", "image_path": "assets/output_3_arknights__suzuran__arknights___arknights_70.webp"}, {"prompt": "kantai_collection, naka (kancolle), kantai collection", "chinese_prompt": "那珂 (舰队收藏)", "image_path": "assets/output_3_kantai_collection__naka__kancolle___kantai_collection_71.webp"}, {"prompt": "sonic_(series), sonic the hedgehog, sonic (series)", "chinese_prompt": "音速小子 (音速小子)", "image_path": "assets/output_3_sonic__series___sonic_the_hedgehog__sonic__series__72.webp"}, {"prompt": "love_live!, sa<PERSON><PERSON> riko, love live!", "chinese_prompt": "樱内梨子 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_3_love_live___sa<PERSON><PERSON>_riko__love_live__73.webp"}, {"prompt": "idolmaster, tachi<PERSON> a<PERSON>u, idolmaster", "chinese_prompt": "橘爱丽丝 (灰姑娘) (偶像大师)", "image_path": "assets/output_3_idolmaster__ta<PERSON><PERSON>_arisu__idolmaster_74.webp"}, {"prompt": "kantai_collection, zuihou (kancolle), kantai collection", "chinese_prompt": "瑞凤 (舰队收藏)", "image_path": "assets/output_3_kantai_collection__zu<PERSON><PERSON>__kancolle___kantai_collection_75.webp"}, {"prompt": "kantai_collection, naganami (kancolle), kantai collection", "chinese_prompt": "长波 舰队收藏", "image_path": "assets/output_3_kantai_collection__naganami__kancolle___kantai_collection_76.webp"}, {"prompt": "blue_archive, mutsuki (blue archive), blue archive", "chinese_prompt": "浅黄无月 (蔚蓝档案)", "image_path": "assets/output_3_blue_archive__mutsuki__blue_archive___blue_archive_77.webp"}, {"prompt": "honkai_(series), raiden mei, honkai (series)", "chinese_prompt": "雷电芽衣 (崩坏)", "image_path": "assets/output_3_honkai__series___raiden_mei__honkai__series__78.webp"}, {"prompt": "pokemon, iono (pokemon), pokemon", "chinese_prompt": "奇树 (宝可梦)", "image_path": "assets/output_3_pokemon__iono__pokemon___pokemon_79.webp"}, {"prompt": "genshin_impact, fischl (genshin impact), genshin impact", "chinese_prompt": "菲谢尔 (原神)", "image_path": "assets/output_3_genshin_impact__fischl__genshin_impact___genshin_impact_80.webp"}, {"prompt": "pokemon, misty (pokemon), pokemon", "chinese_prompt": "小霞 (宝可梦)", "image_path": "assets/output_3_pokemon__misty__pokemon___pokemon_81.webp"}, {"prompt": "fate_(series), b<PERSON><PERSON><PERSON> sith (fate), fate (series)", "chinese_prompt": "芭万·希 妖精骑士崔斯坦 (Fate)", "image_path": "assets/output_3_fate__series___b<PERSON><PERSON><PERSON>_sith__fate___fate__series__82.webp"}, {"prompt": "blue_archive, hasumi (blue archive), blue archive", "chinese_prompt": "羽川莲实 (蔚蓝档案)", "image_path": "assets/output_3_blue_archive__hasumi__blue_archive___blue_archive_83.webp"}, {"prompt": "arknights, kal'tsit (arknights), arknights", "chinese_prompt": "凯尔希 (明日方舟)", "image_path": "assets/output_3_arknights__kal_tsit__arknights___arknights_84.webp"}, {"prompt": "touhou, kisume, touhou", "chinese_prompt": "琪斯美 (东方)", "image_path": "assets/output_3_touhou__kisume__touhou_85.webp"}, {"prompt": "go-toubun_no_hanayome, nakano nino, go-toubun no hanayome", "chinese_prompt": "中野二乃 (五等分的新娘)", "image_path": "assets/output_3_go-toubun_no_hanayome__nakano_nino__go-toubun_no_hanayome_86.webp"}, {"prompt": "blue_archive, kanna (blue archive), blue archive", "chinese_prompt": "尾刃环奈 (蔚蓝档案)", "image_path": "assets/output_3_blue_archive__kanna__blue_archive___blue_archive_87.webp"}, {"prompt": "fate_(series), cu chulainn (fate/stay night), fate (series)", "chinese_prompt": "库丘林 (fate/stay night) (Fate)", "image_path": "assets/output_3_fate__series___cu_chulainn__fate_stay_night___fate__series__88.webp"}, {"prompt": "idolmaster, i<PERSON><PERSON> shiki, idolmaster", "chinese_prompt": "一之濑志希 (灰姑娘) (偶像大师)", "image_path": "assets/output_3_idolmaster__ichinose_shiki__idolmaster_89.webp"}, {"prompt": "genshin_impact, barbara (genshin impact), genshin impact", "chinese_prompt": "芭芭拉 (原神)", "image_path": "assets/output_3_genshin_impact__barbara__genshin_impact___genshin_impact_90.webp"}, {"prompt": "arknights, ch'en (arknights), arknights", "chinese_prompt": "陈 (明日方舟)", "image_path": "assets/output_3_arknights__ch_en__arknights___arknights_91.webp"}, {"prompt": "hololive, natsuiro matsuri, hololive", "chinese_prompt": "夏色祭 (Hololive)", "image_path": "assets/output_3_hololive__natsu<PERSON>_matsuri__hololive_92.webp"}, {"prompt": "kantai_collection, fairy (kancolle), kantai collection", "chinese_prompt": "妖精 (舰队收藏)", "image_path": "assets/output_3_kantai_collection__fairy__kancolle___kantai_collection_93.webp"}, {"prompt": "genshin_impact, arlecchino (genshin impact), genshin impact", "chinese_prompt": "阿蕾奇诺 仆人 (原神)", "image_path": "assets/output_3_genshin_impact__arlecchino__genshin_impact___genshin_impact_94.webp"}, {"prompt": "genshin_impact, ka<PERSON>hara kazuha, genshin impact", "chinese_prompt": "枫原万叶 (原神)", "image_path": "assets/output_3_genshin_impact__ka<PERSON><PERSON>_kazuha__genshin_impact_95.webp"}, {"prompt": "monster_hunter_(series), monster hunter (character), monster hunter (series)", "chinese_prompt": "猎人 (魔物猎人)", "image_path": "assets/output_3_monster_hunter__series___monster_hunter__character___monster_hunter__series__96.webp"}, {"prompt": "umamusume, nice nature (umamusume), umamusume", "chinese_prompt": "优秀素质 (赛马娘)", "image_path": "assets/output_3_umamusume__nice_nature__umamusume___umamusume_97.webp"}, {"prompt": "nitroplus, super sonico, nitroplus", "chinese_prompt": "超级索尼子 (Nitro+)", "image_path": "assets/output_3_nitroplus__super_sonico__nitroplus_98.webp"}, {"prompt": "honkai_(series), bronya z<PERSON>, honkai (series)", "chinese_prompt": "布洛妮娅 (崩坏)", "image_path": "assets/output_3_honkai__series___bronya_z<PERSON><PERSON><PERSON>__honkai__series__99.webp"}, {"prompt": "atelier_(series), reisalin stout, atelier (series)", "chinese_prompt": "莱莎 (炼金工房)", "image_path": "assets/output_3_atelier__series___reisalin_stout__atelier__series__100.webp"}, {"prompt": "naru<PERSON>_(series), u<PERSON><PERSON><PERSON> naruto, naru<PERSON> (series)", "chinese_prompt": "漩涡鸣人 (火影忍者)", "image_path": "assets/output_3_naruto__series___u<PERSON><PERSON><PERSON>_naruto__naruto__series__101.webp"}, {"prompt": "saibou_shinkyoku, hats<PERSON><PERSON> hajime, saibou shinkyoku", "chinese_prompt": "初鸟创 (细胞神曲)", "image_path": "assets/output_3_saibou_shinkyoku__hatsutori_hajime__saibou_shinkyoku_102.webp"}, {"prompt": "jojo_no_kimyou_na_bouken, ka<PERSON><PERSON>n noria<PERSON>, jojo no kimyou na bouken", "chinese_prompt": "花京院典明 (JOJO的奇妙冒险)", "image_path": "assets/output_3_jojo_no_kimyou_na_bouken__kakyoin_noriaki__jojo_no_kimyou_na_bouken_103.webp"}, {"prompt": "genshin_impact, jean (genshin impact), genshin impact", "chinese_prompt": "琴 (原神)", "image_path": "assets/output_3_genshin_impact__jean__genshin_impact___genshin_impact_104.webp"}, {"prompt": "kantai_collection, seaport princess, kantai collection", "chinese_prompt": "港湾栖姬 (舰队收藏)", "image_path": "assets/output_3_kantai_collection__seaport_princess__kantai_collection_105.webp"}, {"prompt": "idolmaster, nitta minami, idolmaster", "chinese_prompt": "新田美波 (灰姑娘) (偶像大师)", "image_path": "assets/output_3_idolmaster__nitta_minami__idolmaster_106.webp"}, {"prompt": "jojo_no_kimyou_na_bouken, kujo joly<PERSON>, jojo no kimyou na bouken", "chinese_prompt": "空条徐伦 (JOJO的奇妙冒险)", "image_path": "assets/output_3_jojo_no_kimyou_na_bouken__kujo_jolyne__jojo_no_kimyou_na_bouken_107.webp"}, {"prompt": "fate_(series), chlo<PERSON> von <PERSON>, fate (series)", "chinese_prompt": "克洛伊·冯·爱因兹贝伦 (Fate)", "image_path": "assets/output_3_fate__series___chloe_von_<PERSON><PERSON><PERSON><PERSON>n__fate__series__108.webp"}, {"prompt": "danganron<PERSON>_(series), kiri<PERSON> kyoko, dangan<PERSON><PERSON> (series)", "chinese_prompt": "雾切响子 (弹丸论破)", "image_path": "assets/output_3_danganronpa__series___kirigiri_kyoko__danganronpa__series__109.webp"}, {"prompt": "touhou, medicine melancholy, touhou", "chinese_prompt": "梅蒂欣·梅兰可莉 (东方)", "image_path": "assets/output_3_touhou__medicine_melancholy__touhou_110.webp"}, {"prompt": "kantai_collection, i-19 (kanco<PERSON>), kantai collection", "chinese_prompt": "I-19 (舰队收藏)", "image_path": "assets/output_3_kantai_collection__i-19__kancolle___kantai_collection_111.webp"}, {"prompt": "kantai_collection, murasame (kancolle), kantai collection", "chinese_prompt": "村雨 (舰队收藏)", "image_path": "assets/output_3_kantai_collection__murasame__kancolle___kantai_collection_112.webp"}, {"prompt": "touhou, doremy sweet, touhou", "chinese_prompt": "哆来咪·苏伊特 (东方)", "image_path": "assets/output_3_touhou__doremy_sweet__touhou_113.webp"}, {"prompt": "fire_emblem, robin (fire emblem), fire emblem", "chinese_prompt": "罗宾 (外传) (圣火降魔录)", "image_path": "assets/output_3_fire_emblem__robin__fire_emblem___fire_emblem_114.webp"}, {"prompt": "jojo_no_kimyou_na_bouken, g<PERSON><PERSON>, jojo no kimyou na bouken", "chinese_prompt": "乔鲁诺·乔巴拿 (五乔) (JOJO的奇妙冒险)", "image_path": "assets/output_3_jojo_no_kimyou_na_bouken__gior<PERSON>_giovanna__jojo_no_kimyou_na_bouken_115.webp"}, {"prompt": "fatal_fury, shiranui mai, fatal fury", "chinese_prompt": "不知火舞 (饿狼传说)", "image_path": "assets/output_3_fatal_fury__shiranui_mai__fatal_fury_116.webp"}, {"prompt": "princess_connect!, kok<PERSON><PERSON> (princess connect!), princess connect!", "chinese_prompt": "枣可萝 (公主连结)", "image_path": "assets/output_3_princess_connect___kokkoro__princess_connect____princess_connect__117.webp"}, {"prompt": "idolmaster, futa<PERSON> <PERSON>zu, idolmaster", "chinese_prompt": "双叶杏 (灰姑娘) (偶像大师)", "image_path": "assets/output_3_idolmaster__futaba_anzu__idolmaster_118.webp"}, {"prompt": "ore_no_imouto_ga_konna_ni_kawaii_wake_ga_nai, gokou ruri, ore no imouto ga konna ni kawaii wake ga nai", "chinese_prompt": "五更琉璃 (我的妹妹不可能那么可爱)", "image_path": "assets/output_3_ore_no_imouto_ga_konna_ni_kawaii_wake_ga_nai__gokou_ruri__ore_no_imouto_ga_konna_ni_kawaii_wake_ga_nai_119.webp"}, {"prompt": "azur_lane, unicorn (azur lane), azur lane", "chinese_prompt": "独角兽 (碧蓝航线)", "image_path": "assets/output_3_azur_lane__unicorn__azur_lane___azur_lane_120.webp"}, {"prompt": "fate_(series), kama (fate), fate (series)", "chinese_prompt": "迦摩 (Fate)", "image_path": "assets/output_3_fate__series___kama__fate___fate__series__121.webp"}, {"prompt": "girls_und_panzer, kay (girls und panzer), girls und panzer", "chinese_prompt": "凯伊 (少女与战车)", "image_path": "assets/output_3_girls_und_panzer__kay__girls_und_panzer___girls_und_panzer_122.webp"}, {"prompt": "girls'_frontline, ump45 (girls' frontline), girls' frontline", "chinese_prompt": "UMP45 (少女前线)", "image_path": "assets/output_3_girls__frontline__ump45__girls__frontline___girls__frontline_123.webp"}, {"prompt": "vocaloid, yuki miku, vocaloid", "chinese_prompt": "雪初音 (Vocaloid)", "image_path": "assets/output_3_vocaloid__yuki_miku__vocaloid_124.webp"}, {"prompt": "persona, yu<PERSON> ma<PERSON> (persona 3), persona", "chinese_prompt": "结城理 (P3), (女神异闻录)", "image_path": "assets/output_3_persona__yuuki_makoto__persona_3___persona_125.webp"}, {"prompt": "go-toubun_no_hanayome, nakano miku, go-toubun no hanayome", "chinese_prompt": "中野三玖 (五等分的新娘)", "image_path": "assets/output_3_go-toubun_no_hanayome__nakano_miku__go-toubun_no_hanayome_126.webp"}, {"prompt": "shingeki_no_kyojin, mi<PERSON><PERSON> a<PERSON>man, shingeki no kyojin", "chinese_prompt": "米卡莎·阿卡曼 (进击的巨人)", "image_path": "assets/output_3_shingeki_no_kyojin__mikasa_ackerman__shingeki_no_kyojin_127.webp"}, {"prompt": "umamusume, satono diamond (umamusume), umamusume", "chinese_prompt": "里见光钻 (赛马娘)", "image_path": "assets/output_3_umamusume__satono_diamond__umamusume___umamusume_128.webp"}, {"prompt": "hololive, s<PERSON><PERSON><PERSON> fubuki (1st costume), hololive", "chinese_prompt": "白上吹雪 (1st服) (Hololive)", "image_path": "assets/output_3_hololive__shir<PERSON><PERSON>_fubuki__1st_costume___hololive_129.webp"}, {"prompt": "utau, adachi rei, utau", "chinese_prompt": "足立レイ (UTAU)", "image_path": "assets/output_3_utau__adachi_rei__utau_130.webp"}, {"prompt": "arknights, exusiai (arknights), arknights", "chinese_prompt": "能天使 (明日方舟)", "image_path": "assets/output_3_arknights__exusiai__arknights___arknights_131.webp"}, {"prompt": "danga<PERSON><PERSON><PERSON>_(series), <PERSON><PERSON><PERSON> ka<PERSON>, danga<PERSON><PERSON><PERSON> (series)", "chinese_prompt": "弹珠人, 赤松枫 (弹丸论破)", "image_path": "assets/output_3_danganronpa__series___akamatsu_kaede__danganronpa__series__132.webp"}, {"prompt": "fate_(series), florence nightingale (fate), fate (series)", "chinese_prompt": "南丁格尔 (Fate)", "image_path": "assets/output_3_fate__series___florence_nightingale__fate___fate__series__133.webp"}, {"prompt": "kantai_collection, yamakaze (kancolle), kantai collection", "chinese_prompt": "山风 (舰队收藏)", "image_path": "assets/output_3_kantai_collection__yamakaze__kancolle___kantai_collection_134.webp"}, {"prompt": "touh<PERSON>, lunasa prism<PERSON>, touhou", "chinese_prompt": "露娜萨・普莉兹姆利巴 (东方)", "image_path": "assets/output_3_touhou__lunasa_prismriver__touhou_135.webp"}, {"prompt": "re:zero_kara_hajimeru_isekai_seikatsu, emilia (re:zero), re:zero kara hajimeru isekai seikatsu", "chinese_prompt": "艾米莉娅 (Re:从零开始的异世界生活)", "image_path": "assets/output_3_re_zero_kara_hajimeru_isekai_seikatsu__emilia__re_zero___re_zero_kara_hajimeru_isekai_seikatsu_136.webp"}, {"prompt": "guilty_gear, bridget (guilty gear), guilty gear", "chinese_prompt": "布莉姬 (圣骑士之战)", "image_path": "assets/output_3_guilty_gear__bridget__guilty_gear___guilty_gear_137.webp"}, {"prompt": "one-punch_man, tatsu<PERSON><PERSON>, one-punch man", "chinese_prompt": "战栗的龙卷 (一拳超人)", "image_path": "assets/output_3_one-punch_man__tatsu<PERSON><PERSON>__one-punch_man_138.webp"}, {"prompt": "love_live!, takami chika, love live!", "chinese_prompt": "高海千歌 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_3_love_live___takami_chika__love_live__139.webp"}, {"prompt": "arknights, surtr (arknights), arknights", "chinese_prompt": "史尔特尔 (明日方舟)", "image_path": "assets/output_3_arknights__surtr__arknights___arknights_140.webp"}, {"prompt": "world_witches_series, sanya v. lit<PERSON>, world witches series", "chinese_prompt": "萨妮娅·V·利特维亚克 (强袭魔女)", "image_path": "assets/output_3_world_witches_series__sanya_v__litvyak__world_witches_series_141.webp"}, {"prompt": "girls_band_cry, iseri nina, girls band cry", "chinese_prompt": "井芹仁菜 (Girls Band Cry)", "image_path": "assets/output_3_girls_band_cry__iseri_nina__girls_band_cry_142.webp"}, {"prompt": "voiceroid, kizuna akari, voiceroid", "chinese_prompt": "绁星灯 (Vocaloid)", "image_path": "assets/output_3_voiceroid__kizuna_akari__voiceroid_143.webp"}, {"prompt": "touh<PERSON>, moto<PERSON> kos<PERSON>u, touhou", "chinese_prompt": "本居小铃 (东方)", "image_path": "assets/output_3_touhou__motoori_kosuzu__touhou_144.webp"}, {"prompt": "honkai_(series), sparkle (honkai: star rail), honkai (series)", "chinese_prompt": "花火 (崩坏: 星穹铁道) (崩坏)", "image_path": "assets/output_3_honkai__series___sparkle__honkai__star_rail___honkai__series__145.webp"}, {"prompt": "tiger_&_bunny, barnaby brooks jr., tiger & bunny", "chinese_prompt": "巴纳比·布鲁克斯二世 (TIGER & BUNNY)", "image_path": "assets/output_3_tiger___bunny__barnaby_brooks_jr___tiger___bunny_146.webp"}, {"prompt": "naru<PERSON>_(series), hyu<PERSON> hinata, naru<PERSON> (series)", "chinese_prompt": "日向雏田 (火影忍者)", "image_path": "assets/output_3_naruto__series___hyuuga_hinata__naruto__series__147.webp"}, {"prompt": "splatoon_(series), callie (splatoon), splatoon (series)", "chinese_prompt": "小拟 (斯普拉遁)", "image_path": "assets/output_3_splatoon__series___callie__splatoon___splatoon__series__148.webp"}, {"prompt": "kantai_collection, maya (kancolle), kantai collection", "chinese_prompt": "摩耶 (舰队收藏)", "image_path": "assets/output_3_kantai_collection__maya__kancolle___kantai_collection_149.webp"}, {"prompt": "touhou, usami sumireko, touhou", "chinese_prompt": "宇佐见堇子 (东方)", "image_path": "assets/output_3_touhou__usami_sumireko__touhou_150.webp"}, {"prompt": "persona, amamiya ren, persona", "chinese_prompt": "雨宫莲 (P5) (女神异闻录)", "image_path": "assets/output_3_persona__amamiya_ren__persona_151.webp"}, {"prompt": "toaru_majutsu_no_index, shirai kuroko, toaru majutsu no index", "chinese_prompt": "白井黑子 (魔法禁书目录)", "image_path": "assets/output_3_toaru_majutsu_no_index__shir<PERSON>_kuroko__toaru_majutsu_no_index_152.webp"}, {"prompt": "idolmaster, k<PERSON><PERSON><PERSON>, idolmaster", "chinese_prompt": "舆水幸子 (灰姑娘) (偶像大师)", "image_path": "assets/output_3_idolmaster__k<PERSON><PERSON><PERSON>_sa<PERSON><PERSON>__idolmaster_153.webp"}, {"prompt": "world_witches_series, e<PERSON>, world witches series", "chinese_prompt": "艾拉·伊尔玛塔尔·尤蒂莱南 (强袭魔女)", "image_path": "assets/output_3_world_witches_series__e<PERSON>_ilmatar_ju<PERSON><PERSON><PERSON>__world_witches_series_154.webp"}, {"prompt": "jojo_no_kimyou_na_bouken, j<PERSON><PERSON> j<PERSON><PERSON>, jojo no kimyou na bouken", "chinese_prompt": "乔纳森·乔斯达 (大乔) (JOJO的奇妙冒险)", "image_path": "assets/output_3_jojo_no_kimyou_na_bouken__jonathan_joestar__jojo_no_kimyou_na_bouken_155.webp"}, {"prompt": "lucky_star, <PERSON><PERSON><PERSON> tsu<PERSON>, lucky star", "chinese_prompt": "柊司 (幸运星)", "image_path": "assets/output_3_lucky_star__hi<PERSON><PERSON>_tsu<PERSON><PERSON>__lucky_star_156.webp"}, {"prompt": "lyrical_nanoha, yagami hayate, lyrical nanoha", "chinese_prompt": "八神疾风 (魔法少女奈叶)", "image_path": "assets/output_3_lyrical_nanoha__yagami_hayate__lyrical_nanoha_157.webp"}, {"prompt": "idolmaster, anast<PERSON><PERSON> (idolmaster), idolmaster", "chinese_prompt": "安娜斯塔西娅 (灰姑娘) (偶像大师)", "image_path": "assets/output_3_idolmaster__anastasia__idolmaster___idolmaster_158.webp"}, {"prompt": "girls_und_panzer, shima<PERSON> a<PERSON>u, girls und panzer", "chinese_prompt": "岛田爱里寿 (少女与战车)", "image_path": "assets/output_3_girls_und_panzer__shimada_arisu__girls_und_panzer_159.webp"}, {"prompt": "fate_(series), morgan le fay (fate), fate (series)", "chinese_prompt": "摩根- (Fate)", "image_path": "assets/output_3_fate__series___morgan_le_fay__fate___fate__series__160.webp"}, {"prompt": "kantai_collection, i-58 (kanco<PERSON>), kantai collection", "chinese_prompt": "I-58 (舰队收藏)", "image_path": "assets/output_3_kantai_collection__i-58__kancolle___kantai_collection_161.webp"}, {"prompt": "granblue_fantasy, narmaya (granblue fantasy), granblue fantasy", "chinese_prompt": "娜鲁梅亚 (碧蓝幻想)", "image_path": "assets/output_3_granblue_fantasy__narmaya__granblue_fantasy___granblue_fantasy_162.webp"}, {"prompt": "fire_emblem, corrin (female) (fire emblem), fire emblem", "chinese_prompt": "神威 (if) (女性) (圣火降魔录)", "image_path": "assets/output_3_fire_emblem__corrin__female___fire_emblem___fire_emblem_163.webp"}, {"prompt": "spice_and_wolf, holo, spice and wolf", "chinese_prompt": "赫萝 (狼与香辛料)", "image_path": "assets/output_3_spice_and_wolf__holo__spice_and_wolf_164.webp"}, {"prompt": "girls_und_panzer, katy<PERSON>a (girls und panzer), girls und panzer", "chinese_prompt": "喀秋莎 (少女与战车)", "image_path": "assets/output_3_girls_und_panzer__katyusha__girls_und_panzer___girls_und_panzer_165.webp"}, {"prompt": "fate_(series), jack the ripper (fate/apocrypha), fate (series)", "chinese_prompt": "开膛手杰克 (Fate/Apocrypha) (Fate)", "image_path": "assets/output_3_fate__series___jack_the_ripper__fate_apocrypha___fate__series__166.webp"}, {"prompt": "idolmaster, <PERSON><PERSON><PERSON> f<PERSON>, idolmaster", "chinese_prompt": "黛冬优子 (闪耀色彩) (偶像大师)", "image_path": "assets/output_3_idolmaster__may<PERSON><PERSON>_fuy<PERSON>o__idolmaster_167.webp"}, {"prompt": "princess_connect!, pecorine (princess connect!), princess connect!", "chinese_prompt": "冰川镜华 (公主连结)", "image_path": "assets/output_3_princess_connect___pecorine__princess_connect____princess_connect__168.webp"}, {"prompt": "danga<PERSON><PERSON><PERSON>_(series), en<PERSON><PERSON>o, danga<PERSON><PERSON><PERSON> (series)", "chinese_prompt": "江之岛盾子 (弹丸论破)", "image_path": "assets/output_3_danganronpa__series___enoshima_junko__danganronpa__series__169.webp"}, {"prompt": "higurashi_no_naku_koro_ni, ryu<PERSON><PERSON><PERSON> rena, higurashi no naku koro ni", "chinese_prompt": "龙宫蕾娜 (暮蝉悲鸣时)", "image_path": "assets/output_3_higurashi_no_naku_koro_ni__ryuuguu_rena__higurashi_no_naku_koro_ni_170.webp"}, {"prompt": "pokemon, leon (pokemon), pokemon", "chinese_prompt": "丹帝 (宝可梦)", "image_path": "assets/output_3_pokemon__leon__pokemon___pokemon_171.webp"}, {"prompt": "sword_art_online, kirito, sword art online", "chinese_prompt": "桐人 (刀剑神域)", "image_path": "assets/output_3_sword_art_online__kirito__sword_art_online_172.webp"}, {"prompt": "fate_(series), oda nobunaga (fate), fate (series)", "chinese_prompt": "织田信长 (Fate)", "image_path": "assets/output_3_fate__series___oda_nobunaga__fate___fate__series__173.webp"}, {"prompt": "pokemon, raihan (pokemon), pokemon", "chinese_prompt": "奇巴纳 (宝可梦)", "image_path": "assets/output_3_pokemon__raihan__pokemon___pokemon_174.webp"}, {"prompt": "blue_archive, ui (blue archive), blue archive", "chinese_prompt": "古关忧 (蔚蓝档案)", "image_path": "assets/output_3_blue_archive__ui__blue_archive___blue_archive_175.webp"}, {"prompt": "fate_(series), iskan<PERSON> (fate), fate (series)", "chinese_prompt": "亚历山大 (Fate)", "image_path": "assets/output_3_fate__series___iskandar__fate___fate__series__176.webp"}, {"prompt": "touhou, yo<PERSON><PERSON> shion, touhou", "chinese_prompt": "依神紫苑 (东方)", "image_path": "assets/output_3_touh<PERSON>__yo<PERSON><PERSON>_shion__touhou_177.webp"}, {"prompt": "umamusume, kitasan black (umamusume), umamusume", "chinese_prompt": "北部玄驹 (赛马娘)", "image_path": "assets/output_3_umamusume__kitasan_black__umamusume___umamusume_178.webp"}, {"prompt": "fate_(series), kotomine kirei, fate (series)", "chinese_prompt": "言峰绮礼 (Fate)", "image_path": "assets/output_3_fate__series___kotomine_kirei__fate__series__179.webp"}, {"prompt": "rwby, ruby rose, rwby", "chinese_prompt": "露比·萝丝 (RWBY)", "image_path": "assets/output_3_rwby__ruby_rose__rwby_180.webp"}, {"prompt": "tiger_&_bunny, kabur<PERSON> t. kotetsu, tiger & bunny", "chinese_prompt": "镝木·T·虎彻 (TIGER & BUNNY)", "image_path": "assets/output_3_tiger___bunny__kaburagi_t__kotetsu__tiger___bunny_181.webp"}, {"prompt": "genshin_impact, yoimiya (genshin impact), genshin impact", "chinese_prompt": "宵宫 (原神)", "image_path": "assets/output_3_genshin_impact__yoimiya__genshin_impact___genshin_impact_182.webp"}, {"prompt": "touhou, star sapphire, touhou", "chinese_prompt": "星莲船 (东方)", "image_path": "assets/output_3_touhou__star_sapphire__touhou_183.webp"}, {"prompt": "overwatch, mercy (overwatch), overwatch", "chinese_prompt": "慈悲 (斗阵特攻)", "image_path": "assets/output_3_overwatch__mercy__overwatch___overwatch_184.webp"}, {"prompt": "kantai_collection, akigumo (kancolle), kantai collection", "chinese_prompt": "秋云 (舰队收藏)", "image_path": "assets/output_3_kantai_collection__akigumo__kancolle___kantai_collection_185.webp"}, {"prompt": "arknights, mudrock (arknights), arknights", "chinese_prompt": "泥岩 (明日方舟)", "image_path": "assets/output_3_arknights__mudrock__arknights___arknights_186.webp"}, {"prompt": "persona, shiro<PERSON><PERSON> na<PERSON>, persona", "chinese_prompt": "白钟直斗 (P4) (女神异闻录)", "image_path": "assets/output_3_persona__shirogane_naoto__persona_187.webp"}, {"prompt": "kemono_friends, common raccoon (kemono friends), kemono friends", "chinese_prompt": "浣熊 (动物朋友)", "image_path": "assets/output_3_kemono_friends__common_raccoon__kemono_friends___kemono_friends_188.webp"}, {"prompt": "arknights, w (arknights), arknights", "chinese_prompt": "W (明日方舟)", "image_path": "assets/output_3_arknights__w__arknights___arknights_189.webp"}, {"prompt": "street_fighter, han juri, street fighter", "chinese_prompt": "韩蛛俐 (快打旋风)", "image_path": "assets/output_3_street_fighter__han_juri__street_fighter_190.webp"}, {"prompt": "hololive, don-chan (usada pekora), hololive", "chinese_prompt": "<PERSON><PERSON><PERSON><PERSON> (兔田佩克拉) (Hololive)", "image_path": "assets/output_3_hololive__don-chan__usada_pekora___hololive_191.webp"}, {"prompt": "azur_lane, ayana<PERSON> (azur lane), azur lane", "chinese_prompt": "绫波 (碧蓝航线)", "image_path": "assets/output_3_azur_lane__ayanami__azur_lane___azur_lane_192.webp"}, {"prompt": "hololive, irys (hololive), hololive", "chinese_prompt": "埃莉丝 (IRyS) (Hololive)", "image_path": "assets/output_3_hololive__irys__hololive___hololive_193.webp"}, {"prompt": "umamusume, special week (umamusume), umamusume", "chinese_prompt": "特别周 (赛马娘)", "image_path": "assets/output_3_umamusume__special_week__umamusume___umamusume_194.webp"}, {"prompt": "love_live!, hi<PERSON><PERSON> kaho, love live!", "chinese_prompt": "日野下花帆 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_3_love_live___hi<PERSON><PERSON>_ka<PERSON>__love_live__195.webp"}, {"prompt": "toradora!, aisaka taiga, toradora!", "chinese_prompt": "逢坂大河 (虎与龙)", "image_path": "assets/output_3_toradora___aisaka_taiga__toradora__196.webp"}, {"prompt": "splatoon_(series), marie (splatoon), splatoon (series)", "chinese_prompt": "小萤萤 (斯普拉遁)", "image_path": "assets/output_3_splatoon__series___marie__splatoon___splatoon__series__197.webp"}, {"prompt": "kantai_collection, z1 leberecht maass (kancolle), kantai collection", "chinese_prompt": "Z1 (莱伯勒希特·马斯) (舰队收藏)", "image_path": "assets/output_3_kantai_collection__z1_le<PERSON><PERSON><PERSON>_maass__kancolle___kantai_collection_198.webp"}, {"prompt": "gochuumon_wa_usagi_desu_ka?, hoto cocoa, gochuumon wa usagi desu ka?", "chinese_prompt": "保登心爱 (请问您今天要来点兔子吗?)", "image_path": "assets/output_3_gochuumon_wa_usagi_desu_ka___hoto_cocoa__gochuumon_wa_usagi_desu_ka__199.webp"}, {"prompt": "toaru_majutsu_no_index, sho<PERSON><PERSON> misaki, toaru majutsu no index", "chinese_prompt": "食蜂操祈 (魔法禁书目录)", "image_path": "assets/output_3_toaru_majutsu_no_index__shoku<PERSON>_misaki__toaru_majutsu_no_index_200.webp"}, {"prompt": "idolmaster, j<PERSON><PERSON><PERSON> rika, idolmaster", "chinese_prompt": "城崎莉嘉 (灰姑娘) (偶像大师)", "image_path": "assets/output_3_idolmaster__j<PERSON><PERSON><PERSON>_rika__idolmaster_201.webp"}, {"prompt": "fate_(series), artoria pendragon (lancer) (fate), fate (series)", "chinese_prompt": "阿尔托莉亚·潘德拉贡 (枪兵) (Fate)", "image_path": "assets/output_3_fate__series___artoria_pendragon__lancer___fate___fate__series__202.webp"}, {"prompt": "tengen_toppa_gurren_lagann, nia te<PERSON>, tengen toppa gurren lagann", "chinese_prompt": "妮亚·泰佩林 (天元突破 红莲螺岩)", "image_path": "assets/output_3_tengen_toppa_gurren_lagann__nia_teppelin__tengen_toppa_gurren_lagann_203.webp"}, {"prompt": "blue_archive, hifumi (blue archive), blue archive", "chinese_prompt": "阿慈谷日富美 (蔚蓝档案)", "image_path": "assets/output_3_blue_archive__hifumi__blue_archive___blue_archive_204.webp"}, {"prompt": "fate_(series), waver velvet, fate (series)", "chinese_prompt": "韦伯·维尔维特 (Fate)", "image_path": "assets/output_4_fate__series___waver_velvet__fate__series__0.webp"}, {"prompt": "pokemon, nessa (pokemon), pokemon", "chinese_prompt": "露璃娜 (宝可梦)", "image_path": "assets/output_4_pokemon__nessa__pokemon___pokemon_1.webp"}, {"prompt": "kantai_collection, taihou (kancolle), kantai collection", "chinese_prompt": "大凤 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__taihou__kancolle___kantai_collection_2.webp"}, {"prompt": "blue_archive, shiroko (swimsuit) (blue archive), blue archive", "chinese_prompt": "砂狼白子 (泳装) (蔚蓝档案)", "image_path": "assets/output_4_blue_archive__shiroko__swimsuit___blue_archive___blue_archive_3.webp"}, {"prompt": "umamusume, symboli rudo<PERSON> (umamusume), umamusume", "chinese_prompt": "鲁道夫象征 (赛马娘)", "image_path": "assets/output_4_umamusume__symboli_rudolf__umamusume___umamusume_4.webp"}, {"prompt": "yu-gi-oh!, dark magician girl, yu-gi-oh!", "chinese_prompt": "黑魔导女孩 (游戏王)", "image_path": "assets/output_4_yu-gi-oh___dark_magician_girl__yu-gi-oh__5.webp"}, {"prompt": "rwby, weiss schnee, rwby", "chinese_prompt": "怀丝·雪倪 (RWBY)", "image_path": "assets/output_4_rwby__weiss_schnee__rwby_6.webp"}, {"prompt": "hololive, hakui koyori, hololive", "chinese_prompt": "博衣小夜璃 ((Hololive))", "image_path": "assets/output_4_hololive__hakui_koyori__hololive_7.webp"}, {"prompt": "pokemon, rotom, pokemon", "chinese_prompt": "洛托姆 (宝可梦)", "image_path": "assets/output_4_pokemon__rotom__pokemon_8.webp"}, {"prompt": "kantai_collection, z3 max schultz (kancolle), kantai collection", "chinese_prompt": "Z3 (马克斯·舒尔兹) (舰队收藏)", "image_path": "assets/output_4_kantai_collection__z3_max_schultz__kancolle___kantai_collection_9.webp"}, {"prompt": "genshin_impact, diluc (genshin impact), genshin impact", "chinese_prompt": "迪卢克 (原神)", "image_path": "assets/output_4_genshin_impact__diluc__genshin_impact___genshin_impact_10.webp"}, {"prompt": "go-toubun_no_hanayome, nakano yotsuba, go-toubun no hanayome", "chinese_prompt": "中野四叶 (五等分的新娘)", "image_path": "assets/output_4_go-toubun_no_hanayome__nakano_yotsuba__go-toubun_no_hanayome_11.webp"}, {"prompt": "pokemon, kieran (pokemon), pokemon", "chinese_prompt": "乌栗 (宝可梦)", "image_path": "assets/output_4_pokemon__kieran__pokemon___pokemon_12.webp"}, {"prompt": "jojo_no_kimyou_na_bouken, hi<PERSON><PERSON><PERSON> josuke, jojo no kimyou na bouken", "chinese_prompt": "东方仗助 (JOJO的奇妙冒险)", "image_path": "assets/output_4_jojo_no_kimyou_na_bouken__higas<PERSON><PERSON>_josuke__jojo_no_kimyou_na_bouken_13.webp"}, {"prompt": "alice_in_wonderland, alice (alice in wonderland), alice in wonderland", "chinese_prompt": "爱丽丝 (爱丽丝梦游仙境)", "image_path": "assets/output_4_alice_in_wonderland__alice__alice_in_wonderland___alice_in_wonderland_14.webp"}, {"prompt": "bang_dream!, chihaya anon, bang dream!", "chinese_prompt": "千早爱音 (BanG Dream!)", "image_path": "assets/output_4_bang_dream___chihaya_anon__bang_dream__15.webp"}, {"prompt": "spy_x_family, twilight (spy x family), spy x family", "chinese_prompt": "洛伊德 (黄昏) (SPY x FAMILY 间谍家家酒)", "image_path": "assets/output_4_spy_x_family__twilight__spy_x_family___spy_x_family_16.webp"}, {"prompt": "pokemon, bea (pokemon), pokemon", "chinese_prompt": "彩豆 (宝可梦)", "image_path": "assets/output_4_pokemon__bea__pokemon___pokemon_17.webp"}, {"prompt": "macross, sheryl nome, macross", "chinese_prompt": "雪露·诺姆 (F) (超时空要塞)", "image_path": "assets/output_4_macross__sheryl_nome__macross_18.webp"}, {"prompt": "girls_und_panzer, reizei mako, girls und panzer", "chinese_prompt": "冷泉麻子 (少女与战车)", "image_path": "assets/output_4_girls_und_panzer__reizei_mako__girls_und_panzer_19.webp"}, {"prompt": "pokemon, akari (pokemon), pokemon", "chinese_prompt": "小照 (宝可梦)", "image_path": "assets/output_4_pokemon__akari__pokemon___pokemon_20.webp"}, {"prompt": "blue_archive, karin (bunny) (blue archive), blue archive", "chinese_prompt": "角楯花凛 (泳装)  (蔚蓝档案)", "image_path": "assets/output_4_blue_archive__karin__bunny___blue_archive___blue_archive_21.webp"}, {"prompt": "kantai_collection, warspite (kancolle), kantai collection", "chinese_prompt": "厌战 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__warspite__kancolle___kantai_collection_22.webp"}, {"prompt": "blue_archive, neru (blue archive), blue archive", "chinese_prompt": "美甘宁瑠 (蔚蓝档案)", "image_path": "assets/output_4_blue_archive__neru__blue_archive___blue_archive_23.webp"}, {"prompt": "steins;gate, makise kurisu, steins;gate", "chinese_prompt": "牧瀬红莉栖 (命运石之门)", "image_path": "assets/output_4_steins_gate__makise_kurisu__steins_gate_24.webp"}, {"prompt": "touhou, pyonta, touhou", "chinese_prompt": "泄矢诹访子 pyonta (青蛙帽) (东方)", "image_path": "assets/output_4_touh<PERSON>__pyonta__touhou_25.webp"}, {"prompt": "idolmaster, <PERSON><PERSON><PERSON>, idolmaster", "chinese_prompt": "三浦梓 (百万现场) (偶像大师)", "image_path": "assets/output_4_idolmaster__mi<PERSON>_a<PERSON><PERSON>__idolmaster_26.webp"}, {"prompt": "kantai_collection, oboro (kancolle), kantai collection", "chinese_prompt": "胧 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__oboro__kancolle___kantai_collection_27.webp"}, {"prompt": "blue_archive, nonomi (blue archive), blue archive", "chinese_prompt": "十六夜野乃美 (蔚蓝档案)", "image_path": "assets/output_4_blue_archive__nonomi__blue_archive___blue_archive_28.webp"}, {"prompt": "kantai_collection, kuma (kancolle), kantai collection", "chinese_prompt": "球磨 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__kuma__kancolle___kantai_collection_29.webp"}, {"prompt": "touhou, luna child, touhou", "chinese_prompt": "露娜切露德 (东方)", "image_path": "assets/output_4_touhou__luna_child__touhou_30.webp"}, {"prompt": "kantai_collection, fusou (kancolle), kantai collection", "chinese_prompt": "扶桑 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__fusou__kancolle___kantai_collection_31.webp"}, {"prompt": "honkai_(series), caelus (honkai: star rail), honkai (series)", "chinese_prompt": "穹 (崩坏: 星穹铁道) (崩坏)", "image_path": "assets/output_4_honkai__series___caelus__honkai__star_rail___honkai__series__32.webp"}, {"prompt": "one_piece, sanji (one piece), one piece", "chinese_prompt": "香吉士 (海贼王)", "image_path": "assets/output_4_one_piece__sanji__one_piece___one_piece_33.webp"}, {"prompt": "kantai_collection, aoba (kancolle), kantai collection", "chinese_prompt": "青叶 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__aoba__kancolle___kantai_collection_34.webp"}, {"prompt": "tsu<PERSON><PERSON><PERSON>, arc<PERSON><PERSON> brunest<PERSON>, tsu<PERSON><PERSON>e", "chinese_prompt": "爱尔奎特·布伦史塔德 (真月谭－月姬)", "image_path": "assets/output_4_tsukihime__arcueid_brunestud__tsukihime_35.webp"}, {"prompt": "idolmaster, ha<PERSON><PERSON> y<PERSON><PERSON>, idolmaster", "chinese_prompt": "萩原雪步 (偶像大师)", "image_path": "assets/output_4_idolmaster__hagi<PERSON>_y<PERSON><PERSON>__idolmaster_36.webp"}, {"prompt": "blue_archive, miyako (blue archive), blue archive", "chinese_prompt": "月雪宫子 (蔚蓝档案)", "image_path": "assets/output_4_blue_archive__miyako__blue_archive___blue_archive_37.webp"}, {"prompt": "kantai_collection, kumano (kancolle), kantai collection", "chinese_prompt": "熊野 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__kumano__kancolle___kantai_collection_38.webp"}, {"prompt": "kantai_collection, i-401 (kancolle), kantai collection", "chinese_prompt": "I-401 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__i-401__kancolle___kantai_collection_39.webp"}, {"prompt": "idolmaster, ma<PERSON><PERSON> miku, idolmaster", "chinese_prompt": "前川未来 (灰姑娘) (偶像大师)", "image_path": "assets/output_4_idolmaster__ma<PERSON><PERSON>_miku__idolmaster_40.webp"}, {"prompt": "jujutsu_kaisen, it<PERSON><PERSON> yuuji, jujutsu kaisen", "chinese_prompt": "虎杖悠仁 (咒术回战)", "image_path": "assets/output_4_jujutsu_kaisen__itadori_yuuji__jujutsu_kaisen_41.webp"}, {"prompt": "fate_(series), elizabeth bathory (fate), fate (series)", "chinese_prompt": "伊莉莎白・巴托里 (Fate)", "image_path": "assets/output_4_fate__series___elizabeth_bathory__fate___fate__series__42.webp"}, {"prompt": "fate_(series), nitocris (fate), fate (series)", "chinese_prompt": "尼托克丽丝 (Fate)", "image_path": "assets/output_4_fate__series___nitocris__fate___fate__series__43.webp"}, {"prompt": "azur_lane, illustrious (azur lane), azur lane", "chinese_prompt": "光辉 (碧蓝航线)", "image_path": "assets/output_4_azur_lane__illustrious__azur_lane___azur_lane_44.webp"}, {"prompt": "kantai_collection, tokitsukaze (kancolle), kantai collection", "chinese_prompt": "时津风 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__to<PERSON><PERSON><PERSON><PERSON>__kancolle___kantai_collection_45.webp"}, {"prompt": "blue_archive, yuuka (track) (blue archive), blue archive", "chinese_prompt": "早濑优香 (体操服)  (蔚蓝档案)", "image_path": "assets/output_4_blue_archive__yuuka__track___blue_archive___blue_archive_46.webp"}, {"prompt": "love_live!, otomune kozue, love live!", "chinese_prompt": "乙宗梢 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_4_love_live___otomune_kozue__love_live__47.webp"}, {"prompt": "neon_genesis_evangelion, nagisa kawo<PERSON>, neon genesis evangelion", "chinese_prompt": "渚薰 (新世纪福音战士)", "image_path": "assets/output_4_neon_genesis_evangelion__nagisa_ka<PERSON><PERSON>__neon_genesis_evangelion_48.webp"}, {"prompt": "persona, satonaka chie, persona", "chinese_prompt": "里中千枝 (P4) (女神异闻录)", "image_path": "assets/output_4_persona__satonaka_chie__persona_49.webp"}, {"prompt": "kemono_friends, fennec (kemono friends), kemono friends", "chinese_prompt": "耳廓狐 (动物朋友)", "image_path": "assets/output_4_kemono_friends__fennec__kemono_friends___kemono_friends_50.webp"}, {"prompt": "machikado_mazoku, yo<PERSON><PERSON> yu<PERSON> (machikado mazoku), machikado mazoku", "chinese_prompt": "", "image_path": "assets/output_4_machikado_mazoku__yoshida_yuuko__machikado_mazoku___machikado_mazoku_51.webp"}, {"prompt": "splatoon_(series), inkling boy, splatoon (series)", "chinese_prompt": "男孩角色 (斯普拉遁)", "image_path": "assets/output_4_splatoon__series___inkling_boy__splatoon__series__52.webp"}, {"prompt": "guilty_gear, dizzy (guilty gear), guilty gear", "chinese_prompt": "蒂姬 (圣骑士之战)", "image_path": "assets/output_4_guilty_gear__dizzy__guilty_gear___guilty_gear_53.webp"}, {"prompt": "touhou, sunny milk, touhou", "chinese_prompt": "桑尼·米尔克 (东方)", "image_path": "assets/output_4_touhou__sunny_milk__touhou_54.webp"}, {"prompt": "genshin_impact, amber (genshin impact), genshin impact", "chinese_prompt": "安柏 (原神)", "image_path": "assets/output_4_genshin_impact__amber__genshin_impact___genshin_impact_55.webp"}, {"prompt": "code_geass, lelouch vi britannia, code geass", "chinese_prompt": "鲁路修·兰佩洛基 (Code Geass)", "image_path": "assets/output_4_code_geass__lelouch_vi_britannia__code_geass_56.webp"}, {"prompt": "fate_(series), kiyo<PERSON>e (fate), fate (series)", "chinese_prompt": "清姫 (Fate)", "image_path": "assets/output_4_fate__series___kiyohime__fate___fate__series__57.webp"}, {"prompt": "kimetsu_no_ya<PERSON>, ka<PERSON>o <PERSON>, kimetsu no yaiba", "chinese_prompt": "灶门祢豆子 (鬼灭之刃)", "image_path": "assets/output_4_kimetsu_no_yaiba__kamado_nezuko__kimetsu_no_yaiba_58.webp"}, {"prompt": "oshi_no_ko, arima kana, oshi no ko", "chinese_prompt": "有马佳奈 (我推的孩子)", "image_path": "assets/output_4_oshi_no_ko__arima_kana__oshi_no_ko_59.webp"}, {"prompt": "needy_girl_overdose, ch<PERSON><PERSON><PERSON><PERSON><PERSON> tenshi-chan, needy girl overdose", "chinese_prompt": "超绝最可爱天使酱 (主播女孩重度依赖)", "image_path": "assets/output_4_needy_girl_overdose__chou<PERSON><PERSON><PERSON><PERSON>_tenshi-chan__needy_girl_overdose_60.webp"}, {"prompt": "shingeki_no_kyojin, eren yeager, shingeki no kyojin", "chinese_prompt": "艾连·叶卡 (进击的巨人)", "image_path": "assets/output_4_shingeki_no_kyojin__eren_yeager__shingeki_no_kyojin_61.webp"}, {"prompt": "eromanga_sensei, i<PERSON><PERSON>, eromanga sensei", "chinese_prompt": "和泉纱雾 (情色漫画老师)", "image_path": "assets/output_4_eromanga_sensei__i<PERSON><PERSON>_sagiri__eromanga_sensei_62.webp"}, {"prompt": "omori, basil (omori), omori", "chinese_prompt": "BASIL (Omori)", "image_path": "assets/output_4_omori__basil__omori___omori_63.webp"}, {"prompt": "kantai_collection, satsuki (kancolle), kantai collection", "chinese_prompt": "皋月 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__satsuki__kancolle___kantai_collection_64.webp"}, {"prompt": "kill_la_kill, mankan<PERSON><PERSON> mako, kill la kill", "chinese_prompt": "满舰饰真子 (斩服少女)", "image_path": "assets/output_4_kill_la_kill__mankansh<PERSON>_mako__kill_la_kill_65.webp"}, {"prompt": "honkai_(series), aventurine (honkai: star rail), honkai (series)", "chinese_prompt": "砂金 (崩坏: 星穹铁道) (崩坏)", "image_path": "assets/output_4_honkai__series___aventurine__honkai__star_rail___honkai__series__66.webp"}, {"prompt": "hololive, watson amelia (1st costume), hololive", "chinese_prompt": "华生·艾米莉亚 (1st服) (Hololive)", "image_path": "assets/output_4_hololive__watson_amelia__1st_costume___hololive_67.webp"}, {"prompt": "ni<PERSON><PERSON><PERSON>, tsu<PERSON>o mito, ni<PERSON><PERSON><PERSON>", "chinese_prompt": "月之美兔 (彩虹社)", "image_path": "assets/output_4_niji<PERSON><PERSON>__tsukino_mito__niji<PERSON>ji_68.webp"}, {"prompt": "saibou_shinkyoku, u<PERSON><PERSON><PERSON>, saibou shinkyoku", "chinese_prompt": "宇津木 德幸 (细胞神曲)", "image_path": "assets/output_4_saibou_shinkyoku__utsu<PERSON>_nor<PERSON><PERSON>__saibou_shinkyoku_69.webp"}, {"prompt": "kantai_collection, atlanta (kancolle), kantai collection", "chinese_prompt": "亚特兰大 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__atlanta__kancolle___kantai_collection_70.webp"}, {"prompt": "link!_like!_love_live!, fu<PERSON><PERSON> megumi, link! like! love live!", "chinese_prompt": "藤岛慈 (Link! Like! Love Live!)", "image_path": "assets/output_4_link__like__love_live___fuji<PERSON>_megumi__link__like__love_live__71.webp"}, {"prompt": "kantai_collection, mogami (kancolle), kantai collection", "chinese_prompt": "最上 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__mogami__kancolle___kantai_collection_72.webp"}, {"prompt": "kantai_collection, uzuki (kancolle), kantai collection", "chinese_prompt": "卯月 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__u<PERSON>__kancolle___kantai_collection_73.webp"}, {"prompt": "kantai_collection, ashigara (kancolle), kantai collection", "chinese_prompt": "足柄 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__ashigara__kancolle___kantai_collection_74.webp"}, {"prompt": "girls_und_panzer, boko (girls und panzer), girls und panzer", "chinese_prompt": "博科 (少女与战车)", "image_path": "assets/output_4_girls_und_panzer__boko__girls_und_panzer___girls_und_panzer_75.webp"}, {"prompt": "umine<PERSON>_no_naku_koro_ni, beatrice (um<PERSON><PERSON>), umineko no naku koro ni", "chinese_prompt": "贝阿朵莉切 (海猫鸣泣之时)", "image_path": "assets/output_4_umine<PERSON>_no_naku_koro_ni__beatrice__umine<PERSON>___umine<PERSON>_no_naku_koro_ni_76.webp"}, {"prompt": "precure, midorikawa nao, precure", "chinese_prompt": "绿川直 (光之美少女)", "image_path": "assets/output_4_precure__midorikawa_nao__precure_77.webp"}, {"prompt": "girls_band_cry, ka<PERSON><PERSON> momoka, girls band cry", "chinese_prompt": "河原木桃香 (Girls Band Cry)", "image_path": "assets/output_4_girls_band_cry__ka<PERSON><PERSON>_momoka__girls_band_cry_78.webp"}, {"prompt": "code_geass, kouzuki kallen, code geass", "chinese_prompt": "卡莲·修坦费尔特 (Code Geass)", "image_path": "assets/output_4_code_geass__k<PERSON><PERSON>_kallen__code_geass_79.webp"}, {"prompt": "mahou_shoujo_madoka_magica, charlotte (madoka magica), mahou shoujo madoka magica", "chinese_prompt": "夏洛特 魔法少女小圆", "image_path": "assets/output_4_mahou_shoujo_madoka_magica__charlotte__madoka_magica___mahou_shoujo_madoka_magica_80.webp"}, {"prompt": "mario<PERSON>(series), rosalina, mario (series)", "chinese_prompt": "罗洁塔 (超级玛利欧)", "image_path": "assets/output_4_mario__series___rosalina__mario__series__81.webp"}, {"prompt": "honkai_(series), silver wolf (honkai: star rail), honkai (series)", "chinese_prompt": "银狼 (崩坏: 星穹铁道) (崩坏)", "image_path": "assets/output_4_honkai__series___silver_wolf__honkai__star_rail___honkai__series__82.webp"}, {"prompt": "voiceroid, touhoku kiritan, voiceroid", "chinese_prompt": "东北切蒲英 (Vocaloid)", "image_path": "assets/output_4_voiceroid__to<PERSON><PERSON>_kiritan__voiceroid_83.webp"}, {"prompt": "genshin_impact, qiqi (genshin impact), genshin impact", "chinese_prompt": "七七 (原神)", "image_path": "assets/output_4_genshin_impact__qiqi__genshin_impact___genshin_impact_84.webp"}, {"prompt": "kantai_collection, non-human admiral (kancolle), kantai collection", "chinese_prompt": "狗提督 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__non-human_admiral__ka<PERSON><PERSON>___kantai_collection_85.webp"}, {"prompt": "hololive, kazama i<PERSON>ha, hololive", "chinese_prompt": "风真伊吕波 (Hololive)", "image_path": "assets/output_4_hololive__ka<PERSON>a_i<PERSON><PERSON>__hololive_86.webp"}, {"prompt": "pokemon, ethan (pokemon), pokemon", "chinese_prompt": "阿响 (宝可梦)", "image_path": "assets/output_4_pokemon__ethan__pokemon___pokemon_87.webp"}, {"prompt": "xenoblade_chronicles_(series), nia (xenoblade), xenoblade chronicles (series)", "chinese_prompt": "妮雅 (异度神剑)", "image_path": "assets/output_4_xenoblade_chronicles__series___nia__xenoblade___xenoblade_chronicles__series__88.webp"}, {"prompt": "final_fantasy, sephiroth, final fantasy", "chinese_prompt": "塞菲罗斯 (ff7) (最终幻想)", "image_path": "assets/output_4_final_fantasy__sephiroth__final_fantasy_89.webp"}, {"prompt": "idolmaster, hay<PERSON> kanade, idolmaster", "chinese_prompt": "速水奏 (灰姑娘) (偶像大师)", "image_path": "assets/output_4_idolmaster__hayami_kanade__idolmaster_90.webp"}, {"prompt": "kantai_collection, urakaze (kancolle), kantai collection", "chinese_prompt": "浦风 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__urak<PERSON>e__kancolle___kantai_collection_91.webp"}, {"prompt": "kantai_collection, hyuuga (kancolle), kantai collection", "chinese_prompt": "日向 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__hyuuga__kancolle___kantai_collection_92.webp"}, {"prompt": "girls_und_panzer, mika (girls und panzer), girls und panzer", "chinese_prompt": "米卡 (少女与战车)", "image_path": "assets/output_4_girls_und_panzer__mika__girls_und_panzer___girls_und_panzer_93.webp"}, {"prompt": "fate_(series), artoria caster (fate), fate (series)", "chinese_prompt": "阿尔托莉亚 (caster) (Fate)", "image_path": "assets/output_4_fate__series___artoria_caster__fate___fate__series__94.webp"}, {"prompt": "kantai_collection, kagerou (kancolle), kantai collection", "chinese_prompt": "阳炎 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__ka<PERSON><PERSON>__kancolle___kantai_collection_95.webp"}, {"prompt": "fate_(series), tomoe gozen (fate), fate (series)", "chinese_prompt": "巴御前 (Fate)", "image_path": "assets/output_4_fate__series___tomoe_gozen__fate___fate__series__96.webp"}, {"prompt": "kantai_collection, kiyoshi<PERSON> (kancolle), kantai collection", "chinese_prompt": "清霜 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__k<PERSON><PERSON><PERSON>__kancolle___kantai_collection_97.webp"}, {"prompt": "kantai_collection, shiratsuyu (kancolle), kantai collection", "chinese_prompt": "白露 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__shiratsuyu__kancolle___kantai_collection_98.webp"}, {"prompt": "re:zero_kara_hajimeru_isekai_seikatsu, ram (re:zero), re:zero kara hajimeru isekai seikatsu", "chinese_prompt": "拉姆 (Re:从零开始的异世界生活)", "image_path": "assets/output_4_re_zero_kara_hajimeru_isekai_seikatsu__ram__re_zero___re_zero_kara_hajimeru_isekai_seikatsu_99.webp"}, {"prompt": "higu<PERSON>i_no_naku_koro_ni, hou<PERSON> satoko, higurashi no naku koro ni", "chinese_prompt": "北条纱都子 (暮蝉悲鸣时)", "image_path": "assets/output_4_higurashi_no_naku_koro_ni__hou<PERSON>_satoko__higurashi_no_naku_koro_ni_100.webp"}, {"prompt": "naru<PERSON>_(series), <PERSON><PERSON><PERSON> sasuke, naru<PERSON> (series)", "chinese_prompt": "宇智波佐助 (火影忍者)", "image_path": "assets/output_4_naruto__series___u<PERSON><PERSON>_sasuke__naruto__series__101.webp"}, {"prompt": "gochuumon_wa_usagi_desu_ka?, kirima syaro, gochuumon wa usagi desu ka?", "chinese_prompt": "桐间纱路 (请问您今天要来点兔子吗?)", "image_path": "assets/output_4_gochuumon_wa_usagi_desu_ka___kirima_syaro__gochuumon_wa_usagi_desu_ka__102.webp"}, {"prompt": "precure, kise yayoi, precure", "chinese_prompt": "黄濑弥生 (光之美少女)", "image_path": "assets/output_4_precure__kise_yayoi__precure_103.webp"}, {"prompt": "idolmaster, a<PERSON><PERSON> r<PERSON>, idolmaster", "chinese_prompt": "秋月律子 (偶像大师)", "image_path": "assets/output_4_idolmaster__a<PERSON><PERSON>_r<PERSON><PERSON><PERSON>__idolmaster_104.webp"}, {"prompt": "hololive, tako<PERSON><PERSON> (ninomae ina'nis), hololive", "chinese_prompt": "<PERSON><PERSON><PERSON><PERSON> (一伊那尔栖) (Hololive)", "image_path": "assets/output_4_hololive__takodachi__ninomae_ina_nis___hololive_105.webp"}, {"prompt": "pokemon, rowlet, pokemon", "chinese_prompt": "木木枭 (宝可梦)", "image_path": "assets/output_4_pokemon__rowlet__pokemon_106.webp"}, {"prompt": "fate_(series), miyu ed<PERSON>, fate (series)", "chinese_prompt": "美游·艾德费尔特 (Fate)", "image_path": "assets/output_4_fate__series___miyu_ed<PERSON><PERSON>t__fate__series__107.webp"}, {"prompt": "dangan<PERSON><PERSON>_(series), ha<PERSON><PERSON> maki, dangan<PERSON><PERSON> (series)", "chinese_prompt": "春川魔姬 (弹丸论破)", "image_path": "assets/output_4_danganronpa__series___harukawa_maki__danganronpa__series__108.webp"}, {"prompt": "touhou, yorig<PERSON> jo'on, touhou", "chinese_prompt": "依神女苑 (东方)", "image_path": "assets/output_4_touh<PERSON>__yorig<PERSON>_jo_on__touhou_109.webp"}, {"prompt": "kantai_collection, jintsuu (kancolle), kantai collection", "chinese_prompt": "神通 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__jintsuu__kancolle___kantai_collection_110.webp"}, {"prompt": "boku_no_hero_academia, toga himiko, boku no hero academia", "chinese_prompt": "渡我被身子 (我的英雄学院)", "image_path": "assets/output_4_boku_no_hero_academia__toga_himiko__boku_no_hero_academia_111.webp"}, {"prompt": "honkai_(series), robin (honkai: star rail), honkai (series)", "chinese_prompt": "知更鸟 (崩坏: 星穹铁道) (崩坏)", "image_path": "assets/output_4_honkai__series___robin__honkai__star_rail___honkai__series__112.webp"}, {"prompt": "hololive, omaru polka, hololive", "chinese_prompt": "尾丸波尔卡 (Hololive)", "image_path": "assets/output_4_hololive__omaru_polka__hololive_113.webp"}, {"prompt": "touhou, unzan, touhou", "chinese_prompt": "云山 (东方)", "image_path": "assets/output_4_touhou__unzan__touhou_114.webp"}, {"prompt": "ore_no_imouto_ga_konna_ni_kawaii_wake_ga_nai, k<PERSON><PERSON> kirino, ore no imouto ga konna ni kawaii wake ga nai", "chinese_prompt": "高坂桐乃 (我的妹妹不可能那么可爱)", "image_path": "assets/output_4_ore_no_imouto_ga_konna_ni_kawaii_wake_ga_nai__kousaka_kirino__ore_no_imouto_ga_konna_ni_kawaii_wake_ga_nai_115.webp"}, {"prompt": "pokemon, lana (pokemon), pokemon", "chinese_prompt": "水莲 (宝可梦)", "image_path": "assets/output_4_pokemon__lana__pokemon___pokemon_116.webp"}, {"prompt": "kobayashi-san_chi_no_maidragon, kanna kamui, kobayashi-san chi no maidragon", "chinese_prompt": "康娜卡姆依 (小林家的龙女仆)", "image_path": "assets/output_4_kobayashi-san_chi_no_maidragon__kanna_kamui__kobayashi-san_chi_no_maidragon_117.webp"}, {"prompt": "kantai_collection, hatsuyuki (kancolle), kantai collection", "chinese_prompt": "初雪 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__hatsuy<PERSON>__kancolle___kantai_collection_118.webp"}, {"prompt": "kantai_collection, isokaze (kancolle), kantai collection", "chinese_prompt": "矶风 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__isokaze__kancolle___kantai_collection_119.webp"}, {"prompt": "kantai_collection, unryuu (kancolle), kantai collection", "chinese_prompt": "云龙 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__unryuu__kancolle___kantai_collection_120.webp"}, {"prompt": "hololive, fuwawa abyssgard, hololive", "chinese_prompt": "软软·阿比斯加德 (Fuwawa Abyssgard) (Hololive)", "image_path": "assets/output_4_hololive__fuwawa_abyssgard__hololive_121.webp"}, {"prompt": "genshin_impact, alhaitham (genshin impact), genshin impact", "chinese_prompt": "艾尔海森 (原神)", "image_path": "assets/output_4_genshin_impact__alhaitham__genshin_impact___genshin_impact_122.webp"}, {"prompt": "hololive, nino<PERSON><PERSON> in<PERSON>'<PERSON> (1st costume), hololive", "chinese_prompt": "一伊那尔栖 (1st服) (Hololive)", "image_path": "assets/output_4_hololive__ninomae_ina_nis__1st_costume___hololive_123.webp"}, {"prompt": "fate_(series), katsu<PERSON><PERSON> hokusai (fate), fate (series)", "chinese_prompt": "葛饰北斋 (Fate)", "image_path": "assets/output_4_fate__series___katsu<PERSON><PERSON>_hokusai__fate___fate__series__124.webp"}, {"prompt": "splatoon_(series), octoling girl, splatoon (series)", "chinese_prompt": "章鱼女孩 (斯普拉遁)", "image_path": "assets/output_4_splatoon__series___octoling_girl__splatoon__series__125.webp"}, {"prompt": "hyouka, chitanda eru, hyouka", "chinese_prompt": "千反田爱瑠 (冰果)", "image_path": "assets/output_4_hyouka__chitanda_eru__hyouka_126.webp"}, {"prompt": "toaru_majutsu_no_index, accelerator (toaru majutsu no index), toaru majutsu no index", "chinese_prompt": "一方通行 (魔法禁书目录)", "image_path": "assets/output_4_toaru_majutsu_no_index__accelerator__toaru_majutsu_no_index___toaru_majutsu_no_index_127.webp"}, {"prompt": "omori, sunny (omori), omori", "chinese_prompt": "SUNNY (Omori)", "image_path": "assets/output_4_omori__sunny__omori___omori_128.webp"}, {"prompt": "pokemon, hex maniac (pokemon), pokemon", "chinese_prompt": "灵异迷 (宝可梦)", "image_path": "assets/output_4_pokemon__hex_maniac__pokemon___pokemon_129.webp"}, {"prompt": "love_live!, mat<PERSON><PERSON> kanan, love live!", "chinese_prompt": "松浦果南 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_4_love_live___matsu<PERSON>_kanan__love_live__130.webp"}, {"prompt": "girls'_frontline, wa2000 (girls' frontline), girls' frontline", "chinese_prompt": "WA2000 (少女前线)", "image_path": "assets/output_4_girls__frontline__wa2000__girls__frontline___girls__frontline_131.webp"}, {"prompt": "bang_dream!, na<PERSON><PERSON> soyo, bang dream!", "chinese_prompt": "长崎爽世 (Bang Dream!)", "image_path": "assets/output_4_bang_dream___nagasaki_soyo__bang_dream__132.webp"}, {"prompt": "hololive, mococo abyssgard, hololive", "chinese_prompt": "茸茸·阿比斯加德 (Hololive)", "image_path": "assets/output_4_hololive__mococo_abyssgard__hololive_133.webp"}, {"prompt": "kantai_collection, saratoga (kancolle), kantai collection", "chinese_prompt": "萨拉托加 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__saratoga__kancolle___kantai_collection_134.webp"}, {"prompt": "senki_zesshou_symphogear, yukine chris, senki zesshou symphogear", "chinese_prompt": "雪音克莉丝 (战姬绝唱SYMPHOGEAR)", "image_path": "assets/output_4_senki_zesshou_symphogear__yukine_chris__senki_zesshou_symphogear_135.webp"}, {"prompt": "kantai_collection, michi<PERSON>o (kancolle), kantai collection", "chinese_prompt": "满潮 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__mi<PERSON><PERSON><PERSON>__kancolle___kantai_collection_136.webp"}, {"prompt": "umamusume, oguri cap (umamusume), umamusume", "chinese_prompt": "小栗帽 (赛马娘)", "image_path": "assets/output_4_umamusume__oguri_cap__umamusume___umamusume_137.webp"}, {"prompt": "blue_archive, iori (blue archive), blue archive", "chinese_prompt": "银镜伊织 (蔚蓝档案)", "image_path": "assets/output_4_blue_archive__iori__blue_archive___blue_archive_138.webp"}, {"prompt": "kantai_collection, hatsuzuki (kancolle), kantai collection", "chinese_prompt": "初月 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__hatsuzuki__kancolle___kantai_collection_139.webp"}, {"prompt": "hololive, tsun<PERSON>ki watame, hololive", "chinese_prompt": "角卷绵芽 (Hololive)", "image_path": "assets/output_4_hololive__tsun<PERSON><PERSON>_watame__hololive_140.webp"}, {"prompt": "world_witches_series, mi<PERSON><PERSON><PERSON> yoshika, world witches series", "chinese_prompt": "宫藤芳佳 (强袭魔女)", "image_path": "assets/output_4_world_witches_series__mi<PERSON><PERSON><PERSON>_yoshi<PERSON>__world_witches_series_141.webp"}, {"prompt": "blue_archive, izuna (blue archive), blue archive", "chinese_prompt": "久田伊树菜 (蔚蓝档案)", "image_path": "assets/output_4_blue_archive__izuna__blue_archive___blue_archive_142.webp"}, {"prompt": "hololive, akai haato, hololive", "chinese_prompt": "赤井心 (Hololive)", "image_path": "assets/output_4_hololive__akai_haato__hololive_143.webp"}, {"prompt": "pokemon, blue oak, pokemon", "chinese_prompt": "青绿 (宝可梦)", "image_path": "assets/output_4_pokemon__blue_oak__pokemon_144.webp"}, {"prompt": "fate_(series), emiya kiritsu<PERSON>, fate (series)", "chinese_prompt": "卫宫切嗣 (Fate)", "image_path": "assets/output_4_fate__series___emiya_kiritsugu__fate__series__145.webp"}, {"prompt": "kantai_collection, harusame (kancolle), kantai collection", "chinese_prompt": "春雨 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__harusame__kancolle___kantai_collection_146.webp"}, {"prompt": "blue_archive, yuzu (blue archive), blue archive", "chinese_prompt": "花冈柚子 (蔚蓝档案)", "image_path": "assets/output_4_blue_archive__yuzu__blue_archive___blue_archive_147.webp"}, {"prompt": "idolmaster, sakura<PERSON> momoka, idolmaster", "chinese_prompt": "樱井桃华 (灰姑娘) (偶像大师)", "image_path": "assets/output_4_idolmaster__sa<PERSON><PERSON>_mom<PERSON>__idolmaster_148.webp"}, {"prompt": "kantai_collection, taigei (kancolle), kantai collection", "chinese_prompt": "大鲸 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__taigei__kancolle___kantai_collection_149.webp"}, {"prompt": "arknights, mostima (arknights), arknights", "chinese_prompt": "莫斯提马 (明日方舟)", "image_path": "assets/output_4_arknights__mostima__arknights___arknights_150.webp"}, {"prompt": "toaru_majutsu_no_index, kamijou touma, toaru majutsu no index", "chinese_prompt": "上条当麻 (魔法禁书目录)", "image_path": "assets/output_4_toaru_majutsu_no_index__kamijou_touma__toaru_majutsu_no_index_151.webp"}, {"prompt": "world_witches_series, er<PERSON> ha<PERSON>, world witches series", "chinese_prompt": "艾莉卡·哈特曼 (强袭魔女)", "image_path": "assets/output_4_world_witches_series__erica_hartmann__world_witches_series_152.webp"}, {"prompt": "kantai_collection, yahagi (kancolle), kantai collection", "chinese_prompt": "矢矧 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__yahagi__kancolle___kantai_collection_153.webp"}, {"prompt": "azur_lane, laffey (azur lane), azur lane", "chinese_prompt": "拉菲 (碧蓝航线)", "image_path": "assets/output_4_azur_lane__laffey__azur_lane___azur_lane_154.webp"}, {"prompt": "jujutsu_kaisen, fush<PERSON><PERSON> megumi, jujutsu kaisen", "chinese_prompt": "伏黑惠 (咒术回战)", "image_path": "assets/output_4_jujutsu_kaisen__fushiguro_megumi__jujutsu_kaisen_155.webp"}, {"prompt": "touh<PERSON>, kaen<PERSON><PERSON> rin (cat), touh<PERSON>", "chinese_prompt": "火焰猫燐 (猫) (东方)", "image_path": "assets/output_4_touh<PERSON>__kaen<PERSON><PERSON>_rin__cat___touhou_156.webp"}, {"prompt": "kantai_collection, akitsu maru (kancolle), kantai collection", "chinese_prompt": "秋津丸 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__aki<PERSON>_maru__kancolle___kantai_collection_157.webp"}, {"prompt": "kono_subarashii_sekai_ni_shukufuku_wo!, darkness (konosuba), kono subarashii sekai ni shukufuku wo!", "chinese_prompt": "达克妮丝 (为美好的世界献上祝福)", "image_path": "assets/output_4_kono_subarashii_sekai_ni_shukufuku_wo___darkness__konosuba___kono_subarashii_sekai_ni_shukufuku_wo__158.webp"}, {"prompt": "monogatari_(series), sen<PERSON><PERSON><PERSON> hitagi, monogatari (series)", "chinese_prompt": "战场原黑仪 (化物语)", "image_path": "assets/output_4_monogatari__series___senjougahara_hitagi__monogatari__series__159.webp"}, {"prompt": "pokemon, hilbert (pokemon), pokemon", "chinese_prompt": "透也 (宝可梦)", "image_path": "assets/output_4_pokemon__hilbert__pokemon___pokemon_160.webp"}, {"prompt": "granblue_fantasy, gran (granblue fantasy), granblue fantasy", "chinese_prompt": "古兰 (碧蓝幻想)", "image_path": "assets/output_4_granblue_fantasy__gran__granblue_fantasy___granblue_fantasy_161.webp"}, {"prompt": "love_live!, kuro<PERSON>wa dia, love live!", "chinese_prompt": "黑泽黛雅 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_4_love_live___kuro<PERSON><PERSON>_dia__love_live__162.webp"}, {"prompt": "neon_genesis_evangelion, makinami mari illustrious, neon genesis evangelion", "chinese_prompt": "真希波·真理·伊拉丝多莉亚斯 (新世纪福音战士)", "image_path": "assets/output_4_neon_genesis_evangelion__makinami_mari_illustrious__neon_genesis_evangelion_163.webp"}, {"prompt": "touhou, matara okina, touhou", "chinese_prompt": "摩多罗隐岐奈 (东方)", "image_path": "assets/output_4_touhou__matara_okina__touhou_164.webp"}, {"prompt": "touhou, merlin prismriver, touhou", "chinese_prompt": "梅露兰·普莉兹姆利巴 (东方)", "image_path": "assets/output_4_touhou__merlin_prismriver__touhou_165.webp"}, {"prompt": "precure, <PERSON><PERSON><PERSON> rikka, precure", "chinese_prompt": "菱川六花 (光之美少女)", "image_path": "assets/output_4_precure__his<PERSON><PERSON>_rikka__precure_166.webp"}, {"prompt": "urus<PERSON>_yatsura, lum, urusei yatsura", "chinese_prompt": "拉姆 (福星小子)", "image_path": "assets/output_4_urusei_yatsura__lum__urusei_yatsura_167.webp"}, {"prompt": "pokemon, piplup, pokemon", "chinese_prompt": "宝波加曼 (宝可梦)", "image_path": "assets/output_4_pokemon__piplup__pokemon_168.webp"}, {"prompt": "idolmaster, <PERSON><PERSON><PERSON> to<PERSON>, idolmaster", "chinese_prompt": "浅仓透 (闪耀色彩) (偶像大师)", "image_path": "assets/output_4_idolmaster__as<PERSON><PERSON>_toru__idolmaster_169.webp"}, {"prompt": "precure, aoki reika, precure", "chinese_prompt": "青木丽华 (Smile) (光之美少女)", "image_path": "assets/output_4_precure__aoki_reika__precure_170.webp"}, {"prompt": "idolmaster, p-head producer, idolmaster", "chinese_prompt": "P-head 制作人 (偶像大师)", "image_path": "assets/output_4_idolmaster__p-head_producer__idolmaster_171.webp"}, {"prompt": "zenless_zone_zero, jane doe (zenless zone zero), zenless zone zero", "chinese_prompt": "简·杜 (绝区零)", "image_path": "assets/output_4_zenless_zone_zero__jane_doe__zenless_zone_zero___zenless_zone_zero_172.webp"}, {"prompt": "azur_lane, enterprise (azur lane), azur lane", "chinese_prompt": "企业 (碧蓝航线)", "image_path": "assets/output_4_azur_lane__enterprise__azur_lane___azur_lane_173.webp"}, {"prompt": "honkai_(series), dan heng (honkai: star rail), honkai (series)", "chinese_prompt": "丹恒 (崩坏: 星穹铁道) (崩坏)", "image_path": "assets/output_4_honkai__series___dan_heng__honkai__star_rail___honkai__series__174.webp"}, {"prompt": "hololive, nerissa ravencroft, hololive", "chinese_prompt": "纳瑞莎·雷文克罗夫特 (Hololive)", "image_path": "assets/output_4_hololive__nerissa_ravencroft__hololive_175.webp"}, {"prompt": "azur_lane, takao (azur lane), azur lane", "chinese_prompt": "高雄 (碧蓝航线)", "image_path": "assets/output_4_azur_lane__takao__azur_lane___azur_lane_176.webp"}, {"prompt": "idolmaster, futami mami, idolmaster", "chinese_prompt": "双海真美 (偶像大师)", "image_path": "assets/output_4_idolmaster__futami_mami__idolmaster_177.webp"}, {"prompt": "rwby, yang xiao long, rwby", "chinese_prompt": "阳小龙 (RWBY)", "image_path": "assets/output_4_rwby__yang_xiao_long__rwby_178.webp"}, {"prompt": "umamusume, twin turbo (umamusume), umamusume", "chinese_prompt": "双涡轮 (赛马娘)", "image_path": "assets/output_4_umamusume__twin_turbo__umamusume___umamusume_179.webp"}, {"prompt": "angel_beats!, tachibana kanade, angel beats!", "chinese_prompt": "立华奏 天使 (Angel Beats!)", "image_path": "assets/output_4_angel_beats___tachibana_kanade__angel_beats__180.webp"}, {"prompt": "tengen_toppa_gurren_lagann, simon (ttgl), tengen toppa gurren lagann", "chinese_prompt": "西蒙 (天元突破 红莲螺岩)", "image_path": "assets/output_4_tengen_toppa_gurren_lagann__simon__ttgl___tengen_toppa_gurren_lagann_181.webp"}, {"prompt": "pokemon, charizard, pokemon", "chinese_prompt": "喷火龙 (宝可梦)", "image_path": "assets/output_4_pokemon__charizard__pokemon_182.webp"}, {"prompt": "idolmaster, <PERSON><PERSON><PERSON> yui, idolmaster", "chinese_prompt": "大槻唯 (灰姑娘) (偶像大师)", "image_path": "assets/output_4_idolmaster__oh<PERSON><PERSON>_yui__idolmaster_183.webp"}, {"prompt": "kantai_collection, yura (kancolle), kantai collection", "chinese_prompt": "由良 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__yura__kancolle___kantai_collection_184.webp"}, {"prompt": "shakugan_no_shana, shana, shakugan no shana", "chinese_prompt": "夏娜 (灼眼的夏娜)", "image_path": "assets/output_4_shakugan_no_shana__shana__shakugan_no_shana_185.webp"}, {"prompt": "touh<PERSON>, lyrica prism<PERSON>, touhou", "chinese_prompt": "莉莉卡・普莉兹姆利巴 (东方)", "image_path": "assets/output_4_touhou__lyrica_prismriver__touhou_186.webp"}, {"prompt": "gochuumon_wa_usagi_desu_ka?, tippy (go<PERSON><PERSON>), gochuumon wa usagi desu ka?", "chinese_prompt": "提比 (请问您今天要来点兔子吗?)", "image_path": "assets/output_4_gochuumon_wa_usagi_desu_ka___tippy__gochiusa___gochuumon_wa_usagi_desu_ka__187.webp"}, {"prompt": "<PERSON><PERSON><PERSON>_haru<PERSON>_no_yuu<PERSON><PERSON>, <PERSON><PERSON><PERSON> r<PERSON>, suzu<PERSON> haruhi no yuuutsu", "chinese_prompt": "朝仓凉子 (凉宫春日的忧郁)", "image_path": "assets/output_4_su<PERSON><PERSON>_haruhi_no_yuuutsu__as<PERSON><PERSON>_ryouko__suzu<PERSON>_haruhi_no_yuuutsu_188.webp"}, {"prompt": "hololive, in<PERSON><PERSON> koro<PERSON> (1st costume), hololive", "chinese_prompt": "戌神沁音 (1st服), (Hololive)", "image_path": "assets/output_4_hololive__inugami_korone__1st_costume___hololive_189.webp"}, {"prompt": "fate_(series), diarmuid u<PERSON> (lancer) (fate), fate (series)", "chinese_prompt": "迪尔姆德·奥迪那 (枪兵) (Fate)", "image_path": "assets/output_4_fate__series___diarmuid_ua_du<PERSON><PERSON>e__lancer___fate___fate__series__190.webp"}, {"prompt": "world_witches_series, gertrud barkhorn, world witches series", "chinese_prompt": "歌尔特露特·巴克霍隆 (强袭魔女)", "image_path": "assets/output_4_world_witches_series__gertrud_<PERSON><PERSON>__world_witches_series_191.webp"}, {"prompt": "hololive, sa<PERSON><PERSON> chloe (1st costume), hololive", "chinese_prompt": "沙花叉克萝伊 (1st服) (Hololive)", "image_path": "assets/output_4_hololive__sakamata_chloe__1st_costume___hololive_192.webp"}, {"prompt": "monogatari_(series), ha<PERSON><PERSON> tsubasa, monogatari (series)", "chinese_prompt": "羽川翼 (化物语)", "image_path": "assets/output_4_monogatari__series___hanekawa_tsubasa__monogatari__series__193.webp"}, {"prompt": "fate_(series), tamamo cat (fate), fate (series)", "chinese_prompt": "玉藻猫 (Fate)", "image_path": "assets/output_4_fate__series___tamamo_cat__fate___fate__series__194.webp"}, {"prompt": "arknights, dusk (arknights), arknights", "chinese_prompt": "夕 (明日方舟)", "image_path": "assets/output_4_arknights__dusk__arknights___arknights_195.webp"}, {"prompt": "touh<PERSON>, k<PERSON><PERSON><PERSON> yachi<PERSON>, touhou", "chinese_prompt": "吉吊八千慧 (东方)", "image_path": "assets/output_4_touhou__kic<PERSON><PERSON>_yachie__touhou_196.webp"}, {"prompt": "arknights, nian (arknights), arknights", "chinese_prompt": "年 (明日方舟)", "image_path": "assets/output_4_arknights__nian__arknights___arknights_197.webp"}, {"prompt": "pokemon, juliana (pokemon), pokemon", "chinese_prompt": "小青 (宝可梦)", "image_path": "assets/output_4_pokemon__juliana__pokemon___pokemon_198.webp"}, {"prompt": "bocchi_the_rock!, hiroi kikuri, bocchi the rock!", "chinese_prompt": "广井菊里 (孤独摇滚)", "image_path": "assets/output_4_bocchi_the_rock___hiroi_kikuri__bocchi_the_rock__199.webp"}, {"prompt": "umamusume, vodka (umamusume), umamusume", "chinese_prompt": "伏特加 (赛马娘)", "image_path": "assets/output_4_umamusume__vodka__umamusume___umamusume_200.webp"}, {"prompt": "zero_no_t<PERSON><PERSON><PERSON>, louise franco<PERSON> le blanc de la valliere, zero no tsukaima", "chinese_prompt": "路易丝 (零之使魔)", "image_path": "assets/output_4_zero_no_tsu<PERSON><PERSON>__lo<PERSON>_franco<PERSON>_le_blanc_de_la_valliere__zero_no_tsukaima_201.webp"}, {"prompt": "kantai_collection, haguro (kancolle), kantai collection", "chinese_prompt": "羽黑 (舰队收藏)", "image_path": "assets/output_4_kantai_collection__haguro__kancolle___kantai_collection_202.webp"}, {"prompt": "one_piece, trafalgar law, one piece", "chinese_prompt": "托拉法尔加·D·瓦特尔·罗 (海贼王)", "image_path": "assets/output_4_one_piece__trafalgar_law__one_piece_203.webp"}, {"prompt": "azur_lane, akagi (azur lane), azur lane", "chinese_prompt": "赤城 (碧蓝航线)", "image_path": "assets/output_4_azur_lane__akagi__azur_lane___azur_lane_204.webp"}, {"prompt": "kantai_collection, mutsuki (kancolle), kantai collection", "chinese_prompt": "睦月 (舰队收藏)", "image_path": "assets/output_5_kantai_collection__mutsuki__kancolle___kantai_collection_0.webp"}, {"prompt": "hololive, mori calliope (1st costume), hololive", "chinese_prompt": "森美声 (1st服) (Hololive)", "image_path": "assets/output_5_hololive__mori_calliope__1st_costume___hololive_1.webp"}, {"prompt": "pokemon, sonia (pokemon), pokemon", "chinese_prompt": "索妮亚 (宝可梦)", "image_path": "assets/output_5_pokemon__sonia__pokemon___pokemon_2.webp"}, {"prompt": "pokemon, lucario, pokemon", "chinese_prompt": "路卡利欧 (宝可梦)", "image_path": "assets/output_5_pokemon__lucario__pokemon_3.webp"}, {"prompt": "genshin_impact, kaeya (genshin impact), genshin impact", "chinese_prompt": "凯亚 (原神)", "image_path": "assets/output_5_genshin_impact__kaeya__genshin_impact___genshin_impact_4.webp"}, {"prompt": "girls_und_panzer, is<PERSON>u hana, girls und panzer", "chinese_prompt": "五十铃华 (少女与战车)", "image_path": "assets/output_5_girls_und_panzer__isuzu_hana__girls_und_panzer_5.webp"}, {"prompt": "pokemon, lyra (pokemon), pokemon", "chinese_prompt": "琴音 (宝可梦)", "image_path": "assets/output_5_pokemon__lyra__pokemon___pokemon_6.webp"}, {"prompt": "blue_archive, hanako (swimsuit) (blue archive), blue archive", "chinese_prompt": "浦和花子 (泳装) (蔚蓝档案)", "image_path": "assets/output_5_blue_archive__hanako__swimsuit___blue_archive___blue_archive_7.webp"}, {"prompt": "dragon_ball, android 18, dragon ball", "chinese_prompt": "人造人18号 (七龙珠)", "image_path": "assets/output_5_dragon_ball__android_18__dragon_ball_8.webp"}, {"prompt": "kono_subarashii_sekai_ni_shukufuku_wo!, satou kazuma, kono subarashii sekai ni shukufuku wo!", "chinese_prompt": "佐藤和真 (为美好的世界献上祝福)", "image_path": "assets/output_5_kono_subarashii_sekai_ni_shukufuku_wo___satou_kazuma__kono_subarashii_sekai_ni_shukufuku_wo__9.webp"}, {"prompt": "genshin_impact, ningguang (genshin impact), genshin impact", "chinese_prompt": "凝光 (原神)", "image_path": "assets/output_5_genshin_impact__ningguang__genshin_impact___genshin_impact_10.webp"}, {"prompt": "link!_like!_love_live!, o<PERSON><PERSON> rurino, link! like! love live!", "chinese_prompt": "大泽瑠璃乃 (Link! Like! Love Live!)", "image_path": "assets/output_5_link__like__love_live___osa<PERSON>_rurino__link__like__love_live__11.webp"}, {"prompt": "kantai_collection, t-head admiral, kantai collection", "chinese_prompt": "T-Head提督 (舰队收藏)", "image_path": "assets/output_5_kantai_collection__t-head_admiral__kantai_collection_12.webp"}, {"prompt": "love_live!, k<PERSON><PERSON><PERSON> hanam<PERSON>, love live!", "chinese_prompt": "国木田花丸 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_5_love_live___kuni<PERSON>da_hanamaru__love_live__13.webp"}, {"prompt": "kimetsu_no_yaiba, koch<PERSON> shinobu, kimetsu no yaiba", "chinese_prompt": "胡蝶忍 (鬼灭之刃)", "image_path": "assets/output_5_kimetsu_no_yaiba__kochou_shinobu__kimetsu_no_yaiba_14.webp"}, {"prompt": "genshin_impact, dodoco (genshin impact), genshin impact", "chinese_prompt": "嘟嘟可 (原神)", "image_path": "assets/output_5_genshin_impact__dodoco__genshin_impact___genshin_impact_15.webp"}, {"prompt": "honkai_(series), seele vollerei, honkai (series)", "chinese_prompt": "希儿 (崩坏: 星穹铁道) (崩坏)", "image_path": "assets/output_5_honkai__series___seele_vollerei__honkai__series__16.webp"}, {"prompt": "toaru_majutsu_no_index, saten ruiko, toaru majutsu no index", "chinese_prompt": "佐天泪子 (魔法禁书目录)", "image_path": "assets/output_5_toaru_majutsu_no_index__saten_ruiko__toaru_majutsu_no_index_17.webp"}, {"prompt": "idolmaster, <PERSON><PERSON><PERSON> meguru, idolmaster", "chinese_prompt": "八宫巡 (闪耀色彩) (偶像大师)", "image_path": "assets/output_5_idolmaster__ha<PERSON><PERSON>_meguru__idolmaster_18.webp"}, {"prompt": "umamusume, mihono bourbon (umamusume), umamusume", "chinese_prompt": "美浦波旁 (赛马娘)", "image_path": "assets/output_5_umamusume__mihono_bourbon__umamusume___umamusume_19.webp"}, {"prompt": "rozen_maiden, shinku, rozen maiden", "chinese_prompt": "真红 (蔷薇少女)", "image_path": "assets/output_5_rozen_maiden__shinku__rozen_maiden_20.webp"}, {"prompt": "umamusume, admire vega (umamusume), umamusume", "chinese_prompt": "爱慕织姬 (赛马娘)", "image_path": "assets/output_5_umamusume__admire_vega__umamusume___umamusume_21.webp"}, {"prompt": "vocaloid, sakura miku, vocaloid", "chinese_prompt": "樱初音 (Vocaloid)", "image_path": "assets/output_5_vocaloid__sakura_miku__vocaloid_22.webp"}, {"prompt": "mahou_shoujo_madoka_magica, a<PERSON><PERSON>, mahou shoujo madoka magica", "chinese_prompt": "恶魔焰 (魔法少女小圆)", "image_path": "assets/output_5_mahou_shoujo_madoka_magica__a<PERSON><PERSON>_ho<PERSON>__mahou_shoujo_madoka_magica_23.webp"}, {"prompt": "macross, ranka lee, macross", "chinese_prompt": "兰花·李 (F) (超时空要塞)", "image_path": "assets/output_5_macross__ranka_lee__macross_24.webp"}, {"prompt": "little_witch_academia, kagari atsuko, little witch academia", "chinese_prompt": "篝敦子 (小魔女学院)", "image_path": "assets/output_5_little_witch_academia__kagari_at<PERSON>ko__little_witch_academia_25.webp"}, {"prompt": "love_live!, murano sayaka, love live!", "chinese_prompt": "村野沙耶香 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_5_love_live___murano_sayaka__love_live__26.webp"}, {"prompt": "kantai_collection, a<PERSON><PERSON> (kancolle), kantai collection", "chinese_prompt": "秋月 (舰队收藏)", "image_path": "assets/output_5_kantai_collection__a<PERSON><PERSON>__kancolle___kantai_collection_27.webp"}, {"prompt": "girls'_frontline, ump9 (girls' frontline), girls' frontline", "chinese_prompt": "UMP9 (少女前线)", "image_path": "assets/output_5_girls__frontline__ump9__girls__frontline___girls__frontline_28.webp"}, {"prompt": "su<PERSON><PERSON>_haru<PERSON>_no_yuu<PERSON><PERSON>, k<PERSON><PERSON>, suzumiya haruhi no yuuutsu", "chinese_prompt": "虚子 (凉宫春日的忧郁)", "image_path": "assets/output_5_su<PERSON><PERSON>_haru<PERSON>_no_yuuutsu__k<PERSON><PERSON>__suzu<PERSON>_haruhi_no_yuuutsu_29.webp"}, {"prompt": "fire_emblem, hilda valentine goneril, fire emblem", "chinese_prompt": "希尔妲·凡伦汀·哥纳利尔 (风花雪月) (圣火降魔录)", "image_path": "assets/output_5_fire_emblem__hilda_valentine_goneril__fire_emblem_30.webp"}, {"prompt": "azur_lane, kaga (azur lane), azur lane", "chinese_prompt": "加贺 (碧蓝航线)", "image_path": "assets/output_5_azur_lane__kaga__azur_lane___azur_lane_31.webp"}, {"prompt": "fate/grand_order, napoleon bonaparte (fate), fate/grand order", "chinese_prompt": "拿破仑·波拿巴 (Fate/Grand Order)", "image_path": "assets/output_5_fate_grand_order__napoleon_bonaparte__fate___fate_grand_order_32.webp"}, {"prompt": "fate_(series), jeanne d'arc alter (swimsuit berserker) (fate), fate (series)", "chinese_prompt": "黑贞德 (泳装狂战士) (命运), 命运系列", "image_path": "assets/output_5_fate__series___jeanne_d_arc_alter__swimsuit_berserker___fate___fate__series__33.webp"}, {"prompt": "love_live!, kuro<PERSON><PERSON> ruby, love live!", "chinese_prompt": "黑泽露比 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_5_love_live___kuro<PERSON><PERSON>_ruby__love_live__34.webp"}, {"prompt": "idolmaster, akagi miria, idolmaster", "chinese_prompt": "赤城米莉亚 (灰姑娘) (偶像大师)", "image_path": "assets/output_5_idolmaster__akagi_miria__idolmaster_35.webp"}, {"prompt": "precure, hino akane (smile precure!), precure", "chinese_prompt": "日野茜 (Smile) (光之美少女)", "image_path": "assets/output_5_precure__hino_akane__smile_precure____precure_36.webp"}, {"prompt": "kantai_collection, u-511 (kanco<PERSON>), kantai collection", "chinese_prompt": "U-511 (舰队收藏)", "image_path": "assets/output_5_kantai_collection__u-511__kancolle___kantai_collection_37.webp"}, {"prompt": "pokemon, rika (pokemon), pokemon", "chinese_prompt": "辛俐 (宝可梦)", "image_path": "assets/output_5_pokemon__rika__pokemon___pokemon_38.webp"}, {"prompt": "ni<PERSON><PERSON><PERSON>, lize he<PERSON>, ni<PERSON><PERSON><PERSON>", "chinese_prompt": "莉泽·赫露艾斯塔 (彩虹社)", "image_path": "assets/output_5_niji<PERSON><PERSON>__lize_helesta__niji<PERSON>ji_39.webp"}, {"prompt": "boku_wa_tomodachi_ga_suku<PERSON>, ka<PERSON><PERSON><PERSON> sena, boku wa tomodachi ga sukunai", "chinese_prompt": "柏崎星奈 (我的朋友很少)", "image_path": "assets/output_5_boku_wa_tomodachi_ga_sukunai__ka<PERSON><PERSON><PERSON>_sena__boku_wa_tomodachi_ga_sukunai_40.webp"}, {"prompt": "blue_archive, hibiki (blue archive), blue archive", "chinese_prompt": "猫冢响 (蔚蓝档案)", "image_path": "assets/output_5_blue_archive__hibiki__blue_archive___blue_archive_41.webp"}, {"prompt": "um<PERSON><PERSON>_no_naku_koro_ni, <PERSON><PERSON><PERSON> battler, umineko no naku koro ni", "chinese_prompt": "右代宫 战人 (海猫鸣泣之时)", "image_path": "assets/output_5_um<PERSON><PERSON>_no_naku_koro_ni__<PERSON><PERSON><PERSON>_battler__um<PERSON><PERSON>_no_naku_koro_ni_42.webp"}, {"prompt": "kantai_collection, re-class battleship, kantai collection", "chinese_prompt": "Re级战舰 (舰队收藏)", "image_path": "assets/output_5_kantai_collection__re-class_battleship__kantai_collection_43.webp"}, {"prompt": "blue_archive, azusa (blue archive), blue archive", "chinese_prompt": "白洲梓 (蔚蓝档案)", "image_path": "assets/output_5_blue_archive__a<PERSON>sa__blue_archive___blue_archive_44.webp"}, {"prompt": "dragon_ball, vegeta, dragon ball", "chinese_prompt": "贝吉塔 (七龙珠)", "image_path": "assets/output_5_dragon_ball__vegeta__dragon_ball_45.webp"}, {"prompt": "blue_archive, serika (blue archive), blue archive", "chinese_prompt": "黑见茜香 (蔚蓝档案)", "image_path": "assets/output_5_blue_archive__serika__blue_archive___blue_archive_46.webp"}, {"prompt": "kantai_collection, shikinami (kancolle), kantai collection", "chinese_prompt": "", "image_path": "assets/output_5_kantai_collection__shi<PERSON><PERSON>__kancolle___kantai_collection_47.webp"}, {"prompt": "arknights, specter (arknights), arknights", "chinese_prompt": "", "image_path": "assets/output_5_arknights__specter__arknights___arknights_48.webp"}, {"prompt": "fate_(series), sessy<PERSON>n kiara, fate (series)", "chinese_prompt": "", "image_path": "assets/output_5_fate__series___sessyoin_kiara__fate__series__49.webp"}, {"prompt": "houseki_no_kuni, phosphophyllite, houseki no kuni", "chinese_prompt": "磷叶石 (宝石之国)", "image_path": "assets/output_5_houseki_no_kuni__phosphophyllite__houseki_no_kuni_50.webp"}, {"prompt": "blue_archive, iroha (blue archive), blue archive", "chinese_prompt": "枣伊吕波 (蔚蓝档案)", "image_path": "assets/output_5_blue_archive__iroha__blue_archive___blue_archive_51.webp"}, {"prompt": "mario_(series), luigi, mario (series)", "chinese_prompt": "玛利欧 (路易吉) (超级玛利欧)", "image_path": "assets/output_5_mario__series___luigi__mario__series__52.webp"}, {"prompt": "vocaloid, ia (vocaloid), vocaloid", "chinese_prompt": "IA (Vocaloid)", "image_path": "assets/output_5_vocaloid__ia__vocaloid___vocaloid_53.webp"}, {"prompt": "umamusume, mayano top gun (umamusume), umamusume", "chinese_prompt": "摩耶重砲 (赛马娘)", "image_path": "assets/output_5_umamusume__mayano_top_gun__umamusume___umamusume_54.webp"}, {"prompt": "kantai_collection, i-8 (kanco<PERSON>), kantai collection", "chinese_prompt": "I-8 (舰队收藏)", "image_path": "assets/output_5_kantai_collection__i-8__kancolle___kantai_collection_55.webp"}, {"prompt": "fate_(series), bb (fate/extra), fate (series)", "chinese_prompt": "BB (Fate/Extra) (Fate)", "image_path": "assets/output_5_fate__series___bb__fate_extra___fate__series__56.webp"}, {"prompt": "fire_emblem, lysithea von ordel<PERSON>, fire emblem", "chinese_prompt": "莉丝缇亚·冯·科迪利亚 (风花雪月) (圣火降魔录)", "image_path": "assets/output_5_fire_emblem__lysith<PERSON>_von_or<PERSON><PERSON>__fire_emblem_57.webp"}, {"prompt": "yuru_yuri, to<PERSON><PERSON> k<PERSON>, yuru yuri", "chinese_prompt": "岁纳京子 (悠哉日常大王)", "image_path": "assets/output_5_yuru_yuri__to<PERSON><PERSON>_kyouko__yuru_yuri_58.webp"}, {"prompt": "danga<PERSON><PERSON><PERSON>_(series), t<PERSON><PERSON><PERSON> mikan, danga<PERSON><PERSON><PERSON> (series)", "chinese_prompt": "罪木蜜柑 (弹丸论破)", "image_path": "assets/output_5_danganronpa__series___tsu<PERSON><PERSON>_mikan__danganronpa__series__59.webp"}, {"prompt": "girls_und_panzer, nonna (girls und panzer), girls und panzer", "chinese_prompt": "农娜 (少女与战车)", "image_path": "assets/output_5_girls_und_panzer__nonna__girls_und_panzer___girls_und_panzer_60.webp"}, {"prompt": "touhou, kuda<PERSON><PERSON> tsukasa, touhou", "chinese_prompt": "菅牧典 (东方)", "image_path": "assets/output_5_touh<PERSON>__k<PERSON><PERSON><PERSON>_tsu<PERSON><PERSON>__touhou_61.webp"}, {"prompt": "kantai_collection, abukuma (kancolle), kantai collection", "chinese_prompt": "阿武隈 (舰队收藏)", "image_path": "assets/output_5_kantai_collection__a<PERSON><PERSON><PERSON>__kancolle___kantai_collection_62.webp"}, {"prompt": "kantai_collection, tone (kancolle), kantai collection", "chinese_prompt": "利根 (舰队收藏)", "image_path": "assets/output_5_kantai_collection__tone__kancolle___kantai_collection_63.webp"}, {"prompt": "persona, ku<PERSON><PERSON> rise, persona", "chinese_prompt": "久慈川理世 (P4) (女神异闻录)", "image_path": "assets/output_5_persona__ku<PERSON><PERSON>_rise__persona_64.webp"}, {"prompt": "kill_la_kill, junketsu, kill la kill", "chinese_prompt": "鬼龙院皐月 (纯洁) (斩服少女)", "image_path": "assets/output_5_kill_la_kill__junketsu__kill_la_kill_65.webp"}, {"prompt": "fate_(series), mash kyrielight (dangerous beast), fate (series)", "chinese_prompt": "玛修·基利艾拉特 (危险野兽) (Fate)", "image_path": "assets/output_5_fate__series___mash_kyrielight__dangerous_beast___fate__series__66.webp"}, {"prompt": "pokemon, brendan (pokemon), pokemon", "chinese_prompt": "小悠 (宝可梦)", "image_path": "assets/output_5_pokemon__brendan__pokemon___pokemon_67.webp"}, {"prompt": "chainsaw_man, hi<PERSON><PERSON><PERSON> kobe<PERSON>, chainsaw man", "chinese_prompt": "东山小红 (电锯人)", "image_path": "assets/output_5_chainsaw_man__hi<PERSON><PERSON><PERSON>_kobe<PERSON>__chainsaw_man_68.webp"}, {"prompt": "persona, aegis (persona), persona", "chinese_prompt": "埃癸斯 (P3), (女神异闻录)", "image_path": "assets/output_5_persona__aegis__persona___persona_69.webp"}, {"prompt": "hololive, momos<PERSON>u nene, hololive", "chinese_prompt": "桃铃音音 (Hololive)", "image_path": "assets/output_5_hololive__momosuzu_nene__hololive_70.webp"}, {"prompt": "idolmaster, futami ami, idolmaster", "chinese_prompt": "双海亚美 (偶像大师)", "image_path": "assets/output_5_idolmaster__futami_ami__idolmaster_71.webp"}, {"prompt": "kantai_collection, pola (kancolle), kantai collection", "chinese_prompt": "波拉 (舰队收藏)", "image_path": "assets/output_5_kantai_collection__pola__kancolle___kantai_collection_72.webp"}, {"prompt": "fate_(series), bb (swimsuit mooncancer) (fate), fate (series)", "chinese_prompt": "BB (泳装月癌) (Fate)", "image_path": "assets/output_5_fate__series___bb__swimsuit_mooncancer___fate___fate__series__73.webp"}, {"prompt": "genshin_impact, albedo (genshin impact), genshin impact", "chinese_prompt": "阿贝多 (原神)", "image_path": "assets/output_5_genshin_impact__albedo__genshin_impact___genshin_impact_74.webp"}, {"prompt": "idolmaster, mori<PERSON><PERSON> nono, idolmaster", "chinese_prompt": "森久保乃乃 (灰姑娘) (偶像大师)", "image_path": "assets/output_5_idolmaster__mori<PERSON><PERSON>_nono__idolmaster_75.webp"}, {"prompt": "fire_emblem, lyn (fire emblem), fire emblem", "chinese_prompt": "琳 (烈火之剑) (圣火降魔录)", "image_path": "assets/output_5_fire_emblem__lyn__fire_emblem___fire_emblem_76.webp"}, {"prompt": "blue_archive, shiroko terror (blue archive), blue archive", "chinese_prompt": "砂狼白子 恐怖 (蔚蓝档案)", "image_path": "assets/output_5_blue_archive__shiroko_terror__blue_archive___blue_archive_77.webp"}, {"prompt": "idolmaster, fuku<PERSON><PERSON> k<PERSON>o, idolmaster", "chinese_prompt": "福丸小糸 (闪耀色彩) (偶像大师)", "image_path": "assets/output_5_idolmaster__fuku<PERSON><PERSON>_k<PERSON>o__idolmaster_78.webp"}, {"prompt": "precure, houjou hibiki, precure", "chinese_prompt": "北条响 (光之美少女)", "image_path": "assets/output_5_precure__houjou_hibiki__precure_79.webp"}, {"prompt": "fate/grand_order, anastasia (fate), fate/grand order", "chinese_prompt": "安娜塔西亚 (Fate/Grand Order)", "image_path": "assets/output_5_fate_grand_order__anastasia__fate___fate_grand_order_80.webp"}, {"prompt": "kantai_collection, kamikaze (kancolle), kantai collection", "chinese_prompt": "神风 (舰队收藏)", "image_path": "assets/output_5_kantai_collection__kamikaze__kancolle___kantai_collection_81.webp"}, {"prompt": "umamusume, curren chan (umamusume), umamusume", "chinese_prompt": "真机伶 (赛马娘)", "image_path": "assets/output_5_umamusume__curren_chan__umamusume___umamusume_82.webp"}, {"prompt": "fate_(series), fou (fate), fate (series)", "chinese_prompt": "芙芙 (Fate)", "image_path": "assets/output_5_fate__series___fou__fate___fate__series__83.webp"}, {"prompt": "pokemon, gengar, pokemon", "chinese_prompt": "耿鬼 (宝可梦)", "image_path": "assets/output_5_pokemon__gengar__pokemon_84.webp"}, {"prompt": "kantai_collection, kisaragi (kancolle), kantai collection", "chinese_prompt": "如月 (舰队收藏)", "image_path": "assets/output_5_kantai_collection__kisa<PERSON>i__kancolle___kantai_collection_85.webp"}, {"prompt": "chuuni<PERSON><PERSON>_demo_koi_ga_shitai!, ta<PERSON><PERSON> rikka, chuunibyou demo koi ga shitai!", "chinese_prompt": "小鸟游六花 (中二病也要谈恋爱!)", "image_path": "assets/output_5_chuuniby<PERSON>_demo_koi_ga_shitai___ta<PERSON><PERSON>_rikka__chuunibyou_demo_koi_ga_shitai__86.webp"}, {"prompt": "ranma_1/2, ranma-chan, ranma 1/2", "chinese_prompt": "早乙女乱马 (乱马1/2)", "image_path": "assets/output_5_ranma_1_2__ranma-chan__ranma_1_2_87.webp"}, {"prompt": "pokemon, leaf (pokemon), pokemon", "chinese_prompt": "叶子 (宝可梦)", "image_path": "assets/output_5_pokemon__leaf__pokemon___pokemon_88.webp"}, {"prompt": "league_of_legends, jinx (league of legends), league of legends", "chinese_prompt": "吉茵珂丝 (英雄联盟 LOL)", "image_path": "assets/output_5_league_of_legends__jinx__league_of_legends___league_of_legends_89.webp"}, {"prompt": "gridman_universe, shin<PERSON> akane, gridman universe", "chinese_prompt": "新条茜 (Gridman Universe)", "image_path": "assets/output_5_gridman_universe__shin<PERSON>_akane__gridman_universe_90.webp"}, {"prompt": "kaguya-sama_wa_kokurasetai_~tensai-tachi_no_renai_zunousen~, s<PERSON><PERSON> kaguya, kaguya-sama wa kokurasetai ~tensai-tachi no renai zunousen~", "chinese_prompt": "四宫辉夜 (辉夜姬想让人告白)", "image_path": "assets/output_5_kaguya-sama_wa_kokurasetai__tensai-tachi_no_renai_zunousen___s<PERSON><PERSON>_kaguya__kaguya-sama_wa_kokurasetai__tensai-tachi_no_renai_zunousen__91.webp"}, {"prompt": "final_fantasy, adventurer (ff11), final fantasy", "chinese_prompt": "冒险者 (ff11) (最终幻想)", "image_path": "assets/output_5_final_fantasy__adventurer__ff11___final_fantasy_92.webp"}, {"prompt": "panty_&_stocking_with_garterbelt, panty (psg), panty & stocking with garterbelt", "chinese_prompt": "<PERSON><PERSON> (吊带袜天使)", "image_path": "assets/output_5_panty___stocking_with_garterbelt__panty__psg___panty___stocking_with_garterbelt_93.webp"}, {"prompt": "kantai_collection, miyuki (kancolle), kantai collection", "chinese_prompt": "深雪 (舰队收藏)", "image_path": "assets/output_5_kantai_collection__miyuki__kancolle___kantai_collection_94.webp"}, {"prompt": "danganronpa_(series), monoku<PERSON>, danganronpa (series)", "chinese_prompt": "黑白熊 (弹丸论破)", "image_path": "assets/output_5_danganronpa__series___monokuma__danganronpa__series__95.webp"}, {"prompt": "kantai_collection, gambier bay (kancolle), kantai collection", "chinese_prompt": "甘比尔湾 (舰队收藏)", "image_path": "assets/output_5_kantai_collection__gambier_bay__kancolle___kantai_collection_96.webp"}, {"prompt": "fate_(series), yu mei-ren (fate), fate (series)", "chinese_prompt": "虞美人 (Fate)", "image_path": "assets/output_5_fate__series___yu_mei-ren__fate___fate__series__97.webp"}, {"prompt": "fate_(series), i<PERSON>ki douji (fate), fate (series)", "chinese_prompt": "茨木童子 (Fate)", "image_path": "assets/output_5_fate__series___i<PERSON><PERSON>_douji__fate___fate__series__98.webp"}, {"prompt": "arknights, angelina (arknights), arknights", "chinese_prompt": "安洁莉娜 (明日方舟)", "image_path": "assets/output_5_arknights__angelina__arknights___arknights_99.webp"}, {"prompt": "pokemon, morpeko, pokemon", "chinese_prompt": "莫鲁贝可 (宝可梦)", "image_path": "assets/output_5_pokemon__morpeko__pokemon_100.webp"}, {"prompt": "blue_archive, ichika (blue archive), blue archive", "chinese_prompt": "仲正一花 (蔚蓝档案)", "image_path": "assets/output_5_blue_archive__ichika__blue_archive___blue_archive_101.webp"}, {"prompt": "kara_no_kyoukai, ryo<PERSON>i shiki, kara no kyoukai", "chinese_prompt": "两仪式 (空之境界)", "image_path": "assets/output_5_kara_no_kyoukai__ryougi_shiki__kara_no_kyoukai_102.webp"}, {"prompt": "hololive, na<PERSON><PERSON> (1st costume), hololive", "chinese_prompt": "七诗无铭 (1st服), (Hololive)", "image_path": "assets/output_5_hololive__nanas<PERSON>_mumei__1st_costume___hololive_103.webp"}, {"prompt": "persona, amagi yuki<PERSON>, persona", "chinese_prompt": "天城雪子 (P4) (女神异闻录)", "image_path": "assets/output_5_persona__amagi_yuki<PERSON>__persona_104.webp"}, {"prompt": "dokidoki!_precure, aida mana, dokidoki! precure", "chinese_prompt": "相田爱 (光之美少女)", "image_path": "assets/output_5_dokidoki__precure__aida_mana__dokidoki__precure_105.webp"}, {"prompt": "genshin_impact, tighnari (genshin impact), genshin impact", "chinese_prompt": "提纳里 (原神)", "image_path": "assets/output_5_genshin_impact__tighnari__genshin_impact___genshin_impact_106.webp"}, {"prompt": "hololive, la+ darknesss (1st costume), hololive", "chinese_prompt": "拉普拉斯·暗黑 (1st服) (Hololive)", "image_path": "assets/output_5_hololive__la__darknesss__1st_costume___hololive_107.webp"}, {"prompt": "lyrical_nanoha, raising heart, lyrical nanoha", "chinese_prompt": "高町奈叶 旭日之心 (魔法少女奈叶)", "image_path": "assets/output_5_lyrical_nanoha__raising_heart__lyrical_nanoha_108.webp"}, {"prompt": "to_heart_(series), kousaka tamaki, to heart (series)", "chinese_prompt": "向坂环 (To Heart)", "image_path": "assets/output_5_to_heart__series___kousaka_tamaki__to_heart__series__109.webp"}, {"prompt": "pokemon, n (pokemon), pokemon", "chinese_prompt": "N (宝可梦)", "image_path": "assets/output_5_pokemon__n__pokemon___pokemon_110.webp"}, {"prompt": "promare, lio fotia, promare", "chinese_prompt": "Promare, 利欧·佛蒂亚, Promare", "image_path": "assets/output_5_promare__lio_fotia__promare_111.webp"}, {"prompt": "precure, kurumi erika, precure", "chinese_prompt": "来海绘里香 (光之美少女)", "image_path": "assets/output_5_precure__kurumi_erika__precure_112.webp"}, {"prompt": "girls'_frontline, springfield (girls' frontline), girls' frontline", "chinese_prompt": "springfield (少女前线)", "image_path": "assets/output_5_girls__frontline__springfield__girls__frontline___girls__frontline_113.webp"}, {"prompt": "hibike!_euphonium, ou<PERSON>e kumiko, hibike! euphonium", "chinese_prompt": "黄前久美子 (吹响吧！上低音号)", "image_path": "assets/output_5_hibike__euphonium__oumae_kumiko__hibike__euphonium_114.webp"}, {"prompt": "pokemon, mallow (pokemon), pokemon", "chinese_prompt": "玛奥 (宝可梦)", "image_path": "assets/output_5_pokemon__mallow__pokemon___pokemon_115.webp"}, {"prompt": "vampire_(game), lilith a<PERSON>land, vampire (game)", "chinese_prompt": "莉莉丝·安斯兰特 (魔域幽灵)", "image_path": "assets/output_5_vampire__game___lilith_aensland__vampire__game__116.webp"}, {"prompt": "hololive, shiori novella, hololive", "chinese_prompt": "诗织·诺薇拉 (Hololive)", "image_path": "assets/output_5_hololive__shi<PERSON>_novella__hololive_117.webp"}, {"prompt": "world_witches_series, per<PERSON> h<PERSON>, world witches series", "chinese_prompt": "佩琳·H·克洛斯特曼 (强袭魔女)", "image_path": "assets/output_5_world_witches_series__perrine_h__clos<PERSON><PERSON>__world_witches_series_118.webp"}, {"prompt": "love_live!, yugiri tsu<PERSON>ri, love live!", "chinese_prompt": "夕雾缀理 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_5_love_live___yug<PERSON>_tsu<PERSON><PERSON>__love_live__119.webp"}, {"prompt": "shinryaku!_ikamusume, ikamusume, shinryaku! ikamusume", "chinese_prompt": "花枝娘 (侵略！花枝娘)", "image_path": "assets/output_5_shinryaku__ikamusume__ikamusume__shinryaku__ikamusume_120.webp"}, {"prompt": "love_live!, u<PERSON>ara ayumu, love live!", "chinese_prompt": "上原步梦 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_5_love_live___u<PERSON><PERSON>_ayu<PERSON>__love_live__121.webp"}, {"prompt": "bishoujo_senshi_sailor_moon, mizuno ami, bishoujo senshi sailor moon", "chinese_prompt": "水野亚美 水手水星 (美少女战士)", "image_path": "assets/output_5_bishoujo_senshi_sailor_moon__mizuno_ami__bishoujo_senshi_sailor_moon_122.webp"}, {"prompt": "umam<PERSON><PERSON>, king halo (umamusume), umamusume", "chinese_prompt": "圣王光环 (赛马娘)", "image_path": "assets/output_5_umamusume__king_halo__umamusume___umamusume_123.webp"}, {"prompt": "fate_(series), nero claudius (swimsuit caster) (fate), fate (series)", "chinese_prompt": "尼禄 (泳装魔术师) (Fate)", "image_path": "assets/output_5_fate__series___nero_claudius__swimsuit_caster___fate___fate__series__124.webp"}, {"prompt": "hololive, ne<PERSON><PERSON> (1st costume), hololive", "chinese_prompt": "猫又小粥 (1st服) (Hololive)", "image_path": "assets/output_5_hololive__nekomata_okayu__1st_costume___hololive_125.webp"}, {"prompt": "idolmaster, honda mio, idolmaster", "chinese_prompt": "本田未央 (灰姑娘) (偶像大师)", "image_path": "assets/output_5_idolmaster__honda_mio__idolmaster_126.webp"}, {"prompt": "kantai_collection, i-168 (kanco<PERSON>), kantai collection", "chinese_prompt": "I-168 (舰队收藏)", "image_path": "assets/output_5_kantai_collection__i-168__kancolle___kantai_collection_127.webp"}, {"prompt": "world_witches_series, charlotte e. yeager, world witches series", "chinese_prompt": "夏洛特·E·叶格 (强袭魔女)", "image_path": "assets/output_5_world_witches_series__charlotte_e__yeager__world_witches_series_128.webp"}, {"prompt": "chainsaw_man, reze (chainsaw man), chainsaw man", "chinese_prompt": "蕾塞 (电锯人)", "image_path": "assets/output_5_chainsaw_man__reze__chainsaw_man___chainsaw_man_129.webp"}, {"prompt": "fate_(series), tamamo no mae (swimsuit lancer) (fate), fate (series)", "chinese_prompt": "玉藻前 (泳装枪) (Fate)", "image_path": "assets/output_5_fate__series___tamamo_no_mae__swimsuit_lancer___fate___fate__series__130.webp"}, {"prompt": "honkai_(series), theresa apocalypse, honkai (series)", "chinese_prompt": "德丽莎．阿波卡利斯 (崩坏)", "image_path": "assets/output_5_honkai__series___theresa_apocalypse__honkai__series__131.webp"}, {"prompt": "higurashi_no_naku_koro_ni, son<PERSON><PERSON>on, higurashi no naku koro ni", "chinese_prompt": "园崎魅音 (暮蝉悲鸣时)", "image_path": "assets/output_5_higurashi_no_naku_koro_ni__sonozaki_mion__higurashi_no_naku_koro_ni_132.webp"}, {"prompt": "blue_archive, seia (blue archive), blue archive", "chinese_prompt": "百合园圣亚 (蔚蓝档案)", "image_path": "assets/output_5_blue_archive__seia__blue_archive___blue_archive_133.webp"}, {"prompt": "blue_archive, hoshino (swimsuit) (blue archive), blue archive", "chinese_prompt": "小鸟游星野 (泳装) (蔚蓝档案)", "image_path": "assets/output_5_blue_archive__hoshino__swimsuit___blue_archive___blue_archive_134.webp"}, {"prompt": "rozen_maiden, su<PERSON><PERSON><PERSON>, rozen maiden", "chinese_prompt": "翠星石 (蔷薇少女)", "image_path": "assets/output_5_rozen_maiden__suiseiseki__rozen_maiden_135.webp"}, {"prompt": "touhou, shinki (touhou), touhou", "chinese_prompt": "神绮 (东方)", "image_path": "assets/output_5_touhou__shinki__touhou___touhou_136.webp"}, {"prompt": "touhou, seiran (touhou), touhou", "chinese_prompt": "清兰 (东方)", "image_path": "assets/output_5_touhou__seiran__touhou___touhou_137.webp"}, {"prompt": "persona, shi<PERSON> kotone, persona", "chinese_prompt": "汐见琴音 (P3) (女神异闻录)", "image_path": "assets/output_5_persona__shiomi_kotone__persona_138.webp"}, {"prompt": "nier_(series), 9s (nier:automata), nier (series)", "chinese_prompt": "9S (尼尔:自动人形)", "image_path": "assets/output_5_nier__series___9s__nier_automata___nier__series__139.webp"}, {"prompt": "boku_no_hero_academia, endeavor (boku no hero academia), boku no hero academia", "chinese_prompt": "轰炎司 (我的英雄学院)", "image_path": "assets/output_5_boku_no_hero_academia__endeavor__boku_no_hero_academia___boku_no_hero_academia_140.webp"}, {"prompt": "zenless_zone_zero, nicole demara, zenless zone zero", "chinese_prompt": "妮可 (绝区零)", "image_path": "assets/output_5_zenless_zone_zero__nicole_demara__zenless_zone_zero_141.webp"}, {"prompt": "precure, hoshi<PERSON>a miyuki, precure", "chinese_prompt": "星空美幸 (光之美少女)", "image_path": "assets/output_5_precure__hoshi<PERSON><PERSON>_mi<PERSON>__precure_142.webp"}, {"prompt": "genshin_impact, slime (genshin impact), genshin impact", "chinese_prompt": "史莱姆 (原神)", "image_path": "assets/output_5_genshin_impact__slime__genshin_impact___genshin_impact_143.webp"}, {"prompt": "yuru<PERSON><PERSON>, shima rin, yurucamp", "chinese_prompt": "志摩凛 (摇曳露营)", "image_path": "assets/output_5_yurucamp__shima_rin__yurucamp_144.webp"}, {"prompt": "fire_emblem, robin (female) (fire emblem), fire emblem", "chinese_prompt": "罗宾 (女性) (外传) (圣火降魔录)", "image_path": "assets/output_5_fire_emblem__robin__female___fire_emblem___fire_emblem_145.webp"}, {"prompt": "kantai_collection, makigumo (kancolle), kantai collection", "chinese_prompt": "巻云 (舰队收藏)", "image_path": "assets/output_5_kantai_collection__makigumo__kancolle___kantai_collection_146.webp"}, {"prompt": "arknights, skadi the corrupting heart (arknights), arknights", "chinese_prompt": "腐败的斯卡蒂 (明日方舟)", "image_path": "assets/output_5_arknights__skadi_the_corrupting_heart__arknights___arknights_147.webp"}, {"prompt": "genshin_impact, kujou sara, genshin impact", "chinese_prompt": "九条裟罗 (原神)", "image_path": "assets/output_5_genshin_impact__kujou_sara__genshin_impact_148.webp"}, {"prompt": "love_live!, ohara mari, love live!", "chinese_prompt": "小原鞠莉 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_5_love_live___ohara_mari__love_live__149.webp"}, {"prompt": "kantai_collection, gotland (kancolle), kantai collection", "chinese_prompt": "哥特兰 (舰队收藏)", "image_path": "assets/output_5_kantai_collection__gotland__kancolle___kantai_collection_150.webp"}, {"prompt": "blue_archive, peroro (blue archive), blue archive", "chinese_prompt": "佩洛洛 (蔚蓝档案)", "image_path": "assets/output_5_blue_archive__peroro__blue_archive___blue_archive_151.webp"}, {"prompt": "pokemon, sylveon, pokemon", "chinese_prompt": "仙子伊布 (宝可梦)", "image_path": "assets/output_5_pokemon__sylveon__pokemon_152.webp"}, {"prompt": "kantai_collection, ise (kancolle), kantai collection", "chinese_prompt": "伊势 (舰队收藏)", "image_path": "assets/output_5_kantai_collection__ise__kancolle___kantai_collection_153.webp"}, {"prompt": "honkai:_star_rail, black swan (honkai: star rail), honkai: star rail", "chinese_prompt": "黑天鹅 (崩坏: 星穹铁道)", "image_path": "assets/output_5_honkai__star_rail__black_swan__honkai__star_rail___honkai__star_rail_154.webp"}, {"prompt": "kantai_collection, yuugumo (kancolle), kantai collection", "chinese_prompt": "夕云 (舰队收藏)", "image_path": "assets/output_5_kantai_collection__yuugumo__kancolle___kantai_collection_155.webp"}, {"prompt": "fate_(series), hassan of serenity (fate), fate (series)", "chinese_prompt": "静谧的哈桑 (Fate)", "image_path": "assets/output_5_fate__series___hassan_of_serenity__fate___fate__series__156.webp"}, {"prompt": "rwby, blake belladonna, rwby", "chinese_prompt": "布蕾克·贝拉多娜 (RWBY)", "image_path": "assets/output_5_rwby__blake_belladonna__rwby_157.webp"}, {"prompt": "yuru_yuri, aka<PERSON> akari, yuru yuri", "chinese_prompt": "赤座明 (悠哉日常大王)", "image_path": "assets/output_5_yuru_yuri__akaza_akari__yuru_yuri_158.webp"}, {"prompt": "fate_(series), oberon (fate), fate (series)", "chinese_prompt": "奥伯龙 (Fate)", "image_path": "assets/output_5_fate__series___oberon__fate___fate__series__159.webp"}, {"prompt": "kemono_friends, shoebill (kemono friends), kemono friends", "chinese_prompt": "鲸头鹳 (动物朋友)", "image_path": "assets/output_5_kemono_friends__shoebill__kemono_friends___kemono_friends_160.webp"}, {"prompt": "fate_(series), <PERSON><PERSON><PERSON> (swimsuit lancer) (fate), fate (series)", "chinese_prompt": "喵吹丽斯 溶解莉莉丝 (泳装枪) (Fate)", "image_path": "assets/output_5_fate__series___meltryllis__swimsuit_lancer___fate___fate__series__161.webp"}, {"prompt": "idolmaster, mi<PERSON><PERSON>, idolmaster", "chinese_prompt": "宫本芙蕾德莉卡 (灰姑娘) (偶像大师)", "image_path": "assets/output_5_idolmaster__mi<PERSON><PERSON>_fred<PERSON>a__idolmaster_162.webp"}, {"prompt": "genshin_impact, lisa (genshin impact), genshin impact", "chinese_prompt": "丽莎 (原神)", "image_path": "assets/output_5_genshin_impact__lisa__genshin_impact___genshin_impact_163.webp"}, {"prompt": "boku_no_hero_academia, <PERSON><PERSON> tsuyu, boku no hero academia", "chinese_prompt": "蛙吹梅雨 (我的英雄学院)", "image_path": "assets/output_5_boku_no_hero_academia__asui_tsuyu__boku_no_hero_academia_164.webp"}, {"prompt": "kantai_collection, battleship princess, kantai collection", "chinese_prompt": "战舰栖姫 (舰队收藏)", "image_path": "assets/output_5_kantai_collection__battleship_princess__kantai_collection_165.webp"}, {"prompt": "final_fantasy, yuffie k<PERSON>, final fantasy", "chinese_prompt": "尤菲·吉萨拉基 (ff7) (最终幻想)", "image_path": "assets/output_5_final_fantasy__yuffie_kisa<PERSON>i__final_fantasy_166.webp"}, {"prompt": "world_witches_series, lynette bishop, world witches series", "chinese_prompt": "莉涅特·毕晓普 (强袭魔女)", "image_path": "assets/output_5_world_witches_series__lynette_bishop__world_witches_series_167.webp"}, {"prompt": "fire_emblem, camilla (fire emblem), fire emblem", "chinese_prompt": "卡美拉 (if) (圣火降魔录)", "image_path": "assets/output_5_fire_emblem__camilla__fire_emblem___fire_emblem_168.webp"}, {"prompt": "pokemon, umbreon, pokemon", "chinese_prompt": "月亮伊布 (宝可梦)", "image_path": "assets/output_5_pokemon__umbreon__pokemon_169.webp"}, {"prompt": "fate_(series), melusine (fate), fate (series)", "chinese_prompt": "梅柳齐娜 妖精骑士兰斯洛特 (Fate)", "image_path": "assets/output_5_fate__series___melusine__fate___fate__series__170.webp"}, {"prompt": "girls'_frontline, ak-12 (girls' frontline), girls' frontline", "chinese_prompt": "AK-12 (少女前线), (少女前线)", "image_path": "assets/output_5_girls__frontline__ak-12__girls__frontline___girls__frontline_171.webp"}, {"prompt": "bishoujo_senshi_sailor_moon, aino minako, bishoujo senshi sailor moon", "chinese_prompt": "爱野美奈子 水手金星 (美少女战士)", "image_path": "assets/output_5_bishoujo_senshi_sailor_moon__aino_minako__bishoujo_senshi_sailor_moon_172.webp"}, {"prompt": "kantai_collection, jun'you (kanco<PERSON>), kantai collection", "chinese_prompt": "隼鹰 (舰队收藏)", "image_path": "assets/output_5_kantai_collection__jun_you__kancolle___kantai_collection_173.webp"}, {"prompt": "honkai_(series), yae sakura, honkai (series)", "chinese_prompt": "八重樱 (崩坏)", "image_path": "assets/output_5_honkai__series___yae_sakura__honkai__series__174.webp"}, {"prompt": "idolmaster, shir<PERSON> sakuya, idolmaster", "chinese_prompt": "白濑咲耶 (闪耀色彩) (偶像大师)", "image_path": "assets/output_5_idolmaster__shirase_sakuya__idolmaster_175.webp"}, {"prompt": "genshin_impact, neuvillette (genshin impact), genshin impact", "chinese_prompt": "那维莱特 (原神)", "image_path": "assets/output_5_genshin_impact__neuvillette__genshin_impact___genshin_impact_176.webp"}, {"prompt": "neptune_(series), neptune (neptunia), neptune (series)", "chinese_prompt": "妮普禔努 (战机少女)", "image_path": "assets/output_5_neptune__series___neptune__neptunia___neptune__series__177.webp"}, {"prompt": "dungeon_meshi, senshi (dungeon meshi), dungeon meshi", "chinese_prompt": "森西 (迷宫饭)", "image_path": "assets/output_5_dungeon_meshi__senshi__dungeon_meshi___dungeon_meshi_178.webp"}, {"prompt": "hololive, takane lui, hololive", "chinese_prompt": "鹰岭琉依 (Hololive)", "image_path": "assets/output_5_hololive__takane_lui__hololive_179.webp"}, {"prompt": "fate_(series), mysterious heroine xx (fate), fate (series)", "chinese_prompt": "谜之女主角XX (Fate)", "image_path": "assets/output_5_fate__series___mysterious_heroine_xx__fate___fate__series__180.webp"}, {"prompt": "kantai_collection, kinugasa (kancolle), kantai collection", "chinese_prompt": "衣笠 (舰队收藏)", "image_path": "assets/output_5_kantai_collection__kinugasa__kancolle___kantai_collection_181.webp"}, {"prompt": "fate_(series), ushi<PERSON><PERSON><PERSON><PERSON> (fate), fate (series)", "chinese_prompt": "牛若丸 (Fate)", "image_path": "assets/output_5_fate__series___ushiwaka<PERSON>u__fate___fate__series__182.webp"}, {"prompt": "umamusume, seiun sky (umamusume), umamusume", "chinese_prompt": "青云天空 (赛马娘)", "image_path": "assets/output_5_umamusume__seiun_sky__umamusume___umamusume_183.webp"}, {"prompt": "senki_zesshou_symphogear, <PERSON><PERSON><PERSON> kiri<PERSON>, senki zesshou symphogear", "chinese_prompt": "晓切歌 (战姬绝唱SYMPHOGEAR)", "image_path": "assets/output_5_senki_zesshou_symphogear__akatsuki_kirika__senki_zesshou_symphogear_184.webp"}, {"prompt": "danga<PERSON><PERSON><PERSON>_(series), na<PERSON><PERSON>, danga<PERSON><PERSON><PERSON> (series)", "chinese_prompt": "苗木诚 (弹丸论破)", "image_path": "assets/output_5_danganronpa__series___naegi_makoto__danganronpa__series__185.webp"}, {"prompt": "fate_(series), oda nobuna<PERSON> (koha-ace), fate (series)", "chinese_prompt": "织田信长 (帝都圣杯奇谭) (Fate)", "image_path": "assets/output_5_fate__series___oda_nobunaga__koha-ace___fate__series__186.webp"}, {"prompt": "pokemon, rotom phone, pokemon", "chinese_prompt": "洛托姆手机 (宝可梦)", "image_path": "assets/output_5_pokemon__rotom_phone__pokemon_187.webp"}, {"prompt": "kantai_collection, gangut (kancolle), kantai collection", "chinese_prompt": "甘古特 (舰队收藏)", "image_path": "assets/output_5_kantai_collection__gangut__kancolle___kantai_collection_188.webp"}, {"prompt": "kantai_collection, suzu<PERSON><PERSON> (kancolle), kantai collection", "chinese_prompt": "凉月 (舰队收藏)", "image_path": "assets/output_5_kantai_collection__su<PERSON><PERSON><PERSON>__kancolle___kantai_collection_189.webp"}, {"prompt": "kimetsu_no_yaiba, kan<PERSON><PERSON> mitsuri, kimetsu no yaiba", "chinese_prompt": "甘露寺蜜璃 (鬼灭之刃)", "image_path": "assets/output_5_kimetsu_no_yaiba__kan<PERSON><PERSON>_mitsuri__kimetsu_no_yaiba_190.webp"}, {"prompt": "kemono_friends, lucky beast (kemono friends), kemono friends", "chinese_prompt": "幸运兽 (动物朋友)", "image_path": "assets/output_5_kemono_friends__lucky_beast__kemono_friends___kemono_friends_191.webp"}, {"prompt": "umamusume, eishin flash (umamusume), umamusume", "chinese_prompt": "荣进闪耀 (赛马娘)", "image_path": "assets/output_5_umamusume__eishin_flash__umamusume___umamusume_192.webp"}, {"prompt": "senki_zess<PERSON>_symphogear, ta<PERSON><PERSON> hibiki (symphogear), senki zesshou symphogear", "chinese_prompt": "立花响 (战姬绝唱SYMPHOGEAR)", "image_path": "assets/output_5_senki_zesshou_symphogear__tachibana_hibiki__symphogear___senki_zesshou_symphogear_193.webp"}, {"prompt": "blue_archive, saki (blue archive), blue archive", "chinese_prompt": "空井咲 (蔚蓝档案)", "image_path": "assets/output_5_blue_archive__saki__blue_archive___blue_archive_194.webp"}, {"prompt": "project_moon, is<PERSON><PERSON> (project moon), project moon", "chinese_prompt": "以索梅尔 (Project Moon)", "image_path": "assets/output_5_project_moon__is<PERSON>ael__project_moon___project_moon_195.webp"}, {"prompt": "ni<PERSON><PERSON><PERSON>, <PERSON><PERSON> pen<PERSON>, ni<PERSON><PERSON><PERSON>", "chinese_prompt": "<PERSON><PERSON> (彩虹社)", "image_path": "assets/output_5_niji<PERSON><PERSON>__el<PERSON>_pendora__niji<PERSON>ji_196.webp"}, {"prompt": "sonic_(series), amy rose, sonic (series)", "chinese_prompt": "艾咪·罗丝 (音速小子)", "image_path": "assets/output_5_sonic__series___amy_rose__sonic__series__197.webp"}, {"prompt": "azur_lane, st. louis (azur lane), azur lane", "chinese_prompt": "圣路易斯 (碧蓝航线)", "image_path": "assets/output_5_azur_lane__st__louis__azur_lane___azur_lane_198.webp"}, {"prompt": "girls_und_panzer, ni<PERSON><PERSON><PERSON> shiho, girls und panzer", "chinese_prompt": "西住志穗 (少女与战车)", "image_path": "assets/output_5_girls_und_panzer__ni<PERSON><PERSON><PERSON>_shiho__girls_und_panzer_199.webp"}, {"prompt": "genshin_impact, yanfei (genshin impact), genshin impact", "chinese_prompt": "烟绯 (原神)", "image_path": "assets/output_5_genshin_impact__yanfei__genshin_impact___genshin_impact_200.webp"}, {"prompt": "kantai_collection, nachi (kancolle), kantai collection", "chinese_prompt": "那智 (舰队收藏)", "image_path": "assets/output_5_kantai_collection__nachi__kancolle___kantai_collection_201.webp"}, {"prompt": "kid_icarus, pal<PERSON>na, kid icarus", "chinese_prompt": "帕露蒂娜 (光神话)", "image_path": "assets/output_5_kid_icarus__palutena__kid_icarus_202.webp"}, {"prompt": "pokemon, lusamine (pokemon), pokemon", "chinese_prompt": "露莎米奈 (宝可梦)", "image_path": "assets/output_5_pokemon__lusamine__pokemon___pokemon_203.webp"}, {"prompt": "hololive, housh<PERSON> marine (summer), hololive", "chinese_prompt": "宝钟玛琳 (夏季), (Hololive)", "image_path": "assets/output_5_hololive__houshou_marine__summer___hololive_204.webp"}, {"prompt": "fate_(series), ashiya douman (fate), fate (series)", "chinese_prompt": "芦屋道满 (Fate)", "image_path": "assets/output_6_fate__series___ashiya_douman__fate___fate__series__0.webp"}, {"prompt": "arknights, saria (arknights), arknights", "chinese_prompt": "塞雷娅 (明日方舟)", "image_path": "assets/output_6_arknights__saria__arknights___arknights_1.webp"}, {"prompt": "bleach, kuchiki rukia, bleach", "chinese_prompt": "朽木露琪亚 (死神)", "image_path": "assets/output_6_bleach__kuchiki_rukia__bleach_2.webp"}, {"prompt": "dragon_ball, bulma, dragon ball", "chinese_prompt": "布玛 (七龙珠)", "image_path": "assets/output_6_dragon_ball__bulma__dragon_ball_3.webp"}, {"prompt": "genshin_impact, clorinde (genshin impact), genshin impact", "chinese_prompt": "克洛琳德 (原神)", "image_path": "assets/output_6_genshin_impact__clorinde__genshin_impact___genshin_impact_4.webp"}, {"prompt": "shingeki_no_kyojin, k<PERSON>a le<PERSON>, shingeki no kyojin", "chinese_prompt": "克里斯塔·连兹 (进击的巨人)", "image_path": "assets/output_6_shingeki_no_kyojin__krista_lenz__shingeki_no_kyojin_5.webp"}, {"prompt": "kill_la_kill, jak<PERSON>ure nonon, kill la kill", "chinese_prompt": "蛇崩乃音 (斩服少女)", "image_path": "assets/output_6_kill_la_kill__jak<PERSON><PERSON>_nonon__kill_la_kill_6.webp"}, {"prompt": "ni<PERSON><PERSON><PERSON>, selen tatsuki, ni<PERSON><PERSON><PERSON>", "chinese_prompt": "龙月Selen (彩虹社)", "image_path": "assets/output_6_niji<PERSON><PERSON>__selen_tatsuki__nijisanji_7.webp"}, {"prompt": "girls_und_panzer, orange pekoe (girls und panzer), girls und panzer", "chinese_prompt": "橙黄白毫 (少女与战车)", "image_path": "assets/output_6_girls_und_panzer__orange_pekoe__girls_und_panzer___girls_und_panzer_8.webp"}, {"prompt": "hololive, himemori luna, hololive", "chinese_prompt": "姬森璐娜 (Hololive)", "image_path": "assets/output_6_hololive__himemori_luna__hololive_9.webp"}, {"prompt": "boku_no_hero_academia, to<PERSON><PERSON> shouto, boku no hero academia", "chinese_prompt": "轰焦冻 (我的英雄学院)", "image_path": "assets/output_6_boku_no_hero_academia__todoroki_shouto__boku_no_hero_academia_10.webp"}, {"prompt": "komi-san_wa_komyushou_desu, komi shou<PERSON>, komi-san wa komyushou desu", "chinese_prompt": "古见硝子 (古见同学是沟通鲁蛇)", "image_path": "assets/output_6_komi-san_wa_komyushou_desu__komi_shouko__komi-san_wa_komyushou_desu_11.webp"}, {"prompt": "machikado_mazoku, chi<PERSON>da momo, machikado mazoku", "chinese_prompt": "千代田桃 (街角魔族)", "image_path": "assets/output_6_machikado_mazoku__chiyoda_momo__machikado_mazoku_12.webp"}, {"prompt": "genshin_impact, kaveh (genshin impact), genshin impact", "chinese_prompt": "卡维 (原神)", "image_path": "assets/output_6_genshin_impact__kaveh__genshin_impact___genshin_impact_13.webp"}, {"prompt": "little_busters!, no<PERSON> k<PERSON>, little busters!", "chinese_prompt": "能美 库特莉亚芙卡 (校园克星)", "image_path": "assets/output_6_little_busters___noumi_kud<PERSON><PERSON>ka__little_busters__14.webp"}, {"prompt": "touhou, mima (touhou), touhou", "chinese_prompt": "魅魔 (东方)", "image_path": "assets/output_6_touhou__mima__touhou___touhou_15.webp"}, {"prompt": "oshi_no_ko, hoshino ai (oshi no ko), oshi no ko", "chinese_prompt": "星野爱 (我推的孩子)", "image_path": "assets/output_6_oshi_no_ko__hoshino_ai__oshi_no_ko___oshi_no_ko_16.webp"}, {"prompt": "omori, aubre<PERSON> (omori), omori", "chinese_prompt": "AUBREY (Omori)", "image_path": "assets/output_6_omori__aubrey__omori___omori_17.webp"}, {"prompt": "sword_art_online, sinon, sword art online", "chinese_prompt": "诗音 (刀剑神域)", "image_path": "assets/output_6_sword_art_online__sinon__sword_art_online_18.webp"}, {"prompt": "yume_nikki, mad<PERSON>uki, yume nikki", "chinese_prompt": "附窗子 (梦日记)", "image_path": "assets/output_6_yume_nikki__madotsuki__yume_nikki_19.webp"}, {"prompt": "gochuumon_wa_usagi_desu_ka?, te<PERSON>a rize, gochuumon wa usagi desu ka?", "chinese_prompt": "天天座理世 (请问您今天要来点兔子吗?)", "image_path": "assets/output_6_gochuumon_wa_usagi_desu_ka___tedeza_rize__gochuumon_wa_usagi_desu_ka__20.webp"}, {"prompt": "splatoon_(series), marina (splatoon), splatoon (series)", "chinese_prompt": "饭田＝玛利涅 (斯普拉遁)", "image_path": "assets/output_6_splatoon__series___marina__splatoon___splatoon__series__21.webp"}, {"prompt": "mario_(series), princess daisy, mario (series)", "chinese_prompt": "黛西公主 (超级玛利欧)", "image_path": "assets/output_6_mario__series___princess_daisy__mario__series__22.webp"}, {"prompt": "persona, takamaki anne, persona", "chinese_prompt": "高卷杏 (P5) (女神异闻录)", "image_path": "assets/output_6_persona__taka<PERSON><PERSON>_anne__persona_23.webp"}, {"prompt": "kantai_collection, no<PERSON>ro (kancolle), kantai collection", "chinese_prompt": "能代 (舰队收藏)", "image_path": "assets/output_6_kantai_collection__no<PERSON><PERSON>__kancolle___kantai_collection_24.webp"}, {"prompt": "fire_emblem, byleth (male) (fire emblem), fire emblem", "chinese_prompt": "贝雷特 (男性) (风花雪月) (圣火降魔录)", "image_path": "assets/output_6_fire_emblem__byleth__male___fire_emblem___fire_emblem_25.webp"}, {"prompt": "voiceroid, koto<PERSON>ha akane, voiceroid", "chinese_prompt": "琴叶茜 (Vocaloid)", "image_path": "assets/output_6_voiceroid__koto<PERSON><PERSON>_akane__voiceroid_26.webp"}, {"prompt": "blue_archive, kokona (blue archive), blue archive", "chinese_prompt": "春原心菜 (蔚蓝档案)", "image_path": "assets/output_6_blue_archive__kokona__blue_archive___blue_archive_27.webp"}, {"prompt": "azur_lane, javelin (azur lane), azur lane", "chinese_prompt": "标枪 (碧蓝航线)", "image_path": "assets/output_6_azur_lane__javelin__azur_lane___azur_lane_28.webp"}, {"prompt": "gochuumon_wa_usagi_desu_ka?, u<PERSON><PERSON> chiya, gochuumon wa usagi desu ka?", "chinese_prompt": "宇治松千夜 (请问您今天要来点兔子吗?)", "image_path": "assets/output_6_gochuumon_wa_usagi_desu_ka___u<PERSON><PERSON>_chiya__gochuumon_wa_usagi_desu_ka__29.webp"}, {"prompt": "kantai_collection, choukai (kancolle), kantai collection", "chinese_prompt": "鸟海 (舰队收藏)", "image_path": "assets/output_6_kantai_collection__chou<PERSON>__kancolle___kantai_collection_30.webp"}, {"prompt": "jojo_no_kimyou_na_bouken, johnny j<PERSON><PERSON>, jojo no kimyou na bouken", "chinese_prompt": "乔尼·乔斯达 (七乔) (JOJO的奇妙冒险)", "image_path": "assets/output_6_jojo_no_kimyou_na_bouken__johnny_joestar__jojo_no_kimyou_na_bouken_31.webp"}, {"prompt": "fate_(series), iris<PERSON><PERSON> von <PERSON>, fate (series)", "chinese_prompt": "爱丽丝菲尔·冯·爱因兹贝伦 (Fate)", "image_path": "assets/output_6_fate__series___irisvie<PERSON>_<PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>__fate__series__32.webp"}, {"prompt": "kantai_collection, te<PERSON><PERSON> (kancolle), kantai collection", "chinese_prompt": "照月 (舰队收藏)", "image_path": "assets/output_6_kantai_collection__te<PERSON><PERSON>__kancolle___kantai_collection_33.webp"}, {"prompt": "<PERSON><PERSON><PERSON><PERSON>, t-head trainer, umamus<PERSON>", "chinese_prompt": "T-Head 培训师 (赛马娘)", "image_path": "assets/output_6_umamusume__t-head_trainer__umamusume_34.webp"}, {"prompt": "persona, sakura futaba, persona", "chinese_prompt": "佐仓双叶 (P5) (女神异闻录)", "image_path": "assets/output_6_persona__sakura_futaba__persona_35.webp"}, {"prompt": "umamusume, tamamo cross (umamusume), umamusume", "chinese_prompt": "玉藻十字 (赛马娘)", "image_path": "assets/output_6_umamusume__tamamo_cross__umamusume___umamusume_36.webp"}, {"prompt": "hololive, <PERSON><PERSON><PERSON> (1st costume), hololive", "chinese_prompt": "星街彗星 (1st服), (Hololive)", "image_path": "assets/output_6_hololive__ho<PERSON><PERSON>_suisei__1st_costume___hololive_37.webp"}, {"prompt": "sonic_(series), shadow the hedgehog, sonic (series)", "chinese_prompt": "夏特 (音速小子)", "image_path": "assets/output_6_sonic__series___shadow_the_hedgehog__sonic__series__38.webp"}, {"prompt": "omori, omori (omori), omori", "chinese_prompt": "OMORI (Omori)", "image_path": "assets/output_6_omori__omori__omori___omori_39.webp"}, {"prompt": "ni<PERSON><PERSON><PERSON>, ho<PERSON><PERSON> sara, ni<PERSON><PERSON><PERSON>", "chinese_prompt": "星川莎拉 (彩虹社)", "image_path": "assets/output_6_niji<PERSON><PERSON>__ho<PERSON><PERSON>_sara__niji<PERSON>ji_40.webp"}, {"prompt": "bang_dream!, s<PERSON><PERSON> taki, bang dream!", "chinese_prompt": "椎名立希 (Bang Dream!)", "image_path": "assets/output_6_bang_dream___shi<PERSON>_taki__bang_dream__41.webp"}, {"prompt": "pokemon, jessie (pokemon), pokemon", "chinese_prompt": "武藏 (宝可梦)", "image_path": "assets/output_6_pokemon__jessie__pokemon___pokemon_42.webp"}, {"prompt": "pokemon, glaceon, pokemon", "chinese_prompt": "冰伊布 (宝可梦)", "image_path": "assets/output_6_pokemon__glaceon__pokemon_43.webp"}, {"prompt": "pokemon, skyla (pokemon), pokemon", "chinese_prompt": "风露 (宝可梦)", "image_path": "assets/output_6_pokemon__skyla__pokemon___pokemon_44.webp"}, {"prompt": "date_a_live, to<PERSON><PERSON>, date a live", "chinese_prompt": "时崎狂三 (约会大作战)", "image_path": "assets/output_6_date_a_live__to<PERSON><PERSON>_kuru<PERSON>__date_a_live_45.webp"}, {"prompt": "idolmaster, s<PERSON><PERSON><PERSON>, idolmaster", "chinese_prompt": "盐见周子 (灰姑娘) (偶像大师)", "image_path": "assets/output_6_idolmaster__shi<PERSON>_s<PERSON><PERSON>__idolmaster_46.webp"}, {"prompt": "pokemon, bulbasaur, pokemon", "chinese_prompt": "妙蛙种子 (宝可梦)", "image_path": "assets/output_6_pokemon__bulbasaur__pokemon_47.webp"}, {"prompt": "gintama, sakata gintoki, gintama", "chinese_prompt": "坂田银时 (银魂)", "image_path": "assets/output_6_gintama__sakata_gintoki__gintama_48.webp"}, {"prompt": "hololive, ta<PERSON><PERSON> kiara (1st costume), hololive", "chinese_prompt": "小鸟游琪亚拉 (1st服) (Hololive)", "image_path": "assets/output_6_hololive__ta<PERSON><PERSON>_k<PERSON>__1st_costume___hololive_49.webp"}, {"prompt": "umamus<PERSON>, matika<PERSON> (umamusume), umamusume", "chinese_prompt": "待兼诗歌剧 (赛马娘)", "image_path": "assets/output_6_umamusume__matika<PERSON>_tan<PERSON><PERSON><PERSON>__umamusume___umamusume_50.webp"}, {"prompt": "chainsaw_man, yoru (chainsaw man), chainsaw man", "chinese_prompt": "夜 (电锯人)", "image_path": "assets/output_6_chainsaw_man__yoru__chainsaw_man___chainsaw_man_51.webp"}, {"prompt": "street_fighter, ryu (street fighter), street fighter", "chinese_prompt": "隆 (快打旋风)", "image_path": "assets/output_6_street_fighter__ryu__street_fighter___street_fighter_52.webp"}, {"prompt": "hololive, minato aqua (1st costume), hololive", "chinese_prompt": "凑阿库娅 (1st服) (Hololive)", "image_path": "assets/output_6_hololive__minato_aqua__1st_costume___hololive_53.webp"}, {"prompt": "shingeki_no_kyojin, levi (shingeki no kyojin), shingeki no kyojin", "chinese_prompt": "里维·阿卡曼 (进击的巨人)", "image_path": "assets/output_6_shingeki_no_kyojin__levi__shingeki_no_kyojin___shingeki_no_kyojin_54.webp"}, {"prompt": "kantai_collection, nagatsuki (kancolle), kantai collection", "chinese_prompt": "长月 (舰队收藏)", "image_path": "assets/output_6_kantai_collection__nagatsuki__kancolle___kantai_collection_55.webp"}, {"prompt": "genshin_impact, cyno (genshin impact), genshin impact", "chinese_prompt": "赛诺 (原神)", "image_path": "assets/output_6_genshin_impact__cyno__genshin_impact___genshin_impact_56.webp"}, {"prompt": "honkai_(series), blade (honkai: star rail), honkai (series)", "chinese_prompt": "刃 (崩坏: 星穹铁道) (崩坏)", "image_path": "assets/output_6_honkai__series___blade__honkai__star_rail___honkai__series__57.webp"}, {"prompt": "fate_(series), leonardo da vinci (fate), fate (series)", "chinese_prompt": "莱昂纳多 达文西 (Fate)", "image_path": "assets/output_6_fate__series___leonardo_da_vinci__fate___fate__series__58.webp"}, {"prompt": "fate_(series), jeanne d'arc alter santa lily (fate), fate (series)", "chinese_prompt": "黑贞德 (圣诞莉莉) (Fate)", "image_path": "assets/output_6_fate__series___jeanne_d_arc_alter_santa_lily__fate___fate__series__59.webp"}, {"prompt": "hololive, yuzuki choco, hololive", "chinese_prompt": "愈月巧可 (Hololive)", "image_path": "assets/output_6_hololive__yuzuki_choco__hololive_60.webp"}, {"prompt": "fate_(series), elizabeth bathory (fate/extra ccc), fate (series)", "chinese_prompt": "伊莉莎白・巴托里 (fate/extra ccc), (Fate)", "image_path": "assets/output_6_fate__series___elizabeth_bathory__fate_extra_ccc___fate__series__61.webp"}, {"prompt": "kantai_collection, maru-yu (kancolle), kantai collection", "chinese_prompt": "马路由 (舰队收藏)", "image_path": "assets/output_6_kantai_collection__maru-yu__kancolle___kantai_collection_62.webp"}, {"prompt": "yuri!!!_on_ice, katsuki yuuri, yuri!!! on ice", "chinese_prompt": "胜生勇利 (勇利!!! on ICE)", "image_path": "assets/output_6_yuri____on_ice__katsuki_yuuri__yuri____on_ice_63.webp"}, {"prompt": "girls_und_panzer, and<PERSON> (girls und panzer), girls und panzer", "chinese_prompt": "安住 (少女与战车)", "image_path": "assets/output_6_girls_und_panzer__andou__girls_und_panzer___girls_und_panzer_64.webp"}, {"prompt": "project_sekai, as<PERSON><PERSON> ma<PERSON>, project sekai", "chinese_prompt": "Project Sekai, 朝日名冬, Project Sekai", "image_path": "assets/output_6_project_sekai__asahina_ma<PERSON><PERSON>__project_sekai_65.webp"}, {"prompt": "lyrical_nanoha, vivio, lyrical nanoha", "chinese_prompt": "薇薇欧 (魔法少女奈叶)", "image_path": "assets/output_6_lyrical_nanoha__vivio__lyrical_nanoha_66.webp"}, {"prompt": "idolmaster, sasaki chie, idolmaster", "chinese_prompt": "佐佐木千枝 (灰姑娘) (偶像大师)", "image_path": "assets/output_6_idolmaster__sasaki_chie__idolmaster_67.webp"}, {"prompt": "idolmaster, k<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, idolmaster", "chinese_prompt": "桑山千雪 (闪耀色彩) (偶像大师)", "image_path": "assets/output_6_idolmaster__k<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>__idolmaster_68.webp"}, {"prompt": "dungeon_meshi, falin touden, dungeon meshi", "chinese_prompt": "法琳 (迷宫饭)", "image_path": "assets/output_6_dungeon_meshi__falin_touden__dungeon_meshi_69.webp"}, {"prompt": "lyrical_nanoha, vita (nanoha), lyrical nanoha", "chinese_prompt": "维塔 (魔法少女奈叶)", "image_path": "assets/output_6_lyrical_nanoha__vita__nanoha___lyrical_nanoha_70.webp"}, {"prompt": "idolmaster, morino rinze, idolmaster", "chinese_prompt": "杜野凛世 (闪耀色彩) (偶像大师)", "image_path": "assets/output_6_idolmaster__morino_rinze__idolmaster_71.webp"}, {"prompt": "chainsaw_man, hay<PERSON><PERSON> aki, chainsaw man", "chinese_prompt": "早川秋 (电锯人)", "image_path": "assets/output_6_chainsaw_man__hay<PERSON><PERSON>_aki__chainsaw_man_72.webp"}, {"prompt": "touhou, kuro<PERSON>ma saki, touhou", "chinese_prompt": "骊驹早鬼 (东方)", "image_path": "assets/output_6_touhou__kuro<PERSON><PERSON>_saki__touhou_73.webp"}, {"prompt": "guilty_gear, ramlethal valentine, guilty gear", "chinese_prompt": "拉姆蕾萨尔‧华伦泰 (圣骑士之战)", "image_path": "assets/output_6_guilty_gear__ramlethal_valentine__guilty_gear_74.webp"}, {"prompt": "idolmaster, hoshi syoko, idolmaster", "chinese_prompt": "星辉子 (灰姑娘) (偶像大师)", "image_path": "assets/output_6_idolmaster__hoshi_syoko__idolmaster_75.webp"}, {"prompt": "yuri!!!_on_ice, v<PERSON><PERSON>, yuri!!! on ice", "chinese_prompt": "维克托·尼基福罗夫 (勇利!!! on ICE)", "image_path": "assets/output_6_yuri____on_ice__viktor_<PERSON><PERSON><PERSON><PERSON>__yuri____on_ice_76.webp"}, {"prompt": "toaru_majutsu_no_index, index (toaru majutsu no index), toaru majutsu no index", "chinese_prompt": "茵蒂克丝 (魔法禁书目录)", "image_path": "assets/output_6_toaru_majutsu_no_index__index__toaru_majutsu_no_index___toaru_majutsu_no_index_77.webp"}, {"prompt": "genshin_impact, beidou (genshin impact), genshin impact", "chinese_prompt": "北斗 (原神)", "image_path": "assets/output_6_genshin_impact__beidou__genshin_impact___genshin_impact_78.webp"}, {"prompt": "o<PERSON><PERSON><PERSON>-san, mat<PERSON><PERSON>, o<PERSON><PERSON><PERSON>-san", "chinese_prompt": "松野小松 (阿松)", "image_path": "assets/output_6_osomatsu-san__matsuno_osomatsu__osomatsu-san_79.webp"}, {"prompt": "umamusume, haru urara (umamusume), umamusume", "chinese_prompt": "春丽 (赛马娘)", "image_path": "assets/output_6_umamusume__haru_urara__umamusume___umamusume_80.webp"}, {"prompt": "dungeon_meshi, la<PERSON> touden, dungeon meshi", "chinese_prompt": "莱欧斯 (迷宫饭)", "image_path": "assets/output_6_dungeon_meshi__laios_touden__dungeon_meshi_81.webp"}, {"prompt": "blue_archive, ibuki (blue archive), blue archive", "chinese_prompt": "丹花伊吹 (蔚蓝档案)", "image_path": "assets/output_6_blue_archive__ibuki__blue_archive___blue_archive_82.webp"}, {"prompt": "kantai_collection, as<PERSON>o kai ni (kancolle), kantai collection", "chinese_prompt": "朝潮改二 (舰队收藏)", "image_path": "assets/output_6_kantai_collection__asashio_kai_ni__kancolle___kantai_collection_83.webp"}, {"prompt": "<PERSON><PERSON><PERSON><PERSON>, s<PERSON><PERSON> yui<PERSON>, ni<PERSON><PERSON><PERSON>", "chinese_prompt": "椎名唯华 (彩虹社)", "image_path": "assets/output_6_niji<PERSON><PERSON>__s<PERSON><PERSON>_yui<PERSON>__niji<PERSON><PERSON>_84.webp"}, {"prompt": "precure, misumi nagisa, precure", "chinese_prompt": "美墨渚 (光之美少女)", "image_path": "assets/output_6_precure__misumi_nagisa__precure_85.webp"}, {"prompt": "umamusume, mr. c.b. (umamusume), umamusume", "chinese_prompt": "千明代表 (赛马娘)", "image_path": "assets/output_6_umamusume__mr__c_b___umamusume___umamusume_86.webp"}, {"prompt": "idolmaster, hojo karen, idolmaster", "chinese_prompt": "北条加莲 (灰姑娘) (偶像大师)", "image_path": "assets/output_6_idolmaster__hojo_karen__idolmaster_87.webp"}, {"prompt": "touhou, horikawa raiko, touhou", "chinese_prompt": "堀川雷鼓 (东方)", "image_path": "assets/output_6_touh<PERSON>__ho<PERSON><PERSON>_rai<PERSON>__touhou_88.webp"}, {"prompt": "girls_und_panzer, pepperoni (girls und panzer), girls und panzer", "chinese_prompt": "佩帕罗妮 (少女与战车)", "image_path": "assets/output_6_girls_und_panzer__pepperoni__girls_und_panzer___girls_und_panzer_89.webp"}, {"prompt": "pokemon, morpeko (full), pokemon", "chinese_prompt": "玛俐 莫鲁贝可 (宝可梦), (宝可梦)", "image_path": "assets/output_6_pokemon__morpeko__full___pokemon_90.webp"}, {"prompt": "o<PERSON><PERSON><PERSON>-san, mat<PERSON><PERSON>, o<PERSON><PERSON><PERSON>-san", "chinese_prompt": "松野空松 (阿松)", "image_path": "assets/output_6_osomatsu-san__matsun<PERSON>_karamatsu__osomatsu-san_91.webp"}, {"prompt": "sana_channel, natori sana, sana channel", "chinese_prompt": "名取纱那 (Sana Channel)", "image_path": "assets/output_6_sana_channel__natori_sana__sana_channel_92.webp"}, {"prompt": "umamusume, narita brian (umamusume), umamusume", "chinese_prompt": "成田白仁 (赛马娘)", "image_path": "assets/output_6_umamusume__narita_brian__umamusume___umamusume_93.webp"}, {"prompt": "su<PERSON><PERSON>_haru<PERSON>_no_yuu<PERSON><PERSON>, k<PERSON><PERSON><PERSON> itsuki, suzumiya haruhi no yuuutsu", "chinese_prompt": "小泉一树 (凉宫春日的忧郁)", "image_path": "assets/output_6_su<PERSON><PERSON>_haruhi_no_yuuutsu__koizu<PERSON>_itsuki__suzu<PERSON>_haruhi_no_yuuutsu_94.webp"}, {"prompt": "disgaea, etna (disgaea), disgaea", "chinese_prompt": "艾特娜 (魔界战记)", "image_path": "assets/output_6_disgaea__etna__disgaea___disgaea_95.webp"}, {"prompt": "kantai_collection, kuroshio (kancolle), kantai collection", "chinese_prompt": "黑潮 (舰队收藏)", "image_path": "assets/output_6_kantai_collection__kuro<PERSON><PERSON>__kancolle___kantai_collection_96.webp"}, {"prompt": "blue_archive, hikari (blue archive), blue archive", "chinese_prompt": "橘光 (蔚蓝档案)", "image_path": "assets/output_6_blue_archive__hikari__blue_archive___blue_archive_97.webp"}, {"prompt": "lucky_star, takara miyuki, lucky star", "chinese_prompt": "高良美幸 (幸运星)", "image_path": "assets/output_6_lucky_star__takara_miyuki__lucky_star_98.webp"}, {"prompt": "pokemon, mimikyu, pokemon", "chinese_prompt": "谜拟Q (宝可梦)", "image_path": "assets/output_6_pokemon__mimikyu__pokemon_99.webp"}, {"prompt": "blue_archive, nozomi (blue archive), blue archive", "chinese_prompt": "橘希望 (蔚蓝档案)", "image_path": "assets/output_6_blue_archive__nozomi__blue_archive___blue_archive_100.webp"}, {"prompt": "lyrical_nanoha, bardiche, lyrical nanoha", "chinese_prompt": "菲特·泰斯塔罗沙 雷光战斧 (魔法少女奈叶)", "image_path": "assets/output_6_lyrical_nanoha__bardiche__lyrical_nanoha_101.webp"}, {"prompt": "elden_ring, ranni the witch, elden ring", "chinese_prompt": "菈妮 (艾尔登法环)", "image_path": "assets/output_6_elden_ring__ranni_the_witch__elden_ring_102.webp"}, {"prompt": "kantai_collection, katsuragi (kancolle), kantai collection", "chinese_prompt": "葛城 (舰队收藏)", "image_path": "assets/output_6_kantai_collection__katsu<PERSON>i__kancolle___kantai_collection_103.webp"}, {"prompt": "saibou_shinkyoku, atou haruki, saibou shinkyoku", "chinese_prompt": "阿藤春树 (细胞神曲)", "image_path": "assets/output_6_saibou_shinkyoku__atou_haruki__saibou_shinkyoku_104.webp"}, {"prompt": "fate_(series), artoria pendragon (lancer alter) (fate), fate (series)", "chinese_prompt": "阿尔托莉亚·潘德拉贡 (枪兵 Alter) (Fate)", "image_path": "assets/output_6_fate__series___artoria_pendragon__lancer_alter___fate___fate__series__105.webp"}, {"prompt": "fate_(series), koya<PERSON><PERSON> (fate), fate (series)", "chinese_prompt": "高扬斯卡娅 (Fate)", "image_path": "assets/output_6_fate__series___koyanskaya__fate___fate__series__106.webp"}, {"prompt": "ni<PERSON><PERSON><PERSON>, ange katrina, ni<PERSON><PERSON><PERSON>", "chinese_prompt": "安洁·卡特莉娜 (彩虹社)", "image_path": "assets/output_6_niji<PERSON><PERSON>__ange_katrina__niji<PERSON>ji_107.webp"}, {"prompt": "fate_(series), nero claudius (bride) (fate), fate (series)", "chinese_prompt": "尼禄 (新娘) (Fate)", "image_path": "assets/output_6_fate__series___nero_claudius__bride___fate___fate__series__108.webp"}, {"prompt": "blue_archive, shun (blue archive), blue archive", "chinese_prompt": "春原旬 (蔚蓝档案)", "image_path": "assets/output_6_blue_archive__shun__blue_archive___blue_archive_109.webp"}, {"prompt": "toaru_majutsu_no_index, last order (toaru majutsu no index), toaru majutsu no index", "chinese_prompt": "最后之作 (魔法禁书目录)", "image_path": "assets/output_6_toaru_majutsu_no_index__last_order__toaru_majutsu_no_index___toaru_majutsu_no_index_110.webp"}, {"prompt": "guilty_crown, yuz<PERSON><PERSON> inori, guilty crown", "chinese_prompt": "楪祈 (罪恶王冠)", "image_path": "assets/output_6_guilty_crown__yuzuriha_inori__guilty_crown_111.webp"}, {"prompt": "umamusume, agnes digital (umamusume), umamusume", "chinese_prompt": "爱丽数码 (赛马娘)", "image_path": "assets/output_6_umamusume__agnes_digital__umamusume___umamusume_112.webp"}, {"prompt": "umamusume, sirius symboli (umamusume), umamusume", "chinese_prompt": "天狼星象征 (赛马娘)", "image_path": "assets/output_6_umamusume__sirius_symboli__umamusume___umamusume_113.webp"}, {"prompt": "ragnarok_online, arch bishop (ragnarok online), ragnarok online", "chinese_prompt": "主教 (仙境传说RO)", "image_path": "assets/output_6_ragnarok_online__arch_bishop__ragnarok_online___ragnarok_online_114.webp"}, {"prompt": "blue_archive, mari (track) (blue archive), blue archive", "chinese_prompt": "伊落玛丽 (体操服) (蔚蓝档案)", "image_path": "assets/output_6_blue_archive__mari__track___blue_archive___blue_archive_115.webp"}, {"prompt": "persona, takeba yukari, persona", "chinese_prompt": "岳羽由加莉 (P3) (女神异闻录)", "image_path": "assets/output_6_persona__takeba_yukari__persona_116.webp"}, {"prompt": "boku_no_hero_academia, ya<PERSON><PERSON><PERSON> momo, boku no hero academia", "chinese_prompt": "八百万百 我的英雄学院", "image_path": "assets/output_6_boku_no_hero_academia__ya<PERSON><PERSON><PERSON>_momo__boku_no_hero_academia_117.webp"}, {"prompt": "nijisanji, inui toko, nijisanji", "chinese_prompt": "戌亥床 (彩虹社)", "image_path": "assets/output_6_niji<PERSON><PERSON>__inui_toko__niji<PERSON>ji_118.webp"}, {"prompt": "<PERSON><PERSON><PERSON><PERSON>-san, <PERSON><PERSON><PERSON>, o<PERSON><PERSON><PERSON>-san", "chinese_prompt": "松野十四松 (阿松)", "image_path": "assets/output_6_osomatsu-san__mat<PERSON><PERSON>_j<PERSON><PERSON><PERSON>__osomatsu-san_119.webp"}, {"prompt": "o<PERSON><PERSON><PERSON>-san, mat<PERSON><PERSON>, o<PERSON><PERSON><PERSON>-san", "chinese_prompt": "松野一松 (阿松)", "image_path": "assets/output_6_osomatsu-san__matsun<PERSON>_i<PERSON><PERSON><PERSON>__osomatsu-san_120.webp"}, {"prompt": "kantai_collection, yayoi (kancolle), kantai collection", "chinese_prompt": "弥生 (舰队收藏)", "image_path": "assets/output_6_kantai_collection__yayoi__kancolle___kantai_collection_121.webp"}, {"prompt": "apex_legends, wattson (apex legends), apex legends", "chinese_prompt": "华森 (Apex Legends)", "image_path": "assets/output_6_apex_legends__wattson__apex_legends___apex_legends_122.webp"}, {"prompt": "fire_emblem, tiki (fire emblem), fire emblem", "chinese_prompt": "琪姬 (黑暗龙与光之剑) (圣火降魔录)", "image_path": "assets/output_6_fire_emblem__tiki__fire_emblem___fire_emblem_123.webp"}, {"prompt": "kantai_collection, musashi kai ni (kancolle), kantai collection", "chinese_prompt": "武藏改二 (舰队收藏)", "image_path": "assets/output_6_kantai_collection__musashi_kai_ni__kancolle___kantai_collection_124.webp"}, {"prompt": "idolmaster, kamiya nao, idolmaster", "chinese_prompt": "神谷奈绪 (灰姑娘) (偶像大师)", "image_path": "assets/output_6_idolmaster__ka<PERSON>_nao__idolmaster_125.webp"}, {"prompt": "kaguya-sama_wa_kokurasetai_~tensai-tachi_no_renai_zunousen~, fu<PERSON><PERSON> chika, kaguya-sama wa kokurasetai ~tensai-tachi no renai zunousen~", "chinese_prompt": "藤原千花 (辉夜姬想让人告白)", "image_path": "assets/output_6_kaguya-sama_wa_kokurasetai__tensai-tachi_no_renai_zunousen___fuji<PERSON>_chika__kaguya-sama_wa_kokurasetai__tensai-tachi_no_renai_zunousen__126.webp"}, {"prompt": "genshin_impact, noelle (genshin impact), genshin impact", "chinese_prompt": "诺艾尔 (原神)", "image_path": "assets/output_6_genshin_impact__noelle__genshin_impact___genshin_impact_127.webp"}, {"prompt": "kantai_collection, shir<PERSON>uki (kancolle), kantai collection", "chinese_prompt": "白雪 (舰队收藏)", "image_path": "assets/output_6_kantai_collection__shiray<PERSON>__kancolle___kantai_collection_128.webp"}, {"prompt": "to<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> megumu, touh<PERSON>", "chinese_prompt": "饭纲丸龙 (东方)", "image_path": "assets/output_6_touh<PERSON>__ii<PERSON><PERSON><PERSON>_megumu__touhou_129.webp"}, {"prompt": "overwatch, tracer (overwatch), overwatch", "chinese_prompt": "闪光 (斗阵特攻)", "image_path": "assets/output_6_overwatch__tracer__overwatch___overwatch_130.webp"}, {"prompt": "original, kemomimi-chan (naga u), original", "chinese_prompt": "猫耳小女孩 (原创)", "image_path": "assets/output_6_original__kemomimi-chan__naga_u___original_131.webp"}, {"prompt": "genshin_impact, navia (genshin impact), genshin impact", "chinese_prompt": "娜维娅 (原神)", "image_path": "assets/output_6_genshin_impact__navia__genshin_impact___genshin_impact_132.webp"}, {"prompt": "vampire_(game), felicia (vampire), vampire (game)", "chinese_prompt": "菲莉西亚 (魔域幽灵)", "image_path": "assets/output_6_vampire__game___felicia__vampire___vampire__game__133.webp"}, {"prompt": "idolmaster, moro<PERSON><PERSON> k<PERSON>, idolmaster", "chinese_prompt": "诸星琪拉莉 (灰姑娘) (偶像大师)", "image_path": "assets/output_6_idolmaster__moro<PERSON><PERSON>_kira<PERSON>__idolmaster_134.webp"}, {"prompt": "link!_like!_love_live!, momose ginko, link! like! love live!", "chinese_prompt": "百生吟子 (Link! Like! Love Live!)", "image_path": "assets/output_6_link__like__love_live___momose_ginko__link__like__love_live__135.webp"}, {"prompt": "pokemon, silver (pokemon), pokemon", "chinese_prompt": "银伴战兽 (宝可梦)", "image_path": "assets/output_6_pokemon__silver__pokemon___pokemon_136.webp"}, {"prompt": "kobayashi-san_chi_no_maidragon, to<PERSON><PERSON> (maidragon), kobayashi-san chi no maidragon", "chinese_prompt": "托尔 (小林家的龙女仆)", "image_path": "assets/output_6_kobayashi-san_chi_no_maidragon__tohru__maidragon___kobayashi-san_chi_no_maidragon_137.webp"}, {"prompt": "persona, ha<PERSON><PERSON> <PERSON><PERSON>, persona", "chinese_prompt": "花村阳介 (P4) (女神异闻录)", "image_path": "assets/output_6_persona__ha<PERSON><PERSON>_yousuke__persona_138.webp"}, {"prompt": "touhou, ringo (touhou), touhou", "chinese_prompt": "铃瑚 (东方)", "image_path": "assets/output_6_touhou__ringo__touhou___touhou_139.webp"}, {"prompt": "the_legend_of_zelda, midna, the legend of zelda", "chinese_prompt": "米德娜 (暮光公主) (塞尔达传说)", "image_path": "assets/output_6_the_legend_of_zelda__midna__the_legend_of_zelda_140.webp"}, {"prompt": "precure, ha<PERSON><PERSON> tsu<PERSON>, precure", "chinese_prompt": "花咲蕾 (光之美少女)", "image_path": "assets/output_6_precure__hanas<PERSON>_tsu<PERSON>mi__precure_141.webp"}, {"prompt": "kantai_collection, tama (kancolle), kantai collection", "chinese_prompt": "多摩 (舰队收藏)", "image_path": "assets/output_6_kantai_collection__tama__kancolle___kantai_collection_142.webp"}, {"prompt": "umamusume, grass wonder (umamusume), umamusume", "chinese_prompt": "草上飞 (赛马娘)", "image_path": "assets/output_6_umamusume__grass_wonder__umamusume___umamusume_143.webp"}, {"prompt": "touhou, komano aunn, touhou", "chinese_prompt": "高丽野阿吽 (东方)", "image_path": "assets/output_6_touhou__komano_aunn__touhou_144.webp"}, {"prompt": "jujutsu_kaisen, k<PERSON><PERSON><PERSON>, jujutsu kaisen", "chinese_prompt": "钉崎野蔷薇 (咒术回战)", "image_path": "assets/output_6_jujutsu_kaisen__kugisaki_nobara__jujutsu_kaisen_145.webp"}, {"prompt": "hololive, s<PERSON><PERSON> botan (1st costume), hololive", "chinese_prompt": "狮白牡丹 (1st服) (Hololive)", "image_path": "assets/output_6_hololive__s<PERSON><PERSON>_botan__1st_costume___hololive_146.webp"}, {"prompt": "touhou, eternity larva, touhou", "chinese_prompt": "爱塔妮缇拉尔瓦 (东方)", "image_path": "assets/output_6_touhou__eternity_larva__touhou_147.webp"}, {"prompt": "kantai_collection, sendai kai ni (kancolle), kantai collection", "chinese_prompt": "川内改二 (舰队收藏)", "image_path": "assets/output_6_kantai_collection__sendai_kai_ni__kancolle___kantai_collection_148.webp"}, {"prompt": "princess_connect!, saren (princess connect!), princess connect!", "chinese_prompt": "佐佐木咲恋 (公主连结)", "image_path": "assets/output_6_princess_connect___saren__princess_connect____princess_connect__149.webp"}, {"prompt": "kantai_collection, as<PERSON><PERSON> (kancolle), kantai collection", "chinese_prompt": "朝霜 (舰队收藏)", "image_path": "assets/output_6_kantai_collection__asashi<PERSON>__kancolle___kantai_collection_150.webp"}, {"prompt": "fate_(series), marie antoinette (fate), fate (series)", "chinese_prompt": "玛丽·安托瓦内特 (命运), (Fate)", "image_path": "assets/output_6_fate__series___marie_antoinette__fate___fate__series__151.webp"}, {"prompt": "project_moon, don quixote (project moon), project moon", "chinese_prompt": "唐吉诃德 (Project Moon)", "image_path": "assets/output_6_project_moon__don_quixote__project_moon___project_moon_152.webp"}, {"prompt": "kantai_collection, suzukaze (kancolle), kantai collection", "chinese_prompt": "凉风 (舰队收藏)", "image_path": "assets/output_6_kantai_collection__su<PERSON>ze__kancolle___kantai_collection_153.webp"}, {"prompt": "sword_art_online, kirigaya suguha, sword art online", "chinese_prompt": "桐谷直叶 (刀剑神域)", "image_path": "assets/output_6_sword_art_online__kirigaya_suguha__sword_art_online_154.webp"}, {"prompt": "fire_emblem, alear (fire emblem), fire emblem", "chinese_prompt": "琉尔 (Engage) (圣火降魔录)", "image_path": "assets/output_6_fire_emblem__alear__fire_emblem___fire_emblem_155.webp"}, {"prompt": "yuuki_bakuhatsu_bang_bravern, ao isami, yuuki bakuhatsu bang bravern", "chinese_prompt": "勇·碧 (勇气爆发Bang Bravern)", "image_path": "assets/output_6_yuuki_bakuhatsu_bang_bravern__ao_isami__yuuki_bakuhatsu_bang_bravern_156.webp"}, {"prompt": "precure, ken<PERSON> makoto, precure", "chinese_prompt": "剑崎真琴 (光之美少女)", "image_path": "assets/output_6_precure__ken<PERSON>_makoto__precure_157.webp"}, {"prompt": "bishoujo_senshi_sailor_moon, hino rei, bishoujo senshi sailor moon", "chinese_prompt": "火野丽 水手火星 (美少女战士)", "image_path": "assets/output_6_bishoujo_senshi_sailor_moon__hino_rei__bishoujo_senshi_sailor_moon_158.webp"}, {"prompt": "vocaloid, yowane haku, vocaloid", "chinese_prompt": "弱音白 (Vocaloid)", "image_path": "assets/output_6_vocaloid__yowane_haku__vocaloid_159.webp"}, {"prompt": "pokemon, elesa (pokemon), pokemon", "chinese_prompt": "小菊儿 (宝可梦)", "image_path": "assets/output_6_pokemon__elesa__pokemon___pokemon_160.webp"}, {"prompt": "idolmaster, <PERSON><PERSON><PERSON> a<PERSON>ra, idolmaster", "chinese_prompt": "砂冢明 (灰姑娘) (偶像大师)", "image_path": "assets/output_6_idolmaster__sun<PERSON><PERSON>_a<PERSON><PERSON>__idolmaster_161.webp"}, {"prompt": "hololive, bloop (gawr gura), hololive", "chinese_prompt": "Bloop (噶呜·古拉) (鲨鲨) (Hololive)", "image_path": "assets/output_6_hololive__bloop__gawr_gura___hololive_162.webp"}, {"prompt": "amagami, nanasaki ai, amagami", "chinese_prompt": "七咲逢 (Amagami)", "image_path": "assets/output_6_amagami__nanasaki_ai__amagami_163.webp"}, {"prompt": "project_sekai, shinonome ena, project sekai", "chinese_prompt": "Project Sekai, 东云圆, Project Sekai", "image_path": "assets/output_6_project_sekai__shinonome_ena__project_sekai_164.webp"}, {"prompt": "blue_archive, wakamo (blue archive), blue archive", "chinese_prompt": "狐坂若藻 (蔚蓝档案)", "image_path": "assets/output_6_blue_archive__wakamo__blue_archive___blue_archive_165.webp"}, {"prompt": "kantai_collection, kasumi kai ni (kancolle), kantai collection", "chinese_prompt": "霞改二 (舰队收藏)", "image_path": "assets/output_6_kantai_collection__kasumi_kai_ni__kancolle___kantai_collection_166.webp"}, {"prompt": "kantai_collection, arashio (kancolle), kantai collection", "chinese_prompt": "荒潮 (舰队收藏)", "image_path": "assets/output_6_kantai_collection__a<PERSON><PERSON>__kancolle___kantai_collection_167.webp"}, {"prompt": "danganron<PERSON>_(series), momota kaito, dangan<PERSON><PERSON> (series)", "chinese_prompt": "百田解斗 (弹丸论破)", "image_path": "assets/output_6_danganronpa__series___momota_kaito__danganronpa__series__168.webp"}, {"prompt": "idolmaster, <PERSON><PERSON>, idolmaster", "chinese_prompt": "大崎甘奈 (闪耀色彩) (偶像大师)", "image_path": "assets/output_6_idolmaster__<PERSON><PERSON>_amana__idolmaster_169.webp"}, {"prompt": "arknights, irene (arknights), arknights", "chinese_prompt": "艾丽妮 (明日方舟)", "image_path": "assets/output_6_arknights__irene__arknights___arknights_170.webp"}, {"prompt": "genshin_impact, lynette (genshin impact), genshin impact", "chinese_prompt": "琳妮特 (原神)", "image_path": "assets/output_6_genshin_impact__lynette__genshin_impact___genshin_impact_171.webp"}, {"prompt": "idolmaster, se<PERSON><PERSON>, idolmaster", "chinese_prompt": "芹泽朝日 (闪耀色彩) (偶像大师)", "image_path": "assets/output_6_idolmaster__se<PERSON><PERSON>_as<PERSON>__idolmaster_172.webp"}, {"prompt": "bleach, shihouin yoru<PERSON>, bleach", "chinese_prompt": "四枫院夜一 (死神)", "image_path": "assets/output_6_bleach__shi<PERSON><PERSON>_yo<PERSON><PERSON>__bleach_173.webp"}, {"prompt": "blue_archive, natsu (blue archive), blue archive", "chinese_prompt": "柚鸟夏 (蔚蓝档案)", "image_path": "assets/output_6_blue_archive__natsu__blue_archive___blue_archive_174.webp"}, {"prompt": "jujutsu_kaisen, na<PERSON>i kento, jujutsu kaisen", "chinese_prompt": "七海建人 (咒术回战)", "image_path": "assets/output_6_jujutsu_kaisen__nanami_kento__jujutsu_kaisen_175.webp"}, {"prompt": "fate_(series), barghest (fate), fate (series)", "chinese_prompt": "巴格斯特 (Fate)", "image_path": "assets/output_6_fate__series___barghest__fate___fate__series__176.webp"}, {"prompt": "persona, kiri<PERSON> mitsu<PERSON>, persona", "chinese_prompt": "桐条美鹤 (P3) (女神异闻录)", "image_path": "assets/output_6_persona__kiri<PERSON>_mitsuru__persona_177.webp"}, {"prompt": "fullmetal_alchemist, edward elric, fullmetal alchemist", "chinese_prompt": "爱德华·爱力克 (钢之炼金术师)", "image_path": "assets/output_6_fullmetal_alchemist__edward_elric__fullmetal_alchemist_178.webp"}, {"prompt": "world_witches_series, sakamoto mio, world witches series", "chinese_prompt": "坂本美绪 (强袭魔女)", "image_path": "assets/output_6_world_witches_series__sakamoto_mio__world_witches_series_179.webp"}, {"prompt": "idolmaster, t<PERSON><PERSON><PERSON> k<PERSON>e, idolmaster", "chinese_prompt": "月冈恋钟 (闪耀色彩) (偶像大师)", "image_path": "assets/output_6_idolmaster__t<PERSON><PERSON><PERSON>_kogane__idolmaster_180.webp"}, {"prompt": "go-toubun_no_hanayome, nakano itsuki, go-toubun no hanayome", "chinese_prompt": "中野五月 (五等分的新娘)", "image_path": "assets/output_6_go-toubun_no_hanayome__nakano_itsuki__go-toubun_no_hanayome_181.webp"}, {"prompt": "blazblue, makoto na<PERSON>a, blazblue", "chinese_prompt": "诚・七夜 (苍翼默示录)", "image_path": "assets/output_6_blazblue__makoto_nanaya__blazblue_182.webp"}, {"prompt": "hololive, y<PERSON><PERSON> (1st costume), hololive", "chinese_prompt": "雪花菈米 (1st服) (Hololive)", "image_path": "assets/output_6_hololive__yuki<PERSON>_lamy__1st_costume___hololive_183.webp"}, {"prompt": "dead_or_alive, kasumi (doa), dead or alive", "chinese_prompt": "霞 (DOA)", "image_path": "assets/output_6_dead_or_alive__ka<PERSON><PERSON>__doa___dead_or_alive_184.webp"}, {"prompt": "girls_und_panzer, rosehip (girls und panzer), girls und panzer", "chinese_prompt": "玫瑰果 (少女与战车)", "image_path": "assets/output_6_girls_und_panzer__rosehip__girls_und_panzer___girls_und_panzer_185.webp"}, {"prompt": "touhou, to<PERSON><PERSON> yuuma, touhou", "chinese_prompt": "饕餮尤魔 (东方)", "image_path": "assets/output_6_touhou__toutetsu_yuuma__touhou_186.webp"}, {"prompt": "bleach, inoue orihime, bleach", "chinese_prompt": "井上织姬 (死神)", "image_path": "assets/output_6_bleach__inoue_orihime__bleach_187.webp"}, {"prompt": "higurashi_no_naku_koro_ni, hanyuu, higurashi no naku koro ni", "chinese_prompt": "羽入 (暮蝉悲鸣时)", "image_path": "assets/output_6_higurashi_no_naku_koro_ni__hanyuu__higurashi_no_naku_koro_ni_188.webp"}, {"prompt": "onii-chan_wa_oshimai!, o<PERSON> mihari, onii-chan wa oshimai!", "chinese_prompt": "绪山哨 (不当哥哥了)", "image_path": "assets/output_6_onii-chan_wa_oshimai___oyama_mihari__onii-chan_wa_oshimai__189.webp"}, {"prompt": "fire_emblem, marian<PERSON>, fire emblem", "chinese_prompt": "玛莉安奴·冯·艾德蒙 (风花雪月) (圣火降魔录)", "image_path": "assets/output_6_fire_emblem__marian<PERSON>_<PERSON>_<PERSON><PERSON>__fire_emblem_190.webp"}, {"prompt": "blue_archive, haruna (blue archive), blue archive", "chinese_prompt": "黑馆晴奈 (蔚蓝档案)", "image_path": "assets/output_6_blue_archive__haruna__blue_archive___blue_archive_191.webp"}, {"prompt": "overwatch, mei (overwatch), overwatch", "chinese_prompt": "小美 (斗阵特攻)", "image_path": "assets/output_6_overwatch__mei__overwatch___overwatch_192.webp"}, {"prompt": "helltaker, lucifer (helltaker), helltaker", "chinese_prompt": "路西法 (Helltaker)", "image_path": "assets/output_6_helltaker__lucifer__helltaker___helltaker_193.webp"}, {"prompt": "street_fighter, ka<PERSON><PERSON> sakura, street fighter", "chinese_prompt": "春日野樱 (快打旋风)", "image_path": "assets/output_6_street_fighter__kasugano_sakura__street_fighter_194.webp"}, {"prompt": "darling_in_the_franxx, hiro (darling in the franxx), darling in the franxx", "chinese_prompt": "广 (<PERSON> in the Franxx)", "image_path": "assets/output_6_darling_in_the_franxx__hiro__darling_in_the_franxx___darling_in_the_franxx_195.webp"}, {"prompt": "<PERSON><PERSON><PERSON><PERSON>, ma<PERSON><PERSON> riri<PERSON>, ni<PERSON><PERSON><PERSON>", "chinese_prompt": "真崎璃玲 (彩虹社)", "image_path": "assets/output_6_niji<PERSON><PERSON>__ma<PERSON><PERSON>_ririmu__niji<PERSON>ji_196.webp"}, {"prompt": "voiceroid, koto<PERSON>ha aoi, voiceroid", "chinese_prompt": "琴叶葵 (Voiceroid)", "image_path": "assets/output_6_voiceroid__koto<PERSON><PERSON>_aoi__voiceroid_197.webp"}, {"prompt": "pokemon, scorbunny, pokemon", "chinese_prompt": "炎兔儿 (宝可梦)", "image_path": "assets/output_6_pokemon__scorbunny__pokemon_198.webp"}, {"prompt": "arknights, hoshiguma (arknights), arknights", "chinese_prompt": "星熊 (明日方舟)", "image_path": "assets/output_6_arknights__hoshiguma__arknights___arknights_199.webp"}, {"prompt": "hololive, kiryu coco (1st costume), hololive", "chinese_prompt": "桐生可可 (1st服) (Hololive)", "image_path": "assets/output_6_hololive__kiryu_coco__1st_costume___hololive_200.webp"}, {"prompt": "needy_girl_overdose, ame-chan (needy girl overdose), needy girl overdose", "chinese_prompt": "糖糖 (主播女孩重度依赖)", "image_path": "assets/output_6_needy_girl_overdose__ame-chan__needy_girl_overdose___needy_girl_overdose_201.webp"}, {"prompt": "kemono_friends, japanese crested ibis (kemono friends), kemono friends", "chinese_prompt": "朱鹭 (动物朋友)", "image_path": "assets/output_6_kemono_friends__japanese_crested_ibis__kemono_friends___kemono_friends_202.webp"}, {"prompt": "final_fantasy, terra branford, final fantasy", "chinese_prompt": "蒂娜·布兰佛德 (ff6) (最终幻想)", "image_path": "assets/output_6_final_fantasy__terra_branford__final_fantasy_203.webp"}, {"prompt": "lyrical_nanoha, signum, lyrical nanoha", "chinese_prompt": "希格纳姆 (魔法少女奈叶)", "image_path": "assets/output_6_lyrical_nanoha__signum__lyrical_nanoha_204.webp"}, {"prompt": "kantai_collection, ayanami (kancolle), kantai collection", "chinese_prompt": "绫波 (舰队收藏)", "image_path": "assets/output_7_kantai_collection__a<PERSON><PERSON>__kancolle___kantai_collection_0.webp"}, {"prompt": "kantai_collection, fletcher (kancolle), kantai collection", "chinese_prompt": "佛莱契尔 (舰队收藏)", "image_path": "assets/output_7_kantai_collection__fletcher__kancolle___kantai_collection_1.webp"}, {"prompt": "kemono_friends, silver fox (kemono friends), kemono friends", "chinese_prompt": "银狐 (动物朋友)", "image_path": "assets/output_7_kemono_friends__silver_fox__kemono_friends___kemono_friends_2.webp"}, {"prompt": "hololive, k<PERSON><PERSON> ollie, hololive", "chinese_prompt": "克蕾西·奥莉 (<PERSON><PERSON><PERSON>) (Hololive)", "image_path": "assets/output_7_hololive__kure<PERSON>_ollie__hololive_3.webp"}, {"prompt": "bishoujo_senshi_sailor_moon, chibi usa, bishoujo senshi sailor moon", "chinese_prompt": "小小月 (美少女战士)", "image_path": "assets/output_7_bishoujo_senshi_sailor_moon__chibi_usa__bishoujo_senshi_sailor_moon_4.webp"}, {"prompt": "girls_und_panzer, o<PERSON><PERSON> (girls und panzer), girls und panzer", "chinese_prompt": "押田 (少女与战车)", "image_path": "assets/output_7_girls_und_panzer__oshida__girls_und_panzer___girls_und_panzer_5.webp"}, {"prompt": "dungeon_meshi, mithrun, dungeon meshi", "chinese_prompt": "米斯伦 (迷宫饭)", "image_path": "assets/output_7_dungeon_meshi__mithrun__dungeon_meshi_6.webp"}, {"prompt": "cyberpunk_(series), rebecca (cyberpunk), cyberpunk (series)", "chinese_prompt": "雷贝卡 (Cyberpunk)", "image_path": "assets/output_7_cyberpunk__series___rebecca__cyberpunk___cyberpunk__series__7.webp"}, {"prompt": "bang_dream!, hi<PERSON> sayo, bang dream!", "chinese_prompt": "冰川纱夜 (Bang Dream!)", "image_path": "assets/output_7_bang_dream___hikawa_sayo__bang_dream__8.webp"}, {"prompt": "jujutsu_kaisen, get<PERSON> sugu<PERSON>, jujutsu kaisen", "chinese_prompt": "夏油杰 (咒术回战)", "image_path": "assets/output_7_jujutsu_kaisen__getou_suguru__jujutsu_kaisen_9.webp"}, {"prompt": "touh<PERSON>, wa<PERSON><PERSON><PERSON> no yori<PERSON>e, touhou", "chinese_prompt": "绵月依姫 (东方)", "image_path": "assets/output_7_touh<PERSON>__wa<PERSON><PERSON><PERSON>_no_yo<PERSON><PERSON>e__touhou_10.webp"}, {"prompt": "arknights, shu (arknights), arknights", "chinese_prompt": "黍 (明日方舟)", "image_path": "assets/output_7_arknights__shu__arknights___arknights_11.webp"}, {"prompt": "arknights, goldenglow (arknights), arknights", "chinese_prompt": "澄闪 (明日方舟)", "image_path": "assets/output_7_arknights__goldenglow__arknights___arknights_12.webp"}, {"prompt": "girls'_frontline, commander (girls' frontline), girls' frontline", "chinese_prompt": "指挥官 (少女前线)", "image_path": "assets/output_7_girls__frontline__commander__girls__frontline___girls__frontline_13.webp"}, {"prompt": "love_live!, yuuki <PERSON> (love live!), love live!", "chinese_prompt": "优木雪菜 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_7_love_live___yuuki_setsuna__love_live____love_live__14.webp"}, {"prompt": "hololive, tokino sora, hololive", "chinese_prompt": "时乃空 (Hololive)", "image_path": "assets/output_7_hololive__tokino_sora__hololive_15.webp"}, {"prompt": "bishoujo_senshi_sailor_moon, kino makoto, bishoujo senshi sailor moon", "chinese_prompt": "木野真琴 水手木星 (美少女战士)", "image_path": "assets/output_7_bishoujo_senshi_sailor_moon__kino_makoto__bishoujo_senshi_sailor_moon_16.webp"}, {"prompt": "idolmaster, o<PERSON><PERSON> koto<PERSON>, idolmaster", "chinese_prompt": "音无小鸟 (偶像大师)", "image_path": "assets/output_7_idolmaster__o<PERSON><PERSON>_k<PERSON><PERSON>__idolmaster_17.webp"}, {"prompt": "blazblue, noel vermillion, blazblue", "chinese_prompt": "诺爱儿·梵蜜莉欧 (苍翼默示录)", "image_path": "assets/output_7_blazblue__noel_vermillion__blazblue_18.webp"}, {"prompt": "mahou_shoujo_madoka_magica, ta<PERSON><PERSON> i<PERSON>, mahou shoujo madoka magica", "chinese_prompt": "环彩羽 (魔法少女小圆)", "image_path": "assets/output_7_mahou_shoujo_madoka_magica__tamaki_i<PERSON><PERSON>__mahou_shoujo_madoka_magica_19.webp"}, {"prompt": "inazuma_eleven_(series), kirino ranmar<PERSON>, inazuma eleven (series)", "chinese_prompt": "雾野兰丸 (闪电十一人)", "image_path": "assets/output_7_inazuma_eleven__series___kirino_ranmaru__inazuma_eleven__series__20.webp"}, {"prompt": "pokemon, oshawott, pokemon", "chinese_prompt": "水水獭 (宝可梦)", "image_path": "assets/output_7_pokemon__o<PERSON><PERSON>__pokemon_21.webp"}, {"prompt": "idolmaster, producer (idolmaster cinderella girls anime), idolmaster", "chinese_prompt": "制作人 (偶像大师 朝著辉煌的彼岸), (偶像大师)", "image_path": "assets/output_7_idolmaster__producer__idolmaster_cinderella_girls_anime___idolmaster_22.webp"}, {"prompt": "omori, basil (faraway) (omori), omori", "chinese_prompt": "BASIL (faraway), (Omori)", "image_path": "assets/output_7_omori__basil__faraway___omori___omori_23.webp"}, {"prompt": "arknights, blue poison (arknights), arknights", "chinese_prompt": "蓝毒 (明日方舟)", "image_path": "assets/output_7_arknights__blue_poison__arknights___arknights_24.webp"}, {"prompt": "fate_(series), art<PERSON> pendragon (swimsuit ruler) (fate), fate (series)", "chinese_prompt": "阿尔托莉雅·潘德拉贡 (泳装) (Fate)", "image_path": "assets/output_7_fate__series___artoria_pendragon__swimsuit_ruler___fate___fate__series__25.webp"}, {"prompt": "idolmaster, sakuma may<PERSON>, idolmaster", "chinese_prompt": "佐久间麻由 (灰姑娘) (偶像大师)", "image_path": "assets/output_7_idolmaster__saku<PERSON>_mayu__idolmaster_26.webp"}, {"prompt": "kantai_collection, agano (kancolle), kantai collection", "chinese_prompt": "阿贺野 (舰队收藏)", "image_path": "assets/output_7_kantai_collection__agano__kancolle___kantai_collection_27.webp"}, {"prompt": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> ka<PERSON>, ni<PERSON><PERSON><PERSON>", "chinese_prompt": "樋口枫 (彩虹社)", "image_path": "assets/output_7_niji<PERSON><PERSON>__hi<PERSON>_ka<PERSON>__niji<PERSON><PERSON>_28.webp"}, {"prompt": "kantai_collection, myoukou (kancolle), kantai collection", "chinese_prompt": "妙高 (舰队收藏)", "image_path": "assets/output_7_kantai_collection__myoukou__kancolle___kantai_collection_29.webp"}, {"prompt": "dragon_ball, son gohan, dragon ball", "chinese_prompt": "孙悟饭 (七龙珠)", "image_path": "assets/output_7_dragon_ball__son_gohan__dragon_ball_30.webp"}, {"prompt": "rozen_maiden, souse<PERSON><PERSON>, rozen maiden", "chinese_prompt": "苍星石 (蔷薇少女)", "image_path": "assets/output_7_rozen_maiden__souseiseki__rozen_maiden_31.webp"}, {"prompt": "pokemon, vaporeon, pokemon", "chinese_prompt": "水伊布 (宝可梦)", "image_path": "assets/output_7_pokemon__vaporeon__pokemon_32.webp"}, {"prompt": "hololive, to<PERSON><PERSON><PERSON> towa (1st costume), hololive", "chinese_prompt": "常暗永远 (1st服) (Hololive)", "image_path": "assets/output_7_hololive__to<PERSON><PERSON>i_towa__1st_costume___hololive_33.webp"}, {"prompt": "go-toubun_no_hanayome, nakano ichika, go-toubun no hanayome", "chinese_prompt": "中野一花 (五等分的新娘)", "image_path": "assets/output_7_go-toubun_no_hanayome__nakano_ichika__go-toubun_no_hanayome_34.webp"}, {"prompt": "mega_man_(series), roll (mega man), mega man (series)", "chinese_prompt": "萝露 (洛克人)", "image_path": "assets/output_7_mega_man__series___roll__mega_man___mega_man__series__35.webp"}, {"prompt": "idolmaster, mi<PERSON> kanako, idolmaster", "chinese_prompt": "三村加奈子 (灰姑娘) (偶像大师)", "image_path": "assets/output_7_idolmaster__mi<PERSON>_kana<PERSON>__idolmaster_36.webp"}, {"prompt": "one-punch_man, fub<PERSON> (one-punch man), one-punch man", "chinese_prompt": "吹雪 (一拳超人)", "image_path": "assets/output_7_one-punch_man__fubuki__one-punch_man___one-punch_man_37.webp"}, {"prompt": "mario_(series), princess king boo, mario (series)", "chinese_prompt": "幽灵公主 (超级玛利欧)", "image_path": "assets/output_7_mario__series___princess_king_boo__mario__series__38.webp"}, {"prompt": "girls'_frontline, an-94 (girls' frontline), girls' frontline", "chinese_prompt": "AN-94, (少女前线)", "image_path": "assets/output_7_girls__frontline__an-94__girls__frontline___girls__frontline_39.webp"}, {"prompt": "devil_may_cry_(series), dante (devil may cry), devil may cry (series)", "chinese_prompt": "但丁 (恶魔猎人)", "image_path": "assets/output_7_devil_may_cry__series___dante__devil_may_cry___devil_may_cry__series__40.webp"}, {"prompt": "doki_doki_literature_club, monika (doki doki literature club), doki doki literature club", "chinese_prompt": "莫妮卡 (心跳文学部)", "image_path": "assets/output_7_doki_doki_literature_club__monika__doki_doki_literature_club___doki_doki_literature_club_41.webp"}, {"prompt": "pokemon, irida (pokemon), pokemon", "chinese_prompt": "珠贝 (宝可梦)", "image_path": "assets/output_7_pokemon__irida__pokemon___pokemon_42.webp"}, {"prompt": "re:zero_kara_hajimeru_iseka<PERSON>_seikatsu, natsuki subaru, re:zero kara hajimeru isekai seikatsu", "chinese_prompt": "菜月昴 (拉姆) (Re:从零开始的异世界生活)", "image_path": "assets/output_7_re_zero_kara_hajimeru_isekai_seikatsu__natsuki_subaru__re_zero_kara_hajimeru_isekai_seikatsu_43.webp"}, {"prompt": "pokemon, kris (pokemon), pokemon", "chinese_prompt": "克丽丝 (宝可梦)", "image_path": "assets/output_7_pokemon__kris__pokemon___pokemon_44.webp"}, {"prompt": "fate_(series), saber lily, fate (series)", "chinese_prompt": "<PERSON>ber (Fate)", "image_path": "assets/output_7_fate__series___saber_lily__fate__series__45.webp"}, {"prompt": "kemono_friends, northern white-faced owl (kemono friends), kemono friends", "chinese_prompt": "白脸角鸮 (动物朋友)", "image_path": "assets/output_7_kemono_friends__northern_white-faced_owl__kemono_friends___kemono_friends_46.webp"}, {"prompt": "azur_lane, z23 (azur lane), azur lane", "chinese_prompt": "Z23 (碧蓝航线)", "image_path": "assets/output_7_azur_lane__z23__azur_lane___azur_lane_47.webp"}, {"prompt": "one_piece, yamato (one piece), one piece", "chinese_prompt": "大和 (海贼王)", "image_path": "assets/output_7_one_piece__yamato__one_piece___one_piece_48.webp"}, {"prompt": "tsugu_(vtuber), hat<PERSON>a tsugu, tsugu (vtuber)", "chinese_prompt": "鸠羽つぐ (虚拟主播)", "image_path": "assets/output_7_tsugu__vtuber___hatoba_tsugu__tsugu__vtuber__49.webp"}, {"prompt": "azur_lane, le malin (azur lane), azur lane", "chinese_prompt": "恶毒 (碧蓝航线)", "image_path": "assets/output_7_azur_lane__le_malin__azur_lane___azur_lane_50.webp"}, {"prompt": "project_sekai, a<PERSON><PERSON> mizuki, project sekai", "chinese_prompt": "Project Sekai, 秋山瑞季, Project Sekai", "image_path": "assets/output_7_project_se<PERSON>__a<PERSON><PERSON>_mi<PERSON>__project_sekai_51.webp"}, {"prompt": "blue_archive, hibiki (cheer squad) (blue archive), blue archive", "chinese_prompt": "猫冢响 (啦啦队队员) (蔚蓝档案)", "image_path": "assets/output_7_blue_archive__hibiki__cheer_squad___blue_archive___blue_archive_52.webp"}, {"prompt": "touhou, niwatari kutaka, touhou", "chinese_prompt": "庭渡久侘歌 (东方)", "image_path": "assets/output_7_touhou__niwat<PERSON>_kutaka__touhou_53.webp"}, {"prompt": "hololive, to<PERSON><PERSON>i towa (jirai kei), hololive", "chinese_prompt": "常暗永远 (地雷系) (Hololive)", "image_path": "assets/output_7_hololive__to<PERSON><PERSON>i_towa__jirai_kei___hololive_54.webp"}, {"prompt": "bleach, kuro<PERSON> ichigo, bleach", "chinese_prompt": "黑崎一护 (死神)", "image_path": "assets/output_7_bleach__<PERSON><PERSON><PERSON>_i<PERSON><PERSON>__bleach_55.webp"}, {"prompt": "kizuna_ai_inc., kizuna ai, kizuna ai inc.", "chinese_prompt": "绊爱 (绊爱)", "image_path": "assets/output_7_kizuna_ai_inc___kizuna_ai__kizuna_ai_inc__56.webp"}, {"prompt": "hololive, bibi (to<PERSON><PERSON>i towa), hololive", "chinese_prompt": "bi<PERSON> (常暗永远) (Hololive)", "image_path": "assets/output_7_hololive__bibi__to<PERSON><PERSON>i_towa___hololive_57.webp"}, {"prompt": "little_red_riding_hood, little red riding hood (grimm), little red riding hood", "chinese_prompt": "小红帽 (格林童话)", "image_path": "assets/output_7_little_red_riding_hood__little_red_riding_hood__grimm___little_red_riding_hood_58.webp"}, {"prompt": "kantai_collection, isuzu (kancolle), kantai collection", "chinese_prompt": "五十铃 (舰队收藏)", "image_path": "assets/output_7_kantai_collection__isuzu__kancolle___kantai_collection_59.webp"}, {"prompt": "omori, kel (omori), omori", "chinese_prompt": "KEL (Omori)", "image_path": "assets/output_7_omori__kel__omori___omori_60.webp"}, {"prompt": "fire_emblem, dimitri alexandre blaiddyd, fire emblem", "chinese_prompt": "帝弥托利·亚历山大·布雷达德 (风花雪月) (圣火降魔录)", "image_path": "assets/output_7_fire_emblem__dimitri_alexandre_blaiddyd__fire_emblem_61.webp"}, {"prompt": "jojo_no_kimyou_na_bouken, k<PERSON><PERSON> rohan, jojo no kimyou na bouken", "chinese_prompt": "岸边露伴 (JOJO的奇妙冒险)", "image_path": "assets/output_7_jojo_no_kimyou_na_bouken__kishibe_rohan__jojo_no_kimyou_na_bouken_62.webp"}, {"prompt": "fate_(series), martha (fate), fate (series)", "chinese_prompt": "玛尔达( (Fate)", "image_path": "assets/output_7_fate__series___martha__fate___fate__series__63.webp"}, {"prompt": "azur_lane, honolulu (azur lane), azur lane", "chinese_prompt": "火奴鲁鲁 (碧蓝航线)", "image_path": "assets/output_7_azur_lane__honolulu__azur_lane___azur_lane_64.webp"}, {"prompt": "o<PERSON><PERSON><PERSON>-san, mat<PERSON><PERSON> ch<PERSON>, o<PERSON><PERSON><PERSON>-san", "chinese_prompt": "松野轻松 (阿松)", "image_path": "assets/output_7_osomatsu-san__matsuno_choromatsu__osomatsu-san_65.webp"}, {"prompt": "pokemon, kirlia, pokemon", "chinese_prompt": "奇鲁莉安 (宝可梦)", "image_path": "assets/output_7_pokemon__kirlia__pokemon_66.webp"}, {"prompt": "sword_art_online, leafa, sword art online", "chinese_prompt": "莉法 (刀剑神域)", "image_path": "assets/output_7_sword_art_online__leafa__sword_art_online_67.webp"}, {"prompt": "precure, minamino kanade, precure", "chinese_prompt": "南野奏 (光之美少女)", "image_path": "assets/output_7_precure__minamino_kanade__precure_68.webp"}, {"prompt": "ijiranaide_nagatoro-san, nagatoro hayase, ijiranaide nagatoro-san", "chinese_prompt": "长瀞早濑 (不要欺负我，长瀞同学)", "image_path": "assets/output_7_ijiranaide_nagatoro-san__nagatoro_hayase__ijiranaide_nagatoro-san_69.webp"}, {"prompt": "pokemon, james (pokemon), pokemon", "chinese_prompt": "小次郎 (宝可梦)", "image_path": "assets/output_7_pokemon__james__pokemon___pokemon_70.webp"}, {"prompt": "guilty_gear, elphelt valentine, guilty gear", "chinese_prompt": "艾尔菲尔特＝瓦伦蒂 (圣骑士之战)", "image_path": "assets/output_7_guilty_gear__elphelt_valentine__guilty_gear_71.webp"}, {"prompt": "blue_archive, haruka (blue archive), blue archive", "chinese_prompt": "伊草遥香 (蔚蓝档案)", "image_path": "assets/output_7_blue_archive__haruka__blue_archive___blue_archive_72.webp"}, {"prompt": "overwatch, widowmaker (overwatch), overwatch", "chinese_prompt": "夺命女 (斗阵特攻)", "image_path": "assets/output_7_overwatch__widowmaker__overwatch___overwatch_73.webp"}, {"prompt": "kantai_collection, libeccio (kancolle), kantai collection", "chinese_prompt": "西南风 (舰队收藏)", "image_path": "assets/output_7_kantai_collection__libe<PERSON><PERSON>__kancolle___kantai_collection_74.webp"}, {"prompt": "honkai_(series), elysia (miss pink elf) (honkai impact), honkai (series)", "chinese_prompt": "爱莉希雅 (粉色妖精) (崩坏)", "image_path": "assets/output_7_honkai__series___elysia__miss_pink_elf___honkai_impact___honkai__series__75.webp"}, {"prompt": "kantai_collection, jervis (kancolle), kantai collection", "chinese_prompt": "杰维斯 (舰队收藏)", "image_path": "assets/output_7_kantai_collection__jervis__kancolle___kantai_collection_76.webp"}, {"prompt": "kantai_collection, tashkent (kancolle), kantai collection", "chinese_prompt": "塔什干 (舰队收藏)", "image_path": "assets/output_7_kantai_collection__tashkent__kancolle___kantai_collection_77.webp"}, {"prompt": "indie_virtual_youtuber, shigure ui (vtuber), indie virtual youtuber", "chinese_prompt": "时雨羽衣 (<PERSON><PERSON>) (虚拟主播)", "image_path": "assets/output_7_indie_virtual_youtuber__shigure_ui__vtuber___indie_virtual_youtuber_78.webp"}, {"prompt": "touh<PERSON>, ha<PERSON><PERSON><PERSON><PERSON> keiki, touhou", "chinese_prompt": "埴安神袿姬 (东方)", "image_path": "assets/output_7_touh<PERSON>__ha<PERSON><PERSON><PERSON><PERSON>_kei<PERSON>__touhou_79.webp"}, {"prompt": "azur_lane, bremerton (scorching-hot training) (azur lane), azur lane", "chinese_prompt": "布雷默顿 (热训) (碧蓝航线)", "image_path": "assets/output_7_azur_lane__bremerton__scorching-hot_training___azur_lane___azur_lane_80.webp"}, {"prompt": "apex_legends, wraith (apex legends), apex legends", "chinese_prompt": "恶灵 (Apex Legends)", "image_path": "assets/output_7_apex_legends__wraith__apex_legends___apex_legends_81.webp"}, {"prompt": "precure, sora harewataru, precure", "chinese_prompt": "索拉·哈雷瓦塔尔 (光之美少女)", "image_path": "assets/output_7_precure__sora_harewataru__precure_82.webp"}, {"prompt": "pokemon, jigglypuff, pokemon", "chinese_prompt": "胖丁 (宝可梦)", "image_path": "assets/output_7_pokemon__jigglypuff__pokemon_83.webp"}, {"prompt": "hololive, minato aqua (sailor), hololive", "chinese_prompt": "凑阿库亚 (水手服), (Hololive)", "image_path": "assets/output_7_hololive__minato_aqua__sailor___hololive_84.webp"}, {"prompt": "pokemon, carmine (pokemon), pokemon", "chinese_prompt": "丹瑜 (宝可梦)", "image_path": "assets/output_7_pokemon__carmine__pokemon___pokemon_85.webp"}, {"prompt": "girls'_frontline, m4a1 (girls' frontline), girls' frontline", "chinese_prompt": "M4A1 (少女前线)", "image_path": "assets/output_7_girls__frontline__m4a1__girls__frontline___girls__frontline_86.webp"}, {"prompt": "fate_(series), matou kariya, fate (series)", "chinese_prompt": "间桐雁夜 (Fate)", "image_path": "assets/output_7_fate__series___matou_kariya__fate__series__87.webp"}, {"prompt": "project_moon, yi sang (project moon), project moon", "chinese_prompt": "李箱 (Project Moon)", "image_path": "assets/output_7_project_moon__yi_sang__project_moon___project_moon_88.webp"}, {"prompt": "mega_man_(series), zero (mega man), mega man (series)", "chinese_prompt": "Zero (洛克人)", "image_path": "assets/output_7_mega_man__series___zero__mega_man___mega_man__series__89.webp"}, {"prompt": "umamusume, air groove (umamusume), umamusume", "chinese_prompt": "气槽 (赛马娘)", "image_path": "assets/output_7_umamusume__air_groove__umamusume___umamusume_90.webp"}, {"prompt": "gintama, kagura (gintama), gintama", "chinese_prompt": "神乐 (银魂)", "image_path": "assets/output_7_gintama__kagura__gintama___gintama_91.webp"}, {"prompt": "onii-chan_wa_oshimai!, ho<PERSON> momiji, onii-chan wa oshimai!", "chinese_prompt": "穗月红叶 (不当哥哥了)", "image_path": "assets/output_7_onii-chan_wa_o<PERSON>i___ho<PERSON>_momiji__onii-chan_wa_oshimai__92.webp"}, {"prompt": "neptune_(series), purple heart (neptunia), neptune (series)", "chinese_prompt": "妮普禔努 紫灵心 (战机少女)", "image_path": "assets/output_7_neptune__series___purple_heart__neptunia___neptune__series__93.webp"}, {"prompt": "to_love-ru, konjiki no yami, to love-ru", "chinese_prompt": "金色暗影 (出包王女)", "image_path": "assets/output_7_to_love-ru__konjiki_no_yami__to_love-ru_94.webp"}, {"prompt": "zenless_zone_zero, zhu yuan, zenless zone zero", "chinese_prompt": "朱鸢 (绝区零)", "image_path": "assets/output_7_zenless_zone_zero__zhu_yuan__zenless_zone_zero_95.webp"}, {"prompt": "cardcaptor_sakura, da<PERSON><PERSON><PERSON> to<PERSON>, cardcaptor sakura", "chinese_prompt": "道寺知世 (库洛魔法使)", "image_path": "assets/output_7_cardcaptor_sakura__da<PERSON><PERSON><PERSON>_tomoyo__cardcaptor_sakura_96.webp"}, {"prompt": "umine<PERSON>_no_naku_koro_ni, <PERSON><PERSON><PERSON> ange, umineko no naku koro ni", "chinese_prompt": "右代宫 缘寿 (海猫鸣泣之时)", "image_path": "assets/output_7_um<PERSON><PERSON>_no_naku_koro_ni__<PERSON><PERSON><PERSON>_ange__um<PERSON><PERSON>_no_naku_koro_ni_97.webp"}, {"prompt": "yahari_ore_no_seishun_lovecome_wa_machigatteiru., y<PERSON><PERSON><PERSON> yuki<PERSON>, yahari ore no seishun lovecome wa machigatteiru.", "chinese_prompt": "雪之下雪乃 (果然我的青春恋爱喜剧搞错了)", "image_path": "assets/output_7_yahari_ore_no_seishun_lovecome_wa_machigatteiru___yukinoshita_yukino__yahari_ore_no_seishun_lovecome_wa_machigatteiru__98.webp"}, {"prompt": "genshin_impact, xingqiu (genshin impact), genshin impact", "chinese_prompt": "行秋 (原神)", "image_path": "assets/output_7_genshin_impact__xingqiu__genshin_impact___genshin_impact_99.webp"}, {"prompt": "kantai_collection, mamiya (kancolle), kantai collection", "chinese_prompt": "间宫 (舰队收藏)", "image_path": "assets/output_7_kantai_collection__mamiya__kancolle___kantai_collection_100.webp"}, {"prompt": "fate_(series), art<PERSON> pendragon (alter swimsuit rider) (fate), fate (series)", "chinese_prompt": "阿尔托莉雅·潘德拉贡 (泳装骑), (Fate)", "image_path": "assets/output_7_fate__series___artoria_pendragon__alter_swimsuit_rider___fate___fate__series__101.webp"}, {"prompt": "honkai_(series), dr. ratio (honkai: star rail), honkai (series)", "chinese_prompt": "真理医生 (崩坏: 星穹铁道) (崩坏)", "image_path": "assets/output_7_honkai__series___dr__ratio__honkai__star_rail___honkai__series__102.webp"}, {"prompt": "pokemon, bianca (pokemon), pokemon", "chinese_prompt": "卡侬 (宝可梦)", "image_path": "assets/output_7_pokemon__bianca__pokemon___pokemon_103.webp"}, {"prompt": "kimetsu_no_ya<PERSON>, ka<PERSON><PERSON>, kimetsu no yaiba", "chinese_prompt": "灶门炭治郎 (鬼灭之刃)", "image_path": "assets/output_7_kimetsu_no_yaiba__kamado_tanji<PERSON>__kimetsu_no_yaiba_104.webp"}, {"prompt": "pokemon, espeon, pokemon", "chinese_prompt": "太阳伊布 (宝可梦)", "image_path": "assets/output_7_pokemon__espeon__pokemon_105.webp"}, {"prompt": "neptune_(series), noire (neptunia), neptune (series)", "chinese_prompt": "诺娃 黑灵心 (战机少女)", "image_path": "assets/output_7_neptune__series___noire__neptunia___neptune__series__106.webp"}, {"prompt": "jujutsu_kaisen, ryo<PERSON>n sukuna (jujutsu kaisen), jujutsu kaisen", "chinese_prompt": "两面宿傩 (咒术回战)", "image_path": "assets/output_7_jujutsu_kaisen__ryoumen_sukuna__jujutsu_kaisen___jujutsu_kaisen_107.webp"}, {"prompt": "umamusume, narita taishin (umamusume), umamusume", "chinese_prompt": "成田大进 (赛马娘)", "image_path": "assets/output_7_umamusume__narita_taishin__umamusume___umamusume_108.webp"}, {"prompt": "pokemon, florian (pokemon), pokemon", "chinese_prompt": "小春 (宝可梦)", "image_path": "assets/output_7_pokemon__florian__pokemon___pokemon_109.webp"}, {"prompt": "azur_lane, baltimore (azur lane), azur lane", "chinese_prompt": "巴尔的摩 (碧蓝航线)", "image_path": "assets/output_7_azur_lane__baltimore__azur_lane___azur_lane_110.webp"}, {"prompt": "kantai_collection, rensouhou-kun, kantai collection", "chinese_prompt": "连装砲君 (舰队收藏)", "image_path": "assets/output_7_kantai_collection__rensouh<PERSON>-kun__kantai_collection_111.webp"}, {"prompt": "kantai_collection, furutaka (kancolle), kantai collection", "chinese_prompt": "古鹰 (舰队收藏)", "image_path": "assets/output_7_kantai_collection__furutaka__kancolle___kantai_collection_112.webp"}, {"prompt": "air_(visual_novel), kamio misuzu, air (visual novel)", "chinese_prompt": "神尾观铃 (AIR)", "image_path": "assets/output_7_air__visual_novel___kamio_mi<PERSON>zu__air__visual_novel__113.webp"}, {"prompt": "fate/grand_order, scathach skadi (fate), fate/grand order", "chinese_prompt": "斯卡哈·斯卡蒂 (Fate/Grand Order)", "image_path": "assets/output_7_fate_grand_order__scathach_skadi__fate___fate_grand_order_114.webp"}, {"prompt": "hololive, u<PERSON><PERSON> (1st costume), hololive", "chinese_prompt": "润羽露西娅 (1st服) (Hololive)", "image_path": "assets/output_7_hololive__uruha_rushia__1st_costume___hololive_115.webp"}, {"prompt": "little_busters!, natsume rin, little busters!", "chinese_prompt": "枣铃 (校园克星)", "image_path": "assets/output_7_little_busters___natsume_rin__little_busters__116.webp"}, {"prompt": "fire_emblem, ike (fire emblem), fire emblem", "chinese_prompt": "艾克 (苍炎之轨迹) (圣火降魔录)", "image_path": "assets/output_7_fire_emblem__ike__fire_emblem___fire_emblem_117.webp"}, {"prompt": "idolmaster, k<PERSON><PERSON> kaho, idolmaster", "chinese_prompt": "小宫果穗 (闪耀色彩) (偶像大师)", "image_path": "assets/output_7_idolmaster__komiya_ka<PERSON>__idolmaster_118.webp"}, {"prompt": "fate_(series), yang gui<PERSON> (fate), fate (series)", "chinese_prompt": "杨贵妃 (Fate)", "image_path": "assets/output_7_fate__series___yang_guifei__fate___fate__series__119.webp"}, {"prompt": "mario_(series), bowser, mario (series)", "chinese_prompt": "库巴 (超级玛利欧)", "image_path": "assets/output_7_mario__series___bowser__mario__series__120.webp"}, {"prompt": "umamusume, daitaku helio<PERSON> (umamusume), umamusume", "chinese_prompt": "大拓太阳神 (赛马娘)", "image_path": "assets/output_7_umamusume__daitaku_helios__umamusume___umamusume_121.webp"}, {"prompt": "kantai_collection, i-class destroyer, kantai collection", "chinese_prompt": "I级驱逐舰 (舰队收藏)", "image_path": "assets/output_7_kantai_collection__i-class_destroyer__kantai_collection_122.webp"}, {"prompt": "project_moon, faust (project moon), project moon", "chinese_prompt": "浮士德 (Project Moon)", "image_path": "assets/output_7_project_moon__faust__project_moon___project_moon_123.webp"}, {"prompt": "girls'_frontline, g11 (girls' frontline), girls' frontline", "chinese_prompt": "G11 (少女前线)", "image_path": "assets/output_7_girls__frontline__g11__girls__frontline___girls__frontline_124.webp"}, {"prompt": "pokemon, sprigatito, pokemon", "chinese_prompt": "新叶喵 (宝可梦)", "image_path": "assets/output_7_pokemon__sprigatito__pokemon_125.webp"}, {"prompt": "girls_und_panzer, nishi kinuyo, girls und panzer", "chinese_prompt": "西绢代 (少女与战车)", "image_path": "assets/output_7_girls_und_panzer__nishi_kinuyo__girls_und_panzer_126.webp"}, {"prompt": "higurashi_no_naku_koro_ni, son<PERSON><PERSON> shi<PERSON>, higurashi no naku koro ni", "chinese_prompt": "园崎诗音 (暮蝉悲鸣时)", "image_path": "assets/output_7_higurashi_no_naku_koro_ni__son<PERSON><PERSON>_shion__higurashi_no_naku_koro_ni_127.webp"}, {"prompt": "kill_me_baby, sonya (kill me baby), kill me baby", "chinese_prompt": "索妮娅 (爱杀宝贝)", "image_path": "assets/output_7_kill_me_baby__sonya__kill_me_baby___kill_me_baby_128.webp"}, {"prompt": "idolmaster, son<PERSON> ch<PERSON><PERSON><PERSON>, idolmaster", "chinese_prompt": "园田智代子 (闪耀色彩) (偶像大师)", "image_path": "assets/output_7_idolmaster__son<PERSON>_chi<PERSON><PERSON>__idolmaster_129.webp"}, {"prompt": "idolmaster, <PERSON><PERSON><PERSON> hay<PERSON>, idolmaster", "chinese_prompt": "久川飒 (灰姑娘) (偶像大师", "image_path": "assets/output_7_idolmaster__his<PERSON><PERSON>_hayate__idolmaster_130.webp"}, {"prompt": "pokemon, lopunny, pokemon", "chinese_prompt": "长耳兔 (宝可梦)", "image_path": "assets/output_7_pokemon__lopunny__pokemon_131.webp"}, {"prompt": "genshin_impact, kamisato ayato, genshin impact", "chinese_prompt": "神里绫人 (原神)", "image_path": "assets/output_7_genshin_impact__kamisato_ayato__genshin_impact_132.webp"}, {"prompt": "umamusume, cheval grand (umamusume), umamusume", "chinese_prompt": "高尚骏逸 (赛马娘)", "image_path": "assets/output_7_umamusume__cheval_grand__umamusume___umamusume_133.webp"}, {"prompt": "frozen_(disney), elsa (frozen), frozen (disney)", "chinese_prompt": "艾莎 (冰雪奇缘)", "image_path": "assets/output_7_frozen__disney___elsa__frozen___frozen__disney__134.webp"}, {"prompt": "kemono_friends, <PERSON><PERSON> red fox (kemono friends), kemono friends", "chinese_prompt": "北狐 (动物朋友)", "image_path": "assets/output_7_kemono_friends__ezo_red_fox__kemono_friends___kemono_friends_135.webp"}, {"prompt": "senpai_ga_uzai_kouhai_no_hanashi, i<PERSON><PERSON> futa<PERSON> (shiromanta), senpai ga uzai kouhai no hanashi", "chinese_prompt": "五十岚双叶 (前辈有够烦)", "image_path": "assets/output_7_senpai_ga_uzai_kouhai_no_hanashi__i<PERSON><PERSON>_futaba__shiromanta___senpai_ga_uzai_kouhai_no_hanashi_136.webp"}, {"prompt": "bang_dream!, togawa sakiko, bang dream!", "chinese_prompt": "豊川祥子 (Bang Dream!)", "image_path": "assets/output_7_bang_dream___togawa_sakiko__bang_dream__137.webp"}, {"prompt": "precure, yuki<PERSON><PERSON> hono<PERSON>, precure", "chinese_prompt": "雪城穗乃香 (光之美少女)", "image_path": "assets/output_7_precure__yuki<PERSON><PERSON>_honoka__precure_138.webp"}, {"prompt": "pokemon, mega gardevoir, pokemon", "chinese_prompt": "Mega 沙奈朵 (宝可梦)", "image_path": "assets/output_7_pokemon__mega_gardevoir__pokemon_139.webp"}, {"prompt": "pokemon, piers (pokemon), pokemon", "chinese_prompt": "聂梓 (宝可梦)", "image_path": "assets/output_7_pokemon__piers__pokemon___pokemon_140.webp"}, {"prompt": "genshin_impact, chongyun (genshin impact), genshin impact", "chinese_prompt": "重云 (原神)", "image_path": "assets/output_7_genshin_impact__chongyun__genshin_impact___genshin_impact_141.webp"}, {"prompt": "bayonetta_(series), bayonetta, bayonetta (series)", "chinese_prompt": "bayonetta (魔兵惊天录)", "image_path": "assets/output_7_bayonetta__series___bayonetta__bayonetta__series__142.webp"}, {"prompt": "touh<PERSON>, sendai hakurei no miko, touhou", "chinese_prompt": "先代博丽巫女 (东方)", "image_path": "assets/output_7_touhou__sendai_hakurei_no_miko__touhou_143.webp"}, {"prompt": "omori, mari (omori), omori", "chinese_prompt": "MARI (Omori)", "image_path": "assets/output_7_omori__mari__omori___omori_144.webp"}, {"prompt": "girls'_frontline, m16a1 (girls' frontline), girls' frontline", "chinese_prompt": "M16A1 (少女前线)", "image_path": "assets/output_7_girls__frontline__m16a1__girls__frontline___girls__frontline_145.webp"}, {"prompt": "umamusume, t.m. opera o (umamusume), umamusume", "chinese_prompt": "好歌剧 (赛马娘)", "image_path": "assets/output_7_umamusume__t_m__opera_o__umamusume___umamusume_146.webp"}, {"prompt": "project_sekai, tenma tsukasa, project sekai", "chinese_prompt": "Project Sekai, 天马司, Project Sekai", "image_path": "assets/output_7_project_sekai__tenma_tsu<PERSON>a__project_sekai_147.webp"}, {"prompt": "sonic_(series), blaze the cat, sonic (series)", "chinese_prompt": "布蕾兹 (音速小子)", "image_path": "assets/output_7_sonic__series___blaze_the_cat__sonic__series__148.webp"}, {"prompt": "o<PERSON><PERSON><PERSON>-san, mat<PERSON><PERSON> to<PERSON>, o<PERSON><PERSON><PERSON>-san", "chinese_prompt": "松野椴松 (阿松)", "image_path": "assets/output_7_osomatsu-san__matsuno_todomatsu__osomatsu-san_149.webp"}, {"prompt": "kemono_friends, grey wolf (kemono friends), kemono friends", "chinese_prompt": "灰狼 (动物朋友)", "image_path": "assets/output_7_kemono_friends__grey_wolf__kemono_friends___kemono_friends_150.webp"}, {"prompt": "love_live!, shi<PERSON>ya kanon, love live!", "chinese_prompt": "涩谷香音 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_7_love_live___shibuya_kanon__love_live__151.webp"}, {"prompt": "kantai_collection, kawakaze (kancolle), kantai collection", "chinese_prompt": "江风 (舰队收藏)", "image_path": "assets/output_7_kantai_collection__kawakaze__kancolle___kantai_collection_152.webp"}, {"prompt": "voicevox, zundamon, voicevox", "chinese_prompt": "俊达萌 (ずんだもん) (VOICEVOX)", "image_path": "assets/output_7_voicevox__zundamon__voicevox_153.webp"}, {"prompt": "bang_dream!, hikawa hina, bang dream!", "chinese_prompt": "冰川日菜 (BanG Dream!)", "image_path": "assets/output_7_bang_dream___hikawa_hina__bang_dream__154.webp"}, {"prompt": "blue_archive, kikyou (blue archive), blue archive", "chinese_prompt": "桐生桔梗 (蔚蓝档案)", "image_path": "assets/output_7_blue_archive__kikyou__blue_archive___blue_archive_155.webp"}, {"prompt": "clannad, furukawa nagisa, clannad", "chinese_prompt": "古河渚 (Clannad)", "image_path": "assets/output_7_clannad__furukawa_nagisa__clannad_156.webp"}, {"prompt": "fate_(series), abigai<PERSON> will<PERSON> (swimsuit foreigner) (fate), fate (series)", "chinese_prompt": "艾比盖儿·威廉斯 (泳装), (Fate)", "image_path": "assets/output_7_fate__series___abigail_williams__swimsuit_foreigner___fate___fate__series__157.webp"}, {"prompt": "boku_no_hero_academia, hawks (boku no hero academia), boku no hero academia", "chinese_prompt": "霍克斯 (我的英雄学院)", "image_path": "assets/output_7_boku_no_hero_academia__hawks__boku_no_hero_academia___boku_no_hero_academia_158.webp"}, {"prompt": "kill_la_kill, gamagoori ira, kill la kill", "chinese_prompt": "蟇郡苛 (斩服少女)", "image_path": "assets/output_7_kill_la_kill__gamago<PERSON>_ira__kill_la_kill_159.webp"}, {"prompt": "blue_archive, hina (swimsuit) (blue archive), blue archive", "chinese_prompt": "空崎阳奈 (泳装) (蔚蓝档案)", "image_path": "assets/output_7_blue_archive__hina__swimsuit___blue_archive___blue_archive_160.webp"}, {"prompt": "kantai_collection, isonami (kancolle), kantai collection", "chinese_prompt": "矶波 (舰队收藏)", "image_path": "assets/output_7_kantai_collection__isonami__kancolle___kantai_collection_161.webp"}, {"prompt": "love_live!, nakasu kasumi, love live!", "chinese_prompt": "中须霞 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_7_love_live___nakasu_kasumi__love_live__162.webp"}, {"prompt": "love_live!, kachi<PERSON><PERSON> kosuzu, love live!", "chinese_prompt": "徒町小铃 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_7_love_live___kachi<PERSON><PERSON>_kosuzu__love_live__163.webp"}, {"prompt": "vocaloid, kamui gakupo, vocaloid", "chinese_prompt": "神威乐步 (Vocaloid)", "image_path": "assets/output_7_vocaloid__kamui_gakupo__vocaloid_164.webp"}, {"prompt": "arknights, schwarz (arknights), arknights", "chinese_prompt": "黑 (明日方舟)", "image_path": "assets/output_7_arknights__schwarz__arknights___arknights_165.webp"}, {"prompt": "vocaloid, hatsune miku (append), vocaloid", "chinese_prompt": "初音未来 (append) (Vocaloid)", "image_path": "assets/output_7_vocaloid__hatsune_miku__append___vocaloid_166.webp"}, {"prompt": "final_fantasy, squall leonhart, final fantasy", "chinese_prompt": "史克尔·里昂哈特 (ff8) (最终幻想)", "image_path": "assets/output_7_final_fantasy__squall_leonhart__final_fantasy_167.webp"}, {"prompt": "hyouka, oreki ho<PERSON>, hyouka", "chinese_prompt": "折木奉太郎 (冰菓)", "image_path": "assets/output_7_hyouka__ore<PERSON>_ho<PERSON><PERSON>__hyouka_168.webp"}, {"prompt": "xenoblade_chronicles_(series), rex (xenoblade), xenoblade chronicles (series)", "chinese_prompt": "雷克斯 (异度神剑)", "image_path": "assets/output_7_xenoblade_chronicles__series___rex__xenoblade___xenoblade_chronicles__series__169.webp"}, {"prompt": "dragon_ball, android 21, dragon ball", "chinese_prompt": "人造人21号 (七龙珠)", "image_path": "assets/output_7_dragon_ball__android_21__dragon_ball_170.webp"}, {"prompt": "clannad, fuji<PERSON> kyou, clannad", "chinese_prompt": "藤林杏 (Clannad)", "image_path": "assets/output_7_clannad__fu<PERSON><PERSON>_kyou__clannad_171.webp"}, {"prompt": "goddess_of_victory:_nikke, anis (nikke), goddess of victory: nikke", "chinese_prompt": "阿妮斯 (胜利女神：妮姬)", "image_path": "assets/output_7_goddess_of_victory__nikke__anis__nikke___goddess_of_victory__nikke_172.webp"}, {"prompt": "yuru_yuri, fun<PERSON> yui, yuru yuri", "chinese_prompt": "船见结衣 (悠哉日常大王)", "image_path": "assets/output_7_yuru_yuri__funami_yui__yuru_yuri_173.webp"}, {"prompt": "hololive, koseki bijou, hololive", "chinese_prompt": "古石碧珠 (Hololive)", "image_path": "assets/output_7_hololive__koseki_bijou__hololive_174.webp"}, {"prompt": "pokemon, snivy, pokemon", "chinese_prompt": "藤藤蛇 (宝可梦)", "image_path": "assets/output_7_pokemon__snivy__pokemon_175.webp"}, {"prompt": "undertale, frisk (undertale), undertale", "chinese_prompt": "Frisk (undertale)", "image_path": "assets/output_7_undertale__frisk__undertale___undertale_176.webp"}, {"prompt": "pokemon, nemona (pokemon), pokemon", "chinese_prompt": "妮莫 (宝可梦)", "image_path": "assets/output_7_pokemon__nemona__pokemon___pokemon_177.webp"}, {"prompt": "kantai_collection, murakumo kai ni (kancolle), kantai collection", "chinese_prompt": "丛云改二 (舰队收藏)", "image_path": "assets/output_7_kantai_collection__muraku<PERSON>_kai_ni__kancolle___kantai_collection_178.webp"}, {"prompt": "link!_like!_love_live!, anyoji hime, link! like! love live!", "chinese_prompt": "安养寺姬芽 (Link! Like! Love Live!)", "image_path": "assets/output_7_link__like__love_live___anyoji_hime__link__like__love_live__179.webp"}, {"prompt": "pokemon, elio (pokemon), pokemon", "chinese_prompt": "小阳 (宝可梦)", "image_path": "assets/output_7_pokemon__elio__pokemon___pokemon_180.webp"}, {"prompt": "toaru_majutsu_no_index, uiharu kazari, toaru majutsu no index", "chinese_prompt": "初春饰利 (魔法禁书目录)", "image_path": "assets/output_7_toaru_majutsu_no_index__uiharu_kazari__toaru_majutsu_no_index_181.webp"}, {"prompt": "k-on!, hi<PERSON>awa ui, k-on!", "chinese_prompt": "平泽忧 (K-ON! 轻音部)", "image_path": "assets/output_7_k-on___hi<PERSON><PERSON>_ui__k-on__182.webp"}, {"prompt": "touken_ranbu, mi<PERSON><PERSON> m<PERSON>, touken ranbu", "chinese_prompt": "三日月宗近 (刀剑乱舞)", "image_path": "assets/output_7_touken_ranbu__mi<PERSON><PERSON>_mune<PERSON><PERSON>__touken_ranbu_183.webp"}, {"prompt": "kantai_collection, katori (kancolle), kantai collection", "chinese_prompt": "香取 (舰队收藏)", "image_path": "assets/output_7_kantai_collection__katori__kancolle___kantai_collection_184.webp"}, {"prompt": "project_moon, sinclair (project moon), project moon", "chinese_prompt": "辛克雷亚 (Project Moon)", "image_path": "assets/output_7_project_moon__sinclair__project_moon___project_moon_185.webp"}, {"prompt": "shantae_(series), shanta<PERSON>, shantae (series)", "chinese_prompt": "桑塔 (桑塔)", "image_path": "assets/output_7_shantae__series___shantae__shantae__series__186.webp"}, {"prompt": "fire_emblem, robin (male) (fire emblem), fire emblem", "chinese_prompt": "罗宾 (男性) (外传) (圣火降魔录)", "image_path": "assets/output_7_fire_emblem__robin__male___fire_emblem___fire_emblem_187.webp"}, {"prompt": "gegege_no_kitarou, ne<PERSON><PERSON><PERSON>, gegege no kitarou", "chinese_prompt": "猫女 (鬼太郎)", "image_path": "assets/output_7_gegege_no_kitarou__nekomusume__gegege_no_kitarou_188.webp"}, {"prompt": "pokemon, squirtle, pokemon", "chinese_prompt": "杰尼龟 (宝可梦)", "image_path": "assets/output_7_pokemon__squirtle__pokemon_189.webp"}, {"prompt": "idolmaster, sakura<PERSON> mano, idolmaster", "chinese_prompt": "樱木真乃 (闪耀色彩) (偶像大师)", "image_path": "assets/output_7_idolmaster__sakura<PERSON>_mano__idolmaster_190.webp"}, {"prompt": "shingeki_no_kyojin, <PERSON><PERSON> (shingeki no kyojin), shingeki no kyojin", "chinese_prompt": "尤米尔 (进击的巨人)", "image_path": "assets/output_7_shingeki_no_kyojin__ymir__shingeki_no_kyojin___shingeki_no_kyojin_191.webp"}, {"prompt": "jojo_no_kimyou_na_bouken, jean pier<PERSON>, jojo no kimyou na bouken", "chinese_prompt": "约翰·皮耶尔·波鲁纳雷夫 (JOJO的奇妙冒险)", "image_path": "assets/output_7_jojo_no_kimyou_na_bouken__jean_pierre_polnareff__jojo_no_kimyou_na_bouken_192.webp"}, {"prompt": "umamusume, matika<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (umamusume), umamusume", "chinese_prompt": "待兼福来 (赛马娘)", "image_path": "assets/output_7_umamusume__matikanefukukitaru__umamusume___umamusume_193.webp"}, {"prompt": "project_sekai, ootori emu, project sekai", "chinese_prompt": "Project Sekai, 大鸟海, Project Sekai", "image_path": "assets/output_7_project_sekai__ootori_emu__project_sekai_194.webp"}, {"prompt": "boku_no_hero_academia, ashido mina, boku no hero academia", "chinese_prompt": "芦户三奈 (我的英雄学院)", "image_path": "assets/output_7_boku_no_hero_academia__ashido_mina__boku_no_hero_academia_195.webp"}, {"prompt": "touh<PERSON>, tsu<PERSON><PERSON> ben<PERSON>, touhou", "chinese_prompt": "九十九弁弁 (东方)", "image_path": "assets/output_7_touh<PERSON>__tsu<PERSON><PERSON>_ben<PERSON>__touhou_196.webp"}, {"prompt": "mushoku_tensei, roxy migurdia, mushoku tensei", "chinese_prompt": "洛琪希·米格路迪亚 (无职转生)", "image_path": "assets/output_7_mushoku_tensei__roxy_migurdia__mushoku_tensei_197.webp"}, {"prompt": "world_witches_series, francesca lucchini, world witches series", "chinese_prompt": "弗兰西斯卡·鲁奇尼 (强袭魔女)", "image_path": "assets/output_7_world_witches_series__francesca_luc<PERSON>i__world_witches_series_198.webp"}, {"prompt": "fate_(series), jeanne d'arc alter (ver. shinjuku 1999) (fate), fate (series)", "chinese_prompt": "黑贞德 （新宿1999版） (Fate)", "image_path": "assets/output_7_fate__series___jeanne_d_arc_alter__ver__shinjuku_1999___fate___fate__series__199.webp"}, {"prompt": "hibike!_euphonium, kousaka reina, hibike! euphonium", "chinese_prompt": "高坂丽奈 (吹响吧！上低音号)", "image_path": "assets/output_7_hibike__euphonium__kousaka_reina__hibike__euphonium_200.webp"}, {"prompt": "genshin_impact, jumpy dumpty, genshin impact", "chinese_prompt": "蹦蹦炸弹 (原神)", "image_path": "assets/output_7_genshin_impact__jumpy_dumpty__genshin_impact_201.webp"}, {"prompt": "arknights, mountain (arknights), arknights", "chinese_prompt": "山 (明日方舟)", "image_path": "assets/output_7_arknights__mountain__arknights___arknights_202.webp"}, {"prompt": "fate_(series), o<PERSON><PERSON><PERSON><PERSON><PERSON> (fate), fate (series)", "chinese_prompt": "刑部姬 (Fate)", "image_path": "assets/output_7_fate__series___o<PERSON><PERSON><PERSON><PERSON>e__fate___fate__series__203.webp"}, {"prompt": "final_fantasy, moogle, final fantasy", "chinese_prompt": "莫古里 (最终幻想)", "image_path": "assets/output_7_final_fantasy__moogle__final_fantasy_204.webp"}, {"prompt": "kagerou_project, tateyama ayano, kagerou project", "chinese_prompt": "楯山文乃 (阳炎计划)", "image_path": "assets/output_8_kagerou_project__tate<PERSON>_ayano__kagerou_project_0.webp"}, {"prompt": "neon_genesis_evangelion, katsu<PERSON><PERSON> misato, neon genesis evangelion", "chinese_prompt": "葛城美里 (新世纪福音战士)", "image_path": "assets/output_8_neon_genesis_evangelion__katsuragi_misato__neon_genesis_evangelion_1.webp"}, {"prompt": "touken_ranbu, ka<PERSON><PERSON> ki<PERSON><PERSON>, touken ranbu", "chinese_prompt": "加州清光 (刀剑乱舞)", "image_path": "assets/output_8_touken_ranbu__kashu<PERSON>_ki<PERSON><PERSON><PERSON>__touken_ranbu_2.webp"}, {"prompt": "girls'_frontline, m4 sopmod ii (girls' frontline), girls' frontline", "chinese_prompt": "M4 SOPMOD II, (少女前线)", "image_path": "assets/output_8_girls__frontline__m4_sopmod_ii__girls__frontline___girls__frontline_3.webp"}, {"prompt": "precure, cure peace, precure", "chinese_prompt": "黄濑弥生 Cure Peace (光之美少女)", "image_path": "assets/output_8_precure__cure_peace__precure_4.webp"}, {"prompt": "fate/grand_order, he<PERSON> b<PERSON> (fate), fate/grand order", "chinese_prompt": "海伦娜·布拉瓦茨基 (Fate/Grand Order)", "image_path": "assets/output_8_fate_grand_order__he<PERSON>_blavatsky__fate___fate_grand_order_5.webp"}, {"prompt": "fate_(series), jeanne d'arc (swimsuit archer) (fate), fate (series)", "chinese_prompt": "贞德 (泳装弓箭手), (Fate)", "image_path": "assets/output_8_fate__series___jeanne_d_arc__swimsuit_archer___fate___fate__series__6.webp"}, {"prompt": "fate_(series), mysterious heroine x alter (fate), fate (series)", "chinese_prompt": "谜之女主角X Alter (Fate)", "image_path": "assets/output_8_fate__series___mysterious_heroine_x_alter__fate___fate__series__7.webp"}, {"prompt": "saibou_shinkyoku, harada minoru, saibou shinkyoku", "chinese_prompt": "原田实 (细胞神曲)", "image_path": "assets/output_8_saibou_shinkyoku__harada_minoru__saibou_shinkyoku_8.webp"}, {"prompt": "idolmaster, his<PERSON><PERSON> nagi, idolmaster", "chinese_prompt": "久川凪 (灰姑娘) (偶像大师)", "image_path": "assets/output_8_idolmaster__his<PERSON><PERSON>_nagi__idolmaster_9.webp"}, {"prompt": "blue_archive, shun (small) (blue archive), blue archive", "chinese_prompt": "春原旬 (小) (蔚蓝档案)", "image_path": "assets/output_8_blue_archive__shun__small___blue_archive___blue_archive_10.webp"}, {"prompt": "jojo_no_kimyou_na_bouken, bruno b<PERSON>, jojo no kimyou na bouken", "chinese_prompt": "布鲁诺·布加拉提 (JOJO的奇妙冒险)", "image_path": "assets/output_8_jojo_no_kimyou_na_bouken__bruno_b<PERSON><PERSON><PERSON>__jojo_no_kimyou_na_bouken_11.webp"}, {"prompt": "honkai_(series), rita ross<PERSON>sse, honkai (series)", "chinese_prompt": "丽塔·洛丝薇瑟 (崩坏)", "image_path": "assets/output_8_honkai__series___rita_rossweisse__honkai__series__12.webp"}, {"prompt": "monogatari_(series), sengo<PERSON> nadeko, monogatari (series)", "chinese_prompt": "千石抚子 (化物语)", "image_path": "assets/output_8_monogatari__series___sengoku_nadeko__monogatari__series__13.webp"}, {"prompt": "arknights, eyjafjalla (arknights), arknights", "chinese_prompt": "艾雅法拉 (明日方舟)", "image_path": "assets/output_8_arknights__eyja<PERSON><PERSON><PERSON>__arknights___arknights_14.webp"}, {"prompt": "touh<PERSON>, <PERSON><PERSON><PERSON> u<PERSON> (bird), to<PERSON><PERSON>", "chinese_prompt": "灵乌路空 (鸟) (东方)", "image_path": "assets/output_8_touh<PERSON>__re<PERSON><PERSON>_u<PERSON><PERSON>__bird___touhou_15.webp"}, {"prompt": "blue_archive, himari (blue archive), blue archive", "chinese_prompt": "明星日鞠 (蔚蓝档案)", "image_path": "assets/output_8_blue_archive__himari__blue_archive___blue_archive_16.webp"}, {"prompt": "fate_(series), prisma illya, fate (series)", "chinese_prompt": "伊莉雅 (Fate)", "image_path": "assets/output_8_fate__series___prisma_illya__fate__series__17.webp"}, {"prompt": "zombie_land_saga, konno junko, zombie land saga", "chinese_prompt": "绀野纯子 (佐贺偶像是传奇)", "image_path": "assets/output_8_zombie_land_saga__konno_junko__zombie_land_saga_18.webp"}, {"prompt": "umamusume, meisho doto (umamusume), umamusume", "chinese_prompt": "名将怒涛 (赛马娘)", "image_path": "assets/output_8_umamusume__meisho_doto__umamusume___umamusume_19.webp"}, {"prompt": "chainsaw_man, poch<PERSON> (chainsaw man), chainsaw man", "chinese_prompt": "波奇塔 (电锯人)", "image_path": "assets/output_8_chainsaw_man__pochita__chainsaw_man___chainsaw_man_20.webp"}, {"prompt": "tsukihime, ciel (tsukihime), tsuki<PERSON>e", "chinese_prompt": "希耶尔 (真月谭－月姬)", "image_path": "assets/output_8_tsukihime__ciel__tsukihime___tsukihime_21.webp"}, {"prompt": "bang_dream!, taka<PERSON><PERSON> tomori, bang dream!", "chinese_prompt": "高松灯 (Bang Dream!)", "image_path": "assets/output_8_bang_dream___taka<PERSON><PERSON>_tomori__bang_dream__22.webp"}, {"prompt": "blue_archive, tsubaki (blue archive), blue archive", "chinese_prompt": "春日椿 (蔚蓝档案)", "image_path": "assets/output_8_blue_archive__tsubaki__blue_archive___blue_archive_23.webp"}, {"prompt": "gakuen_idolmaster, shino<PERSON><PERSON> hiro, gakuen idolmaster", "chinese_prompt": "筿泽广 (学园偶像大师)", "image_path": "assets/output_8_gakuen_idolmaster__s<PERSON><PERSON><PERSON>_hiro__gakuen_idolmaster_24.webp"}, {"prompt": "honkai_(series), fu hua (herrscher of sentience), honkai (series)", "chinese_prompt": "符华 (识之律者) (崩坏)", "image_path": "assets/output_8_honkai__series___fu_hua__herrscher_of_sentience___honkai__series__25.webp"}, {"prompt": "watashi_ga_motenai_no_wa_dou_kangaete<PERSON>_omaera_ga_warui!, tamura yuri, watashi ga motenai no wa dou kangaetemo omaera ga warui!", "chinese_prompt": "田村百合 (我不受欢迎，怎么想都是你们的错)", "image_path": "assets/output_8_watashi_ga_motenai_no_wa_dou_kangaetemo_omaera_ga_warui___tamura_yuri__watashi_ga_motenai_no_wa_dou_kangaetemo_omaera_ga_warui__26.webp"}, {"prompt": "precure, higashi setsuna, precure", "chinese_prompt": "东刹那 (光之美少女)", "image_path": "assets/output_8_precure__higashi_setsuna__precure_27.webp"}, {"prompt": "kantai_collection, hayashimo (kancolle), kantai collection", "chinese_prompt": "早霜 (舰队收藏)", "image_path": "assets/output_8_kantai_collection__hayashimo__kancolle___kantai_collection_28.webp"}, {"prompt": "helltaker, modeus (helltaker), helltaker", "chinese_prompt": "墨狄斯 (Hell<PERSON>)", "image_path": "assets/output_8_helltaker__modeus__helltaker___helltaker_29.webp"}, {"prompt": "hololive, hakui koy<PERSON> (1st costume), hololive", "chinese_prompt": "博衣小夜璃 (1st服) (Hololive)", "image_path": "assets/output_8_hololive__hakui_koy<PERSON>__1st_costume___hololive_30.webp"}, {"prompt": "cardcaptor_sakura, kero, cardcaptor sakura", "chinese_prompt": "可鲁 (库洛魔法使)", "image_path": "assets/output_8_cardcaptor_sakura__kero__cardcaptor_sakura_31.webp"}, {"prompt": "persona, ni<PERSON><PERSON> makoto, persona", "chinese_prompt": "新岛真 (P5) (女神异闻录)", "image_path": "assets/output_8_persona__ni<PERSON><PERSON>_makoto__persona_32.webp"}, {"prompt": "fate/grand_order, mi<PERSON><PERSON> musa<PERSON> (swimsuit berserker) (fate), fate/grand order", "chinese_prompt": "宫本武藏（泳装狂战士) (Fate/Grand Order)", "image_path": "assets/output_8_fate_grand_order__mi<PERSON>oto_musashi__swimsuit_berserker___fate___fate_grand_order_33.webp"}, {"prompt": "tales_of_(series), yuri <PERSON>, tales of (series)", "chinese_prompt": "尤里·洛威尔 (TOV) (传说系列)", "image_path": "assets/output_8_tales_of__series___yuri_lowell__tales_of__series__34.webp"}, {"prompt": "shingeki_no_kyojin, reiner braun, shingeki no kyojin", "chinese_prompt": "莱纳·布朗 (进击的巨人)", "image_path": "assets/output_8_shingeki_no_kyojin__reiner_braun__shingeki_no_kyojin_35.webp"}, {"prompt": "blue_archive, akane (blue archive), blue archive", "chinese_prompt": "室笠朱音 (蔚蓝档案)", "image_path": "assets/output_8_blue_archive__akane__blue_archive___blue_archive_36.webp"}, {"prompt": "to<PERSON><PERSON>, alice marga<PERSON> (pc-98), to<PERSON><PERSON>", "chinese_prompt": "爱丽丝·玛格特罗伊德 (PC-98版) (东方)", "image_path": "assets/output_8_touhou__alice_margatroid__pc-98___touhou_37.webp"}, {"prompt": "pokemon, gladion (pokemon), pokemon", "chinese_prompt": "格拉吉欧 (宝可梦)", "image_path": "assets/output_8_pokemon__gladion__pokemon___pokemon_38.webp"}, {"prompt": "pokemon, sobble, pokemon", "chinese_prompt": "泪眼蜥 (宝可梦)", "image_path": "assets/output_8_pokemon__sobble__pokemon_39.webp"}, {"prompt": "splatoon_(series), pearl (splatoon), splatoon (series)", "chinese_prompt": "小姬 (斯普拉遁)", "image_path": "assets/output_8_splatoon__series___pearl__splatoon___splatoon__series__40.webp"}, {"prompt": "touhou, nishida satono, touhou", "chinese_prompt": "尔子田里乃 (东方)", "image_path": "assets/output_8_touhou__nishida_satono__touhou_41.webp"}, {"prompt": "azur_lane, dido (azur lane), azur lane", "chinese_prompt": "黛朵 (碧蓝航线)", "image_path": "assets/output_8_azur_lane__dido__azur_lane___azur_lane_42.webp"}, {"prompt": "yuuki_bakuhatsu_bang_bravern, le<PERSON><PERSON> smith, yuuki bakuhatsu bang bravern", "chinese_prompt": "路易斯·史密斯 (勇气爆发Bang Bravern)", "image_path": "assets/output_8_yuuki_bakuhatsu_bang_bravern__lewis_smith__yuuki_bakuhatsu_bang_bravern_43.webp"}, {"prompt": "kantai_collection, enemy aircraft (kancolle), kantai collection", "chinese_prompt": "敌机 (舰队收藏)", "image_path": "assets/output_8_kantai_collection__enemy_aircraft__kancolle___kantai_collection_44.webp"}, {"prompt": "idolmaster, tan<PERSON> mami<PERSON>, idolmaster", "chinese_prompt": "田中摩美美 (闪耀色彩) (偶像大师)", "image_path": "assets/output_8_idolmaster__tan<PERSON>_mamimi__idolmaster_45.webp"}, {"prompt": "hololive, tsukumo sana, hololive", "chinese_prompt": "九十九佐命 (Hololive)", "image_path": "assets/output_8_hololive__tsuku<PERSON>_sana__hololive_46.webp"}, {"prompt": "blue_archive, has<PERSON> (track) (blue archive), blue archive", "chinese_prompt": "羽川莲实（体操服） (蔚蓝档案)", "image_path": "assets/output_8_blue_archive__hasumi__track___blue_archive___blue_archive_47.webp"}, {"prompt": "karakai_jouzu_no_takagi-san, takagi-san, karakai jouzu no takagi-san", "chinese_prompt": "高木同学 (擅长捉弄人的高木同学)", "image_path": "assets/output_8_karakai_jouzu_no_takagi-san__takagi-san__karakai_jouzu_no_takagi-san_48.webp"}, {"prompt": "niji<PERSON>ji, pomu rainpuff, niji<PERSON>ji", "chinese_prompt": "pomu rainpuff (彩虹社)", "image_path": "assets/output_8_niji<PERSON>ji__pomu_rainpuff__nijisanji_49.webp"}, {"prompt": "poptepipic, popuko, poptepipic", "chinese_prompt": "pop子 (pop子和pipi美的日常)", "image_path": "assets/output_8_poptepipic__popuko__poptepipic_50.webp"}, {"prompt": "bishoujo_senshi_sailor_moon, sailor mercury, bishoujo senshi sailor moon", "chinese_prompt": "水手水星 (美少女战士)", "image_path": "assets/output_8_bishoujo_senshi_sailor_moon__sailor_mercury__bishoujo_senshi_sailor_moon_51.webp"}, {"prompt": "idolmaster, ni<PERSON><PERSON>, idolmaster", "chinese_prompt": "二宫飞鸟 (灰姑娘) (偶像大师)", "image_path": "assets/output_8_idolmaster__ni<PERSON><PERSON>_asuka__idolmaster_52.webp"}, {"prompt": "chainsaw_man, him<PERSON> (chainsaw man), chainsaw man", "chinese_prompt": "姬野 (电锯人)", "image_path": "assets/output_8_chainsaw_man__himeno__chainsaw_man___chainsaw_man_53.webp"}, {"prompt": "fire_emblem, tharja (fire emblem), fire emblem", "chinese_prompt": "撒拉 (觉醒) (圣火降魔录)", "image_path": "assets/output_8_fire_emblem__tharja__fire_emblem___fire_emblem_54.webp"}, {"prompt": "monogatari_(series), hachi<PERSON><PERSON> may<PERSON>, monogatari (series)", "chinese_prompt": "八九寺真鱼 (化物语)", "image_path": "assets/output_8_monogatari__series___hachikuji_mayoi__monogatari__series__55.webp"}, {"prompt": "yahari_ore_no_seishun_lovecome_wa_machigatteiru., yuiga<PERSON>a yui, yahari ore no seishun lovecome wa machigatteiru.", "chinese_prompt": "由比滨结衣 (果然我的青春恋爱喜剧搞错了)", "image_path": "assets/output_8_yahari_ore_no_seishun_lovecome_wa_machigatteiru___yuigahama_yui__yahari_ore_no_seishun_lovecome_wa_machigatteiru__56.webp"}, {"prompt": "arknights, sussurro (arknights), arknights", "chinese_prompt": "苏苏洛 (明日方舟)", "image_path": "assets/output_8_arknights__sussurro__arknights___arknights_57.webp"}, {"prompt": "hololive, ka<PERSON>a <PERSON> (1st costume), hololive", "chinese_prompt": "风真伊吕波 (1st服) (Hololive)", "image_path": "assets/output_8_hololive__ka<PERSON>a_<PERSON><PERSON><PERSON>__1st_costume___hololive_58.webp"}, {"prompt": "genshin_impact, dehya (genshin impact), genshin impact", "chinese_prompt": "迪希雅 (原神)", "image_path": "assets/output_8_genshin_impact__dehya__genshin_impact___genshin_impact_59.webp"}, {"prompt": "sousou_no_frieren, stark (sousou no frieren), sousou no frieren", "chinese_prompt": "修塔尔克 (葬送的芙莉莲)", "image_path": "assets/output_8_sousou_no_frieren__stark__sousou_no_frieren___sousou_no_frieren_60.webp"}, {"prompt": "girls'_frontline, st ar-15 (girls' frontline), girls' frontline", "chinese_prompt": "AR-15 (少女前线)", "image_path": "assets/output_8_girls__frontline__st_ar-15__girls__frontline___girls__frontline_61.webp"}, {"prompt": "arknights, siege (arknights), arknights", "chinese_prompt": "推进之王 (明日方舟)", "image_path": "assets/output_8_arknights__siege__arknights___arknights_62.webp"}, {"prompt": "idolmaster, to<PERSON>i airi, idolmaster", "chinese_prompt": "十时爱梨 (灰姑娘) (偶像大师)", "image_path": "assets/output_8_idolmaster__totoki_airi__idolmaster_63.webp"}, {"prompt": "kantai_collection, etorofu (kancolle), kantai collection", "chinese_prompt": "择捉 (舰队收藏)", "image_path": "assets/output_8_kantai_collection__etorofu__kancolle___kantai_collection_64.webp"}, {"prompt": "hololive, sakura miko (1st costume), hololive", "chinese_prompt": "樱巫女 (1st服) (Hololive)", "image_path": "assets/output_8_hololive__sakura_miko__1st_costume___hololive_65.webp"}, {"prompt": "dragon_ball, chi-chi (dragon ball), dragon ball", "chinese_prompt": "琪琪 (七龙珠)", "image_path": "assets/output_8_dragon_ball__chi-chi__dragon_ball___dragon_ball_66.webp"}, {"prompt": "idolmaster, <PERSON><PERSON> mom<PERSON>, idolmaster", "chinese_prompt": "周防桃子 (百万现场) (偶像大师)", "image_path": "assets/output_8_idolmaster__su<PERSON>_momoko__idolmaster_67.webp"}, {"prompt": "pokemon, cyndaquil, pokemon", "chinese_prompt": "火球鼠 (宝可梦)", "image_path": "assets/output_8_pokemon__cyndaquil__pokemon_68.webp"}, {"prompt": "yurucamp, kagamihara nadeshiko, yurucamp", "chinese_prompt": "各务原抚子 (摇曳露营)", "image_path": "assets/output_8_yurucamp__kagamihara_nadeshiko__yurucamp_69.webp"}, {"prompt": "precure, tsukikage yuri, precure", "chinese_prompt": "月影百合 (光之美少女)", "image_path": "assets/output_8_precure__tsukikage_yuri__precure_70.webp"}, {"prompt": "hololive, mococo a<PERSON><PERSON> (1st costume), hololive", "chinese_prompt": "茸茸·阿比斯加德 (Mococo Abyssgard) (1st服), (Hololive)", "image_path": "assets/output_8_hololive__mococo_abyssgard__1st_costume___hololive_71.webp"}, {"prompt": "vocaloid, magical mirai miku, vocaloid", "chinese_prompt": "魔法未来 (Vocaloid)", "image_path": "assets/output_8_vocaloid__magical_mirai_miku__vocaloid_72.webp"}, {"prompt": "kantai_collection, ark royal (kancolle), kantai collection", "chinese_prompt": "皇家方舟 (舰队收藏)", "image_path": "assets/output_8_kantai_collection__ark_royal__kancolle___kantai_collection_73.webp"}, {"prompt": "tsuki<PERSON><PERSON>, to<PERSON>o a<PERSON>, tsuki<PERSON>e", "chinese_prompt": "远野秋叶 (真月谭－月姬)", "image_path": "assets/output_8_tsukihime__tohno_aki<PERSON>__tsukihime_74.webp"}, {"prompt": "the_king_of_fighters, leona he<PERSON>n, the king of fighters", "chinese_prompt": "莉安娜·哈迪兰 (KOF)", "image_path": "assets/output_8_the_king_of_fighters__leona_heidern__the_king_of_fighters_75.webp"}, {"prompt": "touhou, teire<PERSON> mai, touhou", "chinese_prompt": "丁礼田舞 (东方)", "image_path": "assets/output_8_touh<PERSON>__teireida_mai__touhou_76.webp"}, {"prompt": "wuthering_waves, changli (wuthering waves), wuthering waves", "chinese_prompt": "长离 (鸣潮)", "image_path": "assets/output_8_wuthering_waves__changli__wuthering_waves___wuthering_waves_77.webp"}, {"prompt": "blue_archive, kanna (swimsuit) (blue archive), blue archive", "chinese_prompt": "尾刃环奈 (泳装)  (蔚蓝档案)", "image_path": "assets/output_8_blue_archive__kanna__swimsuit___blue_archive___blue_archive_78.webp"}, {"prompt": "splatoon_(series), shiver (splatoon), splatoon (series)", "chinese_prompt": "曼曼 (系列)", "image_path": "assets/output_8_splatoon__series___shiver__splatoon___splatoon__series__79.webp"}, {"prompt": "umamusume, smart falcon (umamusume), umamusume", "chinese_prompt": "醒目飞鹰 (赛马娘)", "image_path": "assets/output_8_umamusume__smart_falcon__umamusume___umamusume_80.webp"}, {"prompt": "blue_archive, eimi (blue archive), blue archive", "chinese_prompt": "和泉元英美 (蔚蓝档案)", "image_path": "assets/output_8_blue_archive__eimi__blue_archive___blue_archive_81.webp"}, {"prompt": "kantai_collection, colorado (kancolle), kantai collection", "chinese_prompt": "科罗拉多 (舰队收藏)", "image_path": "assets/output_8_kantai_collection__colorado__kancolle___kantai_collection_82.webp"}, {"prompt": "pokemon, leafeon, pokemon", "chinese_prompt": "叶伊布 (宝可梦)", "image_path": "assets/output_8_pokemon__leafeon__pokemon_83.webp"}, {"prompt": "kantai_collection, kamoi (kancolle), kantai collection", "chinese_prompt": "神威 (舰队收藏)", "image_path": "assets/output_8_kantai_collection__kamoi__kancolle___kantai_collection_84.webp"}, {"prompt": "pokemon, hop (pokemon), pokemon", "chinese_prompt": "赫普 (宝可梦)", "image_path": "assets/output_8_pokemon__hop__pokemon___pokemon_85.webp"}, {"prompt": "saibou_shinkyoku, isoi reiji, saibou shinkyoku", "chinese_prompt": "矶井丽慈 (细胞神曲)", "image_path": "assets/output_8_saibou_shinkyoku__isoi_reiji__saibou_shinkyoku_86.webp"}, {"prompt": "touhou, su-san, touhou", "chinese_prompt": "梅蒂欣·梅兰可莉 su-san (妖精) (东方)", "image_path": "assets/output_8_touhou__su-san__touhou_87.webp"}, {"prompt": "hololive, moona ho<PERSON>, hololive", "chinese_prompt": "暮娜·惑星诺瓦 (Hololive)", "image_path": "assets/output_8_hololive__<PERSON><PERSON>_ho<PERSON><PERSON>__hololive_88.webp"}, {"prompt": "naruto_(series), tsunade (naruto), naruto (series)", "chinese_prompt": "纲手 (火影忍者)", "image_path": "assets/output_8_naruto__series___tsunade__naruto___naruto__series__89.webp"}, {"prompt": "touhou, j<PERSON><PERSON><PERSON><PERSON>, touhou", "chinese_prompt": "杖刀偶磨弓 (东方)", "image_path": "assets/output_8_touh<PERSON>__joutou<PERSON><PERSON>_mayumi__touhou_90.webp"}, {"prompt": "monogatari_(series), a<PERSON><PERSON> koyomi, monogatari (series)", "chinese_prompt": "阿良良木暦 (化物语)", "image_path": "assets/output_8_monogatari__series___araragi_koyomi__monogatari__series__91.webp"}, {"prompt": "hololive, ouro kron<PERSON> (1st costume), hololive", "chinese_prompt": "奥罗·克洛尼 (1st服), (Hololive)", "image_path": "assets/output_8_hololive__ouro_kronii__1st_costume___hololive_92.webp"}, {"prompt": "kantai_collection, umikaze (kancolle), kantai collection", "chinese_prompt": "海风 (舰队收藏)", "image_path": "assets/output_8_kantai_collection__umikaze__kancolle___kantai_collection_93.webp"}, {"prompt": "kill_me_baby, or<PERSON> ya<PERSON>a, kill me baby", "chinese_prompt": "折部安奈 (爱杀宝贝)", "image_path": "assets/output_8_kill_me_baby__oribe_ya<PERSON><PERSON>__kill_me_baby_94.webp"}, {"prompt": "oshi_no_ko, hoshino ruby, oshi no ko", "chinese_prompt": "露比 (我推的孩子)", "image_path": "assets/output_8_oshi_no_ko__hoshino_ruby__oshi_no_ko_95.webp"}, {"prompt": "blue_archive, nonomi (swimsuit) (blue archive), blue archive", "chinese_prompt": "十六夜野乃美（泳装） (蔚蓝档案)", "image_path": "assets/output_8_blue_archive__nonomi__swimsuit___blue_archive___blue_archive_96.webp"}, {"prompt": "kid_icarus, pit (kid icarus), kid icarus", "chinese_prompt": "天使比特 (光神话)", "image_path": "assets/output_8_kid_icarus__pit__kid_icarus___kid_icarus_97.webp"}, {"prompt": "idolmaster, ka<PERSON>, idolmaster", "chinese_prompt": "风野灯织 (闪耀色彩) (偶像大师)", "image_path": "assets/output_8_idolmaster__ka<PERSON>_hi<PERSON>__idolmaster_98.webp"}, {"prompt": "tengen_toppa_gurren_lagann, kamina (ttgl), tengen toppa gurren lagann", "chinese_prompt": "卡米那 (天元突破 红莲螺岩)", "image_path": "assets/output_8_tengen_toppa_gurren_lagann__kamina__ttgl___tengen_toppa_gurren_lagann_99.webp"}, {"prompt": "idolmaster, <PERSON><PERSON>, idolmaster", "chinese_prompt": "大崎甜花 (闪耀色彩) (偶像大师)", "image_path": "assets/output_8_idolmaster__<PERSON><PERSON>_ten<PERSON>__idolmaster_100.webp"}, {"prompt": "ni<PERSON><PERSON><PERSON>, tsu<PERSON><PERSON> mito (1st costume), ni<PERSON><PERSON><PERSON>", "chinese_prompt": "月之美兔 (1st服), (彩虹社)", "image_path": "assets/output_8_niji<PERSON><PERSON>__tsukino_mito__1st_costume___niji<PERSON>ji_101.webp"}, {"prompt": "voiceroid, tsu<PERSON><PERSON> maki, voiceroid", "chinese_prompt": "弦卷真纪 (Vocaloid)", "image_path": "assets/output_8_voiceroid__tsu<PERSON><PERSON>_maki__voiceroid_102.webp"}, {"prompt": "aria_(manga), mi<PERSON><PERSON><PERSON> aka<PERSON>, aria (manga)", "chinese_prompt": "水无灯里 (水星领航员)", "image_path": "assets/output_8_aria__manga___mizu<PERSON><PERSON>_akari__aria__manga__103.webp"}, {"prompt": "the_king_of_fighters, angel (kof), the king of fighters", "chinese_prompt": "安琪儿 (KOF)", "image_path": "assets/output_8_the_king_of_fighters__angel__kof___the_king_of_fighters_104.webp"}, {"prompt": "danganron<PERSON>_(series), cele<PERSON><PERSON> l<PERSON>, dangan<PERSON><PERSON> (series)", "chinese_prompt": "瑟雷丝蒂亚·卢登堡 (弹丸论破)", "image_path": "assets/output_8_danganronpa__series___celestia_ludenberg__danganronpa__series__105.webp"}, {"prompt": "blazblue, ragna the bloodedge, blazblue", "chinese_prompt": "拉格纳‧ 布莱德艾奇 (苍翼默示录)", "image_path": "assets/output_8_blazblue__ragna_the_bloodedge__blazblue_106.webp"}, {"prompt": "persona, tatsumi kanji, persona", "chinese_prompt": "巽完二 (P5) (女神异闻录)", "image_path": "assets/output_8_persona__tatsumi_kanji__persona_107.webp"}, {"prompt": "kantai_collection, hagikaze (kancolle), kantai collection", "chinese_prompt": "萩风 (舰队收藏)", "image_path": "assets/output_8_kantai_collection__hagikaze__kancolle___kantai_collection_108.webp"}, {"prompt": "kantai_collection, richelieu (kancolle), kantai collection", "chinese_prompt": "黎希留 (舰队收藏)", "image_path": "assets/output_8_kantai_collection__richelieu__kancolle___kantai_collection_109.webp"}, {"prompt": "project_sekai, yo<PERSON>ki kanade, project sekai", "chinese_prompt": "Project Sekai, 夜咏奏, Project Sekai", "image_path": "assets/output_8_project_sekai__yo<PERSON><PERSON>_kanade__project_sekai_110.webp"}, {"prompt": "mario_(series), boo (mario), mario (series)", "chinese_prompt": "害羞幽灵 (超级玛利欧)", "image_path": "assets/output_8_mario__series___boo__mario___mario__series__111.webp"}, {"prompt": "idolmaster, i<PERSON><PERSON> hi<PERSON>, idolmaster", "chinese_prompt": "市川雏菜 (闪耀色彩) (偶像大师)", "image_path": "assets/output_8_idolmaster__i<PERSON><PERSON>_hi<PERSON>a__idolmaster_112.webp"}, {"prompt": "kantai_collection, failure penguin, kantai collection", "chinese_prompt": "失败企鹅 (舰队收藏)", "image_path": "assets/output_8_kantai_collection__failure_penguin__kantai_collection_113.webp"}, {"prompt": "kantai_collection, oyas<PERSON>o (kancolle), kantai collection", "chinese_prompt": "亲潮 (舰队收藏)", "image_path": "assets/output_8_kantai_collection__o<PERSON><PERSON><PERSON>__kancolle___kantai_collection_114.webp"}, {"prompt": "oshi_no_ko, hoshino aquamarine, oshi no ko", "chinese_prompt": "阿奎亚 (我推的孩子)", "image_path": "assets/output_8_oshi_no_ko__hoshino_aquamarine__oshi_no_ko_115.webp"}, {"prompt": "umamusume, katsu<PERSON>i ace (umamusume), umamusume", "chinese_prompt": "葛城王牌 (赛马娘)", "image_path": "assets/output_8_umamusume__katsuragi_ace__umamusume___umamusume_116.webp"}, {"prompt": "blue_archive, nagisa (blue archive), blue archive", "chinese_prompt": "桐藤渚 (蔚蓝档案)", "image_path": "assets/output_8_blue_archive__nagisa__blue_archive___blue_archive_117.webp"}, {"prompt": "touken_ranbu, yamat<PERSON><PERSON><PERSON>-kami ya<PERSON>, touken ranbu", "chinese_prompt": "大和守安定 (刀剑乱舞)", "image_path": "assets/output_8_touken_ranbu__yamato-no-kami_ya<PERSON><PERSON>__touken_ranbu_118.webp"}, {"prompt": "yuru_yuri, yoshi<PERSON> chin<PERSON>u, yuru yuri", "chinese_prompt": "吉川千夏 (悠哉日常大王)", "image_path": "assets/output_8_yuru_yuri__yo<PERSON><PERSON>_chinatsu__yuru_yuri_119.webp"}, {"prompt": "girls_und_panzer, ka<PERSON><PERSON> anzu, girls und panzer", "chinese_prompt": "角谷杏 (少女与战车)", "image_path": "assets/output_8_girls_und_panzer__kado<PERSON>_anzu__girls_und_panzer_120.webp"}, {"prompt": "little_witch_academia, diana cavendish, little witch academia", "chinese_prompt": "戴安娜·卡文迪什 (小魔女学院)", "image_path": "assets/output_8_little_witch_academia__diana_cavendish__little_witch_academia_121.webp"}, {"prompt": "blue_archive, koharu (swimsuit) (blue archive), blue archive", "chinese_prompt": "下江小春 (泳装)  (蔚蓝档案)", "image_path": "assets/output_8_blue_archive__koharu__swimsuit___blue_archive___blue_archive_122.webp"}, {"prompt": "gundam, haro, gundam", "chinese_prompt": "哈啰 (钢弹)", "image_path": "assets/output_8_gundam__haro__gundam_123.webp"}, {"prompt": "pokemon, iris (pokemon), pokemon", "chinese_prompt": "艾莉丝 (宝可梦)", "image_path": "assets/output_8_pokemon__iris__pokemon___pokemon_124.webp"}, {"prompt": "pokemon, charmander, pokemon", "chinese_prompt": "小火龙 (宝可梦)", "image_path": "assets/output_8_pokemon__charmander__pokemon_125.webp"}, {"prompt": "danga<PERSON><PERSON><PERSON>_(series), ka<PERSON><PERSON>, dangan<PERSON><PERSON> (series)", "chinese_prompt": "神座出流 (弹丸论破)", "image_path": "assets/output_8_danganronpa__series___kamu<PERSON>_i<PERSON><PERSON>__danganronpa__series__126.webp"}, {"prompt": "fate_(series), caenis (fate), fate (series)", "chinese_prompt": "凯妮斯 (Fate)", "image_path": "assets/output_8_fate__series___caenis__fate___fate__series__127.webp"}, {"prompt": "granblue_fantasy, cagliostro (granblue fantasy), granblue fantasy", "chinese_prompt": "卡里奥斯特罗 (碧蓝幻想)", "image_path": "assets/output_8_granblue_fantasy__cagliostro__granblue_fantasy___granblue_fantasy_128.webp"}, {"prompt": "idolmaster, yuuki haru, idolmaster", "chinese_prompt": "结城晴 (灰姑娘) (偶像大师)", "image_path": "assets/output_8_idolmaster__yuuki_haru__idolmaster_129.webp"}, {"prompt": "genshin_impact, wriot<PERSON>ley (genshin impact), genshin impact", "chinese_prompt": "莱欧斯利 (原神)", "image_path": "assets/output_8_genshin_impact__wriot<PERSON>ley__genshin_impact___genshin_impact_130.webp"}, {"prompt": "infinite_stratos, charlotte dunois, infinite stratos", "chinese_prompt": "夏洛特·杜诺斯 (IS)", "image_path": "assets/output_8_infinite_stratos__charlotte_dunois__infinite_stratos_131.webp"}, {"prompt": "pokemon, victor (pokemon), pokemon", "chinese_prompt": "小胜 (宝可梦)", "image_path": "assets/output_8_pokemon__victor__pokemon___pokemon_132.webp"}, {"prompt": "chainsaw_man, mitaka asa, chainsaw man", "chinese_prompt": "三鹰朝 (电锯人)", "image_path": "assets/output_8_chainsaw_man__mitaka_asa__chainsaw_man_133.webp"}, {"prompt": "kantai_collection, little boy admiral (kanco<PERSON>), kantai collection", "chinese_prompt": "小男孩提督 (舰队收藏)", "image_path": "assets/output_8_kantai_collection__little_boy_admiral__ka<PERSON><PERSON>___kantai_collection_134.webp"}, {"prompt": "hololive, fu<PERSON><PERSON> (1st costume), hololive", "chinese_prompt": "软软·阿比斯加德 (Fuwawa Abyssgard) (1st服) (Hololive)", "image_path": "assets/output_8_hololive__fuwawa_abyssgard__1st_costume___hololive_135.webp"}, {"prompt": "fate_(series), atalanta (fate), fate (series)", "chinese_prompt": "阿塔兰塔 (Fate)", "image_path": "assets/output_8_fate__series___atalanta__fate___fate__series__136.webp"}, {"prompt": "one-punch_man, sa<PERSON><PERSON> (one-punch man), one-punch man", "chinese_prompt": "埼玉 (一拳超人)", "image_path": "assets/output_8_one-punch_man__sa<PERSON>ma__one-punch_man___one-punch_man_137.webp"}, {"prompt": "fate_(series), okita souji alter (fate), fate (series)", "chinese_prompt": "魔神冲田总司 (Fate)", "image_path": "assets/output_8_fate__series___okita_souji_alter__fate___fate__series__138.webp"}, {"prompt": "hidamari_sketch, yuno (hidamari sketch), hidamari sketch", "chinese_prompt": "由乃 (向阳素描)", "image_path": "assets/output_8_hidamari_sketch__yuno__hidamari_sketch___hidamari_sketch_139.webp"}, {"prompt": "touhou, tokiko (touhou), touhou", "chinese_prompt": "朱鹭子 (东方)", "image_path": "assets/output_8_touhou__to<PERSON><PERSON>__touhou___touhou_140.webp"}, {"prompt": "jojo_no_kimyou_na_bouken, jose<PERSON> (old), jojo no kimyou na bouken", "chinese_prompt": "乔瑟夫·乔斯达 (老年) (二乔) (JOJO的奇妙冒险)", "image_path": "assets/output_8_jojo_no_kimyou_na_bouken__joseph_joestar__old___jojo_no_kimyou_na_bouken_141.webp"}, {"prompt": "kantai_collection, fubuki kai ni (kancolle), kantai collection", "chinese_prompt": "吹雪改二 (舰队收藏)", "image_path": "assets/output_8_kantai_collection__fubuki_kai_ni__kancolle___kantai_collection_142.webp"}, {"prompt": "granblue_fantasy, anila (granblue fantasy), granblue fantasy", "chinese_prompt": "阿妮拉 (碧蓝幻想)", "image_path": "assets/output_8_granblue_fantasy__anila__granblue_fantasy___granblue_fantasy_143.webp"}, {"prompt": "azur_lane, shinano (azur lane), azur lane", "chinese_prompt": "信浓 (碧蓝航线)", "image_path": "assets/output_8_azur_lane__shinano__azur_lane___azur_lane_144.webp"}, {"prompt": "fate_(series), to<PERSON><PERSON><PERSON> (fate), fate (series)", "chinese_prompt": "时太郎 (Fate)", "image_path": "assets/output_8_fate__series___toki<PERSON>ou__fate___fate__series__145.webp"}, {"prompt": "yuru_yuri, o<PERSON><PERSON>, yuru yuri", "chinese_prompt": "大室樱子 (悠哉日常大王)", "image_path": "assets/output_8_yuru_yuri__o<PERSON><PERSON>_sakura<PERSON>__yuru_yuri_146.webp"}, {"prompt": "kantai_collection, shouhou (kancolle), kantai collection", "chinese_prompt": "祥凤 (舰队收藏)", "image_path": "assets/output_8_kantai_collection__shouhou__kancolle___kantai_collection_147.webp"}, {"prompt": "granblue_fantasy, zeta (granblue fantasy), granblue fantasy", "chinese_prompt": "瑟塔 (碧蓝幻想)", "image_path": "assets/output_8_granblue_fantasy__zeta__granblue_fantasy___granblue_fantasy_148.webp"}, {"prompt": "nier:automata, pod (nier:automata), nier:automata", "chinese_prompt": "辅助机 (尼尔:自动人形)", "image_path": "assets/output_8_nier_automata__pod__nier_automata___nier_automata_149.webp"}, {"prompt": "umamusume, fine motion (umamusume), umamusume", "chinese_prompt": "美妙姿势 (赛马娘)", "image_path": "assets/output_8_umamusume__fine_motion__umamusume___umamusume_150.webp"}, {"prompt": "one_piece, tony tony chopper, one piece", "chinese_prompt": "多尼多尼·乔巴 (海贼王)", "image_path": "assets/output_8_one_piece__tony_tony_chopper__one_piece_151.webp"}, {"prompt": "idolmaster, i<PERSON><PERSON> nina, idolmaster", "chinese_prompt": "市原仁奈 (灰姑娘) (偶像大师)", "image_path": "assets/output_8_idolmaster__i<PERSON><PERSON>_nina__idolmaster_152.webp"}, {"prompt": "umine<PERSON>_no_naku_koro_ni, f<PERSON><PERSON><PERSON>, umineko no naku koro ni", "chinese_prompt": "贝伦卡斯泰露 (海猫鸣泣之时)", "image_path": "assets/output_8_umineko_no_naku_koro_ni__frederic<PERSON>_bern<PERSON><PERSON>__umineko_no_naku_koro_ni_153.webp"}, {"prompt": "dungeon_meshi, chilchuck tims, dungeon meshi", "chinese_prompt": "齐尔查克 (迷宫饭)", "image_path": "assets/output_8_dungeon_meshi__chilchuck_tims__dungeon_meshi_154.webp"}, {"prompt": "girls_band_cry, awa subaru, girls band cry", "chinese_prompt": "安和昴 (Girls Band Cry)", "image_path": "assets/output_8_girls_band_cry__awa_subaru__girls_band_cry_155.webp"}, {"prompt": "fate_(series), m<PERSON><PERSON> shi<PERSON> (fate), fate (series)", "chinese_prompt": "紫式部 (Fate)", "image_path": "assets/output_8_fate__series___m<PERSON><PERSON>_s<PERSON><PERSON><PERSON>__fate___fate__series__156.webp"}, {"prompt": "danga<PERSON><PERSON><PERSON>_(series), ma<PERSON><PERSON> say<PERSON>, danga<PERSON><PERSON><PERSON> (series)", "chinese_prompt": "舞园沙耶香 (弹丸论破)", "image_path": "assets/output_8_danganronpa__series___maizono_sayaka__danganronpa__series__157.webp"}, {"prompt": "resident_evil, leon s. kennedy, resident evil", "chinese_prompt": "里昂·S·甘乃迪 (恶灵古堡)", "image_path": "assets/output_8_resident_evil__leon_s__kennedy__resident_evil_158.webp"}, {"prompt": "idolmaster, o<PERSON><PERSON> shi<PERSON>ku, idolmaster", "chinese_prompt": "及川雫 (灰姑娘) (偶像大师)", "image_path": "assets/output_8_idolmaster__o<PERSON><PERSON>_shi<PERSON><PERSON>__idolmaster_159.webp"}, {"prompt": "kingdom_hearts, sora (kingdom hearts), kingdom hearts", "chinese_prompt": "索拉 (王国之心)", "image_path": "assets/output_8_kingdom_hearts__sora__kingdom_hearts___kingdom_hearts_160.webp"}, {"prompt": "league_of_legends, akali, league of legends", "chinese_prompt": "阿卡莉 (英雄联盟 LOL)", "image_path": "assets/output_8_league_of_legends__akali__league_of_legends_161.webp"}, {"prompt": "danganron<PERSON>_(series), i<PERSON>a miu, dangan<PERSON><PERSON> (series)", "chinese_prompt": "入间美兔 (弹丸论破)", "image_path": "assets/output_8_danganronpa__series___iruma_miu__danganronpa__series__162.webp"}, {"prompt": "utau, kasane teto (sv), utau", "chinese_prompt": "重音Teto (SynthV) UTAU", "image_path": "assets/output_8_utau__kasane_teto__sv___utau_163.webp"}, {"prompt": "genshin_impact, lyney (genshin impact), genshin impact", "chinese_prompt": "林尼 (原神)", "image_path": "assets/output_8_genshin_impact__lyney__genshin_impact___genshin_impact_164.webp"}, {"prompt": "aikatsu!_(series), <PERSON><PERSON><PERSON> ichigo, aikatsu! (series)", "chinese_prompt": "星宫莓 (偶像活动！)", "image_path": "assets/output_8_aikatsu___series___ho<PERSON><PERSON>_ichigo__aikatsu___series__165.webp"}, {"prompt": "high_school_dxd, rias gremory, high school dxd", "chinese_prompt": "莉雅丝·格莫瑞 (恶魔高校DxD)", "image_path": "assets/output_8_high_school_dxd__rias_gremory__high_school_dxd_166.webp"}, {"prompt": "hololive, azki (hololive), hololive", "chinese_prompt": "AZKi (Hololive)", "image_path": "assets/output_8_hololive__azki__hololive___hololive_167.webp"}, {"prompt": "final_fantasy, zack fair, final fantasy", "chinese_prompt": "札克斯·菲尔 (ff7) (最终幻想)", "image_path": "assets/output_8_final_fantasy__zack_fair__final_fantasy_168.webp"}, {"prompt": "genshin_impact, hilichurl (genshin impact), genshin impact", "chinese_prompt": "丘丘人 (原神)", "image_path": "assets/output_8_genshin_impact__hilichurl__genshin_impact___genshin_impact_169.webp"}, {"prompt": "ado_(utaite), chando (ado), ado (utaite)", "chinese_prompt": "<PERSON><PERSON> (歌手)", "image_path": "assets/output_8_ado__utaite___chando__ado___ado__utaite__170.webp"}, {"prompt": "arknights, chong yue (arknights), arknights", "chinese_prompt": "重岳 (明日方舟)", "image_path": "assets/output_8_arknights__chong_yue__arknights___arknights_171.webp"}, {"prompt": "ghost_in_the_shell, kusa<PERSON><PERSON> motoko, ghost in the shell", "chinese_prompt": "草薙素子 (攻壳机动队)", "image_path": "assets/output_8_ghost_in_the_shell__kusana<PERSON>_motoko__ghost_in_the_shell_172.webp"}, {"prompt": "neptune_(series), blanc (neptunia), neptune (series)", "chinese_prompt": "布兰 白灵心 (战机少女)", "image_path": "assets/output_8_neptune__series___blanc__neptunia___neptune__series__173.webp"}, {"prompt": "one_piece, boa hancock, one piece", "chinese_prompt": "波雅·汉考克 蛇姬 (海贼王)", "image_path": "assets/output_8_one_piece__boa_hancock__one_piece_174.webp"}, {"prompt": "touken_ranbu, tsu<PERSON><PERSON> kuni<PERSON>, touken ranbu", "chinese_prompt": "鹤丸国永 (刀剑乱舞)", "image_path": "assets/output_8_touken_ranbu__tsurum<PERSON>_kuninaga__touken_ranbu_175.webp"}, {"prompt": "azur_lane, kashino (azur lane), azur lane", "chinese_prompt": "㭴野 (碧蓝航线)", "image_path": "assets/output_8_azur_lane__kashino__azur_lane___azur_lane_176.webp"}, {"prompt": "bishoujo_senshi_sailor_moon, sailor venus, bishoujo senshi sailor moon", "chinese_prompt": "水手金星 (美少女战士)", "image_path": "assets/output_8_bishoujo_senshi_sailor_moon__sailor_venus__bishoujo_senshi_sailor_moon_177.webp"}, {"prompt": "idolmaster, abe nana, idolmaster", "chinese_prompt": "安部菜菜 (灰姑娘) (偶像大师)", "image_path": "assets/output_8_idolmaster__abe_nana__idolmaster_178.webp"}, {"prompt": "danganronpa_(series), monomi (danganronpa), danganronpa (series)", "chinese_prompt": "黑白美 弹丸论破", "image_path": "assets/output_8_danganronpa__series___monomi__danganronpa___danganronpa__series__179.webp"}, {"prompt": "dungeon_meshi, kabru, dungeon meshi", "chinese_prompt": "卡布尔 (迷宫饭)", "image_path": "assets/output_8_dungeon_meshi__kabru__dungeon_meshi_180.webp"}, {"prompt": "umamusume, mejiro dober (umamusume), umamusume", "chinese_prompt": "目白多伯 (赛马娘)", "image_path": "assets/output_8_umamusume__mejiro_dober__umamusume___umamusume_181.webp"}, {"prompt": "tera_online, elin, tera online", "chinese_prompt": "艾琳 (Tera Online)", "image_path": "assets/output_8_tera_online__elin__tera_online_182.webp"}, {"prompt": "touhou, tenkyuu chimata, touhou", "chinese_prompt": "天弓千亦 (东方)", "image_path": "assets/output_8_touhou__tenkyuu_chimata__touhou_183.webp"}, {"prompt": "kantai_collection, arashi (kancolle), kantai collection", "chinese_prompt": "岚 (舰队收藏)", "image_path": "assets/output_8_kantai_collection__a<PERSON>i__kancolle___kantai_collection_184.webp"}, {"prompt": "kantai_collection, enemy lifebuoy (kancolle), kantai collection", "chinese_prompt": "敌方救生圈 (舰队收藏)", "image_path": "assets/output_8_kantai_collection__enemy_lifebuoy__kancolle___kantai_collection_185.webp"}, {"prompt": "fate/grand_order, boudica (fate), fate/grand order", "chinese_prompt": "布狄卡 (Fate/Grand Order)", "image_path": "assets/output_8_fate_grand_order__boudica__fate___fate_grand_order_186.webp"}, {"prompt": "girls_und_panzer, marie (girls und panzer), girls und panzer", "chinese_prompt": "玛丽 (少女与战车)", "image_path": "assets/output_8_girls_und_panzer__marie__girls_und_panzer___girls_und_panzer_187.webp"}, {"prompt": "kantai_collection, asagu<PERSON> (kancolle), kantai collection", "chinese_prompt": "朝云 (舰队收藏)", "image_path": "assets/output_8_kantai_collection__asagu<PERSON>__kancolle___kantai_collection_188.webp"}, {"prompt": "fate_(series), caren hortensia, fate (series)", "chinese_prompt": "阿摩耳 (Fate)", "image_path": "assets/output_8_fate__series___caren_hortensia__fate__series__189.webp"}, {"prompt": "saki, ha<PERSON><PERSON> nodoka, saki", "chinese_prompt": "原村和 (天才麻将少女)", "image_path": "assets/output_8_saki__ha<PERSON><PERSON>_nodoka__saki_190.webp"}, {"prompt": "reverse:1999, vertin (reverse:1999), reverse:1999", "chinese_prompt": "维尔汀 (重返未来:1999)", "image_path": "assets/output_8_reverse_1999__vertin__reverse_1999___reverse_1999_191.webp"}, {"prompt": "precure, myoudouin itsuki, precure", "chinese_prompt": "明堂院树 (光之美少女)", "image_path": "assets/output_8_precure__myoudouin_itsuki__precure_192.webp"}, {"prompt": "sousou_no_frieren, ubel (sousou no frieren), sousou no frieren", "chinese_prompt": "尤贝尔 (葬送的芙莉莲)", "image_path": "assets/output_8_sousou_no_frieren__ubel__sousou_no_frieren___sousou_no_frieren_193.webp"}, {"prompt": "danganronpa_(series), <PERSON><PERSON> i<PERSON>, danganron<PERSON> (series)", "chinese_prompt": "澪田唯吹 (弹丸论破)", "image_path": "assets/output_8_danganronpa__series___mioda_ibuki__danganronpa__series__194.webp"}, {"prompt": "guilty_gear, may (guilty gear), guilty gear", "chinese_prompt": "梅 (圣骑士之战)", "image_path": "assets/output_8_guilty_gear__may__guilty_gear___guilty_gear_195.webp"}, {"prompt": "pokemon, calem (pokemon), pokemon", "chinese_prompt": "卡鲁穆 (宝可梦)", "image_path": "assets/output_8_pokemon__calem__pokemon___pokemon_196.webp"}, {"prompt": "league_of_legends, sona (league of legends), league of legends", "chinese_prompt": "索娜 (英雄联盟 LOL)", "image_path": "assets/output_8_league_of_legends__sona__league_of_legends___league_of_legends_197.webp"}, {"prompt": "mushoku_tensei, eris grey<PERSON>, mushoku tensei", "chinese_prompt": "艾莉丝·伯雷亚斯·格雷拉特 (无职转生)", "image_path": "assets/output_8_mushoku_tensei__eris_greyrat__mushoku_tensei_198.webp"}, {"prompt": "pokemon, jirachi, pokemon", "chinese_prompt": "基拉祈 (宝可梦)", "image_path": "assets/output_8_pokemon__ji<PERSON>hi__pokemon_199.webp"}, {"prompt": "fate_(series), altera (fate), fate (series)", "chinese_prompt": "阿蒂拉 (Fate)", "image_path": "assets/output_8_fate__series___altera__fate___fate__series__200.webp"}, {"prompt": "danga<PERSON><PERSON><PERSON>_(series), y<PERSON><PERSON> himiko, dangan<PERSON><PERSON> (series)", "chinese_prompt": "梦野秘密子 (弹丸论破)", "image_path": "assets/output_8_danganronpa__series___yumeno_himiko__danganronpa__series__201.webp"}, {"prompt": "blue_archive, justice task force member (blue archive), blue archive", "chinese_prompt": "正义实现委员会杂鱼部员 (蔚蓝档案)", "image_path": "assets/output_8_blue_archive__justice_task_force_member__blue_archive___blue_archive_202.webp"}, {"prompt": "mega_man_(series), mega man (character), mega man (series)", "chinese_prompt": "洛克人 (洛克人)", "image_path": "assets/output_8_mega_man__series___mega_man__character___mega_man__series__203.webp"}, {"prompt": "pokemon, meowth, pokemon", "chinese_prompt": "喵喵 (宝可梦)", "image_path": "assets/output_8_pokemon__meowth__pokemon_204.webp"}, {"prompt": "hololive, m<PERSON><PERSON> s<PERSON> (1st costume), hololive", "chinese_prompt": "紫咲诗音 (1st服) (Hololive)", "image_path": "assets/output_9_hololive__m<PERSON><PERSON>_shion__1st_costume___hololive_0.webp"}, {"prompt": "dangan<PERSON><PERSON>_(series), amami rantaro, dangan<PERSON><PERSON> (series)", "chinese_prompt": "天海兰太郎 (弹丸论破)", "image_path": "assets/output_9_danganronpa__series___amami_rantaro__danganronpa__series__1.webp"}, {"prompt": "neptune_(series), nepgear, neptune (series)", "chinese_prompt": "妮普吉雅 紫妹妹 (战机少女)", "image_path": "assets/output_9_neptune__series___nepgear__neptune__series__2.webp"}, {"prompt": "seiken_densetsu, ries<PERSON>, seiken densetsu", "chinese_prompt": "莉丝 (圣剑传说3)", "image_path": "assets/output_9_seiken_densetsu__riesz__seiken_densetsu_3.webp"}, {"prompt": "made_in_abyss, nanachi (made in abyss), made in abyss", "chinese_prompt": "娜娜奇 (来自深渊)", "image_path": "assets/output_9_made_in_abyss__nanachi__made_in_abyss___made_in_abyss_4.webp"}, {"prompt": "kemono_friends, emperor penguin (kemono friends), kemono friends", "chinese_prompt": "皇帝企鹅 (动物朋友)", "image_path": "assets/output_9_kemono_friends__emperor_penguin__kemono_friends___kemono_friends_5.webp"}, {"prompt": "kantai_collection, johnston (kancolle), kantai collection", "chinese_prompt": "约翰斯顿 (舰队收藏)", "image_path": "assets/output_9_kantai_collection__johnst<PERSON>__kancolle___kantai_collection_6.webp"}, {"prompt": "higu<PERSON>i_no_naku_koro_ni, ma<PERSON><PERSON> keiichi, higurashi no naku koro ni", "chinese_prompt": "前原圭一 (暮蝉悲鸣时)", "image_path": "assets/output_9_higurashi_no_naku_koro_ni__ma<PERSON><PERSON>_k<PERSON><PERSON>__higurashi_no_naku_koro_ni_7.webp"}, {"prompt": "the_moon_studio, kaguya luna, the moon studio", "chinese_prompt": "辉夜月 (The Moon Studio)", "image_path": "assets/output_9_the_moon_studio__kaguya_luna__the_moon_studio_8.webp"}, {"prompt": "idolmaster, matoba risa, idolmaster", "chinese_prompt": "的场梨沙 (灰姑娘) (偶像大师)", "image_path": "assets/output_9_idolmaster__matoba_risa__idolmaster_9.webp"}, {"prompt": "kantai_collection, haruna kai ni (kancolle), kantai collection", "chinese_prompt": "榛名改二 (舰队收藏)", "image_path": "assets/output_9_kantai_collection__haruna_kai_ni__kancolle___kantai_collection_10.webp"}, {"prompt": "kemono_friends, eurasian eagle owl (kemono friends), kemono friends", "chinese_prompt": "雕鸮 (动物朋友)", "image_path": "assets/output_9_kemono_friends__eurasian_eagle_owl__kemono_friends___kemono_friends_11.webp"}, {"prompt": "idolmaster, a<PERSON><PERSON><PERSON> natsuha, idolmaster", "chinese_prompt": "有栖川夏叶 (闪耀色彩) (偶像大师)", "image_path": "assets/output_9_idolmaster__a<PERSON><PERSON><PERSON>_na<PERSON><PERSON>__idolmaster_12.webp"}, {"prompt": "fire_emblem, alear (female) (fire emblem), fire emblem", "chinese_prompt": "琉尔 (女) (Engage) (圣火降魔录)", "image_path": "assets/output_9_fire_emblem__alear__female___fire_emblem___fire_emblem_13.webp"}, {"prompt": "watashi_ga_motenai_no_wa_dou_kangaete<PERSON>_omaera_ga_warui!, katou asuka, watashi ga motenai no wa dou kangaetemo omaera ga warui!", "chinese_prompt": "加藤明日香 (我不受欢迎，怎么想都是你们的错)", "image_path": "assets/output_9_watashi_ga_motenai_no_wa_dou_kangaetemo_omaera_ga_warui___katou_asuka__watashi_ga_motenai_no_wa_dou_kangaetemo_omaera_ga_warui__14.webp"}, {"prompt": "pokemon, sabrina (pokemon), pokemon", "chinese_prompt": "娜姿 (宝可梦)", "image_path": "assets/output_9_pokemon__sabrina__pokemon___pokemon_15.webp"}, {"prompt": "pokemon, braixen, pokemon", "chinese_prompt": "长尾火狐 (宝可梦)", "image_path": "assets/output_9_pokemon__braixen__pokemon_16.webp"}, {"prompt": "god_eater, alisa il<PERSON> amiella, god eater", "chinese_prompt": "亚莉莎·伊莉妮提娜·阿米耶拉 (噬神者)", "image_path": "assets/output_9_god_eater__alisa_il<PERSON><PERSON>_amiella__god_eater_17.webp"}, {"prompt": "genshin_impact, kuki shinobu, genshin impact", "chinese_prompt": "久岐忍 (原神)", "image_path": "assets/output_9_genshin_impact__kuki_shinobu__genshin_impact_18.webp"}, {"prompt": "genshin_impact, xiangling (genshin impact), genshin impact", "chinese_prompt": "香菱 (原神)", "image_path": "assets/output_9_genshin_impact__xiangling__genshin_impact___genshin_impact_19.webp"}, {"prompt": "bishoujo_senshi_sailor_moon, sailor mars, bishoujo senshi sailor moon", "chinese_prompt": "水手火星 (美少女战士)", "image_path": "assets/output_9_bishoujo_senshi_sailor_moon__sailor_mars__bishoujo_senshi_sailor_moon_20.webp"}, {"prompt": "fate_(series), medea (fate), fate (series)", "chinese_prompt": "美狄亚 (Fate)", "image_path": "assets/output_9_fate__series___medea__fate___fate__series__21.webp"}, {"prompt": "touhou, hourai doll, touhou", "chinese_prompt": "蓬莱人偶 (东方)", "image_path": "assets/output_9_touhou__hourai_doll__touhou_22.webp"}, {"prompt": "gegege_no_kitarou, backbeard, gegege no kitarou", "chinese_prompt": "巴克贝亚德 (鬼太郎)", "image_path": "assets/output_9_gegege_no_kitarou__backbeard__gegege_no_kitarou_23.webp"}, {"prompt": "blue_archive, reisa (blue archive), blue archive", "chinese_prompt": "宇泽玲纱 (蔚蓝档案)", "image_path": "assets/output_9_blue_archive__reisa__blue_archive___blue_archive_24.webp"}, {"prompt": "fate_(series), gilles de rais (caster) (fate), fate (series)", "chinese_prompt": "吉尔・德・莱斯 (Caster) (Fate)", "image_path": "assets/output_9_fate__series___gilles_de_rais__caster___fate___fate__series__25.webp"}, {"prompt": "girls_und_panzer, assam (girls und panzer), girls und panzer", "chinese_prompt": "阿萨姆 (少女与战车)", "image_path": "assets/output_9_girls_und_panzer__assam__girls_und_panzer___girls_und_panzer_26.webp"}, {"prompt": "blue_archive, atsuko (blue archive), blue archive", "chinese_prompt": "秤亚津子 (蔚蓝档案)", "image_path": "assets/output_9_blue_archive__atsuko__blue_archive___blue_archive_27.webp"}, {"prompt": "ragnarok_online, high priest (ragnarok online), ragnarok online", "chinese_prompt": "神官 (仙境传说RO)", "image_path": "assets/output_9_ragnarok_online__high_priest__ragnarok_online___ragnarok_online_28.webp"}, {"prompt": "idolmaster, a<PERSON><PERSON> ryo, idolmaster", "chinese_prompt": "秋月凉 (深情之星) (偶像大师)", "image_path": "assets/output_9_idolmaster__a<PERSON><PERSON>_ryo__idolmaster_29.webp"}, {"prompt": "umamusume, super creek (umamusume), umamusume", "chinese_prompt": "超级溪流 (赛马娘)", "image_path": "assets/output_9_umamusume__super_creek__umamusume___umamusume_30.webp"}, {"prompt": "angel_beats!, nakamura yuri, angel beats!", "chinese_prompt": "仲村由理 (Angel Beats!)", "image_path": "assets/output_9_angel_beats___nakamura_yuri__angel_beats__31.webp"}, {"prompt": "fate_(series), enkidu (fate), fate (series)", "chinese_prompt": "恩奇都 (Fate)", "image_path": "assets/output_9_fate__series___enkidu__fate___fate__series__32.webp"}, {"prompt": "ni<PERSON><PERSON><PERSON>, su<PERSON><PERSON> lulu, ni<PERSON><PERSON><PERSON>", "chinese_prompt": "铃原露露 (彩虹社)", "image_path": "assets/output_9_niji<PERSON><PERSON>__su<PERSON><PERSON>_lulu__niji<PERSON>ji_33.webp"}, {"prompt": "pokemon, green (pokemon), pokemon", "chinese_prompt": "青绿 (THE ORIGIN) (宝可梦)", "image_path": "assets/output_9_pokemon__green__pokemon___pokemon_34.webp"}, {"prompt": "pokemon, lucas (pokemon), pokemon", "chinese_prompt": "明辉 (宝可梦)", "image_path": "assets/output_9_pokemon__lucas__pokemon___pokemon_35.webp"}, {"prompt": "kemono_friends, al<PERSON><PERSON> suri (kemono friends), kemono friends", "chinese_prompt": "苏利羊驼 (动物朋友)", "image_path": "assets/output_9_kemono_friends__alpaca_suri__kemono_friends___kemono_friends_36.webp"}, {"prompt": "blue_archive, moe (blue archive), blue archive", "chinese_prompt": "风仓萌 (蔚蓝档案)", "image_path": "assets/output_9_blue_archive__moe__blue_archive___blue_archive_37.webp"}, {"prompt": "persona, kuma (persona 4), persona", "chinese_prompt": "熊 (P4) (女神异闻录)", "image_path": "assets/output_9_persona__kuma__persona_4___persona_38.webp"}, {"prompt": "precure, cure marine, precure", "chinese_prompt": "来海绘里香 cure marine (光之美少女)", "image_path": "assets/output_9_precure__cure_marine__precure_39.webp"}, {"prompt": "pokemon, bede (pokemon), pokemon", "chinese_prompt": "彼特 (宝可梦)", "image_path": "assets/output_9_pokemon__bede__pokemon___pokemon_40.webp"}, {"prompt": "ranma_1/2, <PERSON><PERSON> aka<PERSON>, ranma 1/2", "chinese_prompt": "天道茜 (乱马1/2)", "image_path": "assets/output_9_ranma_1_2__tendou_akane__ranma_1_2_41.webp"}, {"prompt": "boku_no_hero_academia, jirou kyouka, boku no hero academia", "chinese_prompt": "耳郎响香 (我的英雄学院)", "image_path": "assets/output_9_boku_no_hero_academia__jirou_kyouka__boku_no_hero_academia_42.webp"}, {"prompt": "jojo_no_kimyou_na_bouken, gyro zeppeli, jojo no kimyou na bouken", "chinese_prompt": "杰洛·齐贝林 (JOJO的奇妙冒险)", "image_path": "assets/output_9_jojo_no_kimyou_na_bouken__gyro_zeppeli__jojo_no_kimyou_na_bouken_43.webp"}, {"prompt": "blue_archive, miyako (swimsuit) (blue archive), blue archive", "chinese_prompt": "月雪宫子 (泳装) (蔚蓝档案)", "image_path": "assets/output_9_blue_archive__mi<PERSON><PERSON>__swimsuit___blue_archive___blue_archive_44.webp"}, {"prompt": "kantai_collection, maikaze (kancolle), kantai collection", "chinese_prompt": "舞风 (舰队收藏)", "image_path": "assets/output_9_kantai_collection__maikaze__kancolle___kantai_collection_45.webp"}, {"prompt": "blue_archive, neru (bunny) (blue archive), blue archive", "chinese_prompt": "美甘宁瑠 (兔女郎)  (蔚蓝档案)", "image_path": "assets/output_9_blue_archive__neru__bunny___blue_archive___blue_archive_46.webp"}, {"prompt": "cyberpunk_(series), lucy (cyberpunk), cyberpunk (series)", "chinese_prompt": "露西 (Cyberpunk)", "image_path": "assets/output_9_cyberpunk__series___lucy__cyberpunk___cyberpunk__series__47.webp"}, {"prompt": "fate_(series), karna (fate), fate (series)", "chinese_prompt": "迦尔纳 (Fate)", "image_path": "assets/output_9_fate__series___karna__fate___fate__series__48.webp"}, {"prompt": "tsukihime, kohak<PERSON> (tsukihime), tsuki<PERSON>e", "chinese_prompt": "琥珀 (真月谭－月姬)", "image_path": "assets/output_9_tsukihime__kohaku__tsukihime___tsukihime_49.webp"}, {"prompt": "fate_(series), passionlip (fate), fate (series)", "chinese_prompt": "热情迷唇 (Fate)", "image_path": "assets/output_9_fate__series___passionlip__fate___fate__series__50.webp"}, {"prompt": "love_live!, mi<PERSON><PERSON> ai, love live!", "chinese_prompt": "宫下爱 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_9_love_live___mi<PERSON><PERSON>_ai__love_live__51.webp"}, {"prompt": "umamusume, biwa hayahi<PERSON> (umamusume), umamusume", "chinese_prompt": "琵琶晨光 (赛马娘)", "image_path": "assets/output_9_umamusume__biwa_hayahide__umamusume___umamusume_52.webp"}, {"prompt": "idolmaster, kit<PERSON><PERSON> shiho, idolmaster", "chinese_prompt": "北泽志保 (百万现场) (偶像大师)", "image_path": "assets/output_9_idolmaster__kit<PERSON><PERSON>_shiho__idolmaster_53.webp"}, {"prompt": "ni<PERSON><PERSON><PERSON>, sasaki saku, ni<PERSON><PERSON><PERSON>", "chinese_prompt": "笹木咲 (彩虹社)", "image_path": "assets/output_9_niji<PERSON><PERSON>__sasaki_saku__niji<PERSON><PERSON>_54.webp"}, {"prompt": "hunter_x_hunter, kill<PERSON>, hunter x hunter", "chinese_prompt": "奇犽·揍敌客 (猎人)", "image_path": "assets/output_9_hunter_x_hunter__killua_zold<PERSON><PERSON>__hunter_x_hunter_55.webp"}, {"prompt": "final_fantasy, rydia (ff4), final fantasy", "chinese_prompt": "莉迪亚 (ff4), (最终幻想)", "image_path": "assets/output_9_final_fantasy__rydia__ff4___final_fantasy_56.webp"}, {"prompt": "kantai_collection, o<PERSON>o (kancolle), kantai collection", "chinese_prompt": "大潮 (舰队收藏)", "image_path": "assets/output_9_kantai_collection__o<PERSON>o__kancolle___kantai_collection_57.webp"}, {"prompt": "gundam, hoshino fumina, gundam", "chinese_prompt": "星野文奈 (钢弹创斗者) (钢弹)", "image_path": "assets/output_9_gundam__hoshino_fumina__gundam_58.webp"}, {"prompt": "love_live!, konoe kanata, love live!", "chinese_prompt": "近江彼方 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_9_love_live___konoe_kanata__love_live__59.webp"}, {"prompt": "hololive, pekomama, hololive", "chinese_prompt": "兔田妈妈 (Hololive)", "image_path": "assets/output_9_hololive__pekomama__hololive_60.webp"}, {"prompt": "blue_archive, kayoko (dress) (blue archive), blue archive", "chinese_prompt": "鬼方佳世子 (礼服)  (蔚蓝档案)", "image_path": "assets/output_9_blue_archive__kayoko__dress___blue_archive___blue_archive_61.webp"}, {"prompt": "girls'_frontline, m200 (girls' frontline), girls' frontline", "chinese_prompt": "M200 (少女前线)", "image_path": "assets/output_9_girls__frontline__m200__girls__frontline___girls__frontline_62.webp"}, {"prompt": "hololive, amane ka<PERSON>a (1st costume), hololive", "chinese_prompt": "天音彼方 (1st服) (Hololive)", "image_path": "assets/output_9_hololive__amane_kanata__1st_costume___hololive_63.webp"}, {"prompt": "shingeki_no_kyojin, armin arlert, shingeki no kyojin", "chinese_prompt": "阿尔敏·亚鲁雷特 (进击的巨人)", "image_path": "assets/output_9_shingeki_no_kyojin__armin_arlert__shingeki_no_kyojin_64.webp"}, {"prompt": "kamitsubaki_studio, isekai joucho, kamitsubaki studio", "chinese_prompt": "ヰ世界情绪 (神椿)", "image_path": "assets/output_9_ka<PERSON><PERSON><PERSON><PERSON>_studio__is<PERSON><PERSON>_joucho__kamitsu<PERSON>ki_studio_65.webp"}, {"prompt": "pokemon, torchic, pokemon", "chinese_prompt": "火稚鸡 (宝可梦)", "image_path": "assets/output_9_pokemon__torchic__pokemon_66.webp"}, {"prompt": "sonic_(series), rouge the bat, sonic (series)", "chinese_prompt": "露姬 (音速小子)", "image_path": "assets/output_9_sonic__series___rouge_the_bat__sonic__series__67.webp"}, {"prompt": "precure, momozono love, precure", "chinese_prompt": "桃园爱 (光之美少女)", "image_path": "assets/output_9_precure__momozono_love__precure_68.webp"}, {"prompt": "love_live!, as<PERSON> karin, love live!", "chinese_prompt": "朝香果林, (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_9_love_live___asaka_karin__love_live__69.webp"}, {"prompt": "love_live!, tenno<PERSON>ji rina, love live!", "chinese_prompt": "天王寺璃奈 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_9_love_live___tennouji_rina__love_live__70.webp"}, {"prompt": "go!_princess_precure, am<PERSON><PERSON> kirara, go! princess precure", "chinese_prompt": "天之川绮罗 (go) (光之美少女)", "image_path": "assets/output_9_go__princess_precure__am<PERSON><PERSON>_kirara__go__princess_precure_71.webp"}, {"prompt": "godzilla_(series), godzilla, godzilla (series)", "chinese_prompt": "哥吉拉 (哥吉拉)", "image_path": "assets/output_9_godzilla__series___godzilla__godzilla__series__72.webp"}, {"prompt": "hololive, pavolia reine, hololive", "chinese_prompt": "帕沃莉亚・蕾内 (Pavolia <PERSON>ine) (Hololive)", "image_path": "assets/output_9_hololive__pavolia_reine__hololive_73.webp"}, {"prompt": "zenless_zone_zero, belle (zenless zone zero), zenless zone zero", "chinese_prompt": "铃 (绝区零)", "image_path": "assets/output_9_zenless_zone_zero__belle__zenless_zone_zero___zenless_zone_zero_74.webp"}, {"prompt": "blue_archive, ako (dress) (blue archive), blue archive", "chinese_prompt": "天雨亚子 (礼服)  (蔚蓝档案)", "image_path": "assets/output_9_blue_archive__ako__dress___blue_archive___blue_archive_75.webp"}, {"prompt": "ni<PERSON><PERSON><PERSON>, sukoya kana, ni<PERSON><PERSON><PERSON>", "chinese_prompt": "健屋花那 (彩虹社)", "image_path": "assets/output_9_niji<PERSON><PERSON>__sukoya_kana__niji<PERSON>ji_76.webp"}, {"prompt": "genshin_impact, rosaria (genshin impact), genshin impact", "chinese_prompt": "罗莎莉亚 (原神)", "image_path": "assets/output_9_genshin_impact__rosaria__genshin_impact___genshin_impact_77.webp"}, {"prompt": "elden_ring, tarnished (elden ring), elden ring", "chinese_prompt": "褪色者 (艾尔登法环)", "image_path": "assets/output_9_elden_ring__tarnished__elden_ring___elden_ring_78.webp"}, {"prompt": "league_of_legends, gwen (league of legends), league of legends", "chinese_prompt": "关 (英雄联盟 LOL)", "image_path": "assets/output_9_league_of_legends__gwen__league_of_legends___league_of_legends_79.webp"}, {"prompt": "danga<PERSON><PERSON><PERSON>_(series), <PERSON><PERSON><PERSON><PERSON>, danga<PERSON><PERSON><PERSON> (series)", "chinese_prompt": "不二咲千寻 (弹丸论破)", "image_path": "assets/output_9_danganronpa__series___fu<PERSON><PERSON>_chihiro__danganronpa__series__80.webp"}, {"prompt": "vocaloid, rabbit yukine, vocaloid", "chinese_prompt": "雪初音 (兔) (Vocaloid)", "image_path": "assets/output_9_vocaloid__rabbit_yukine__vocaloid_81.webp"}, {"prompt": "project_moon, hong lu (project moon), project moon", "chinese_prompt": "鸿璐 (Project Moon)", "image_path": "assets/output_9_project_moon__hong_lu__project_moon___project_moon_82.webp"}, {"prompt": "genshin_impact, kirara (genshin impact), genshin impact", "chinese_prompt": "绮良良 (原神)", "image_path": "assets/output_9_genshin_impact__kirara__genshin_impact___genshin_impact_83.webp"}, {"prompt": "pokemon, flareon, pokemon", "chinese_prompt": "火伊布 (宝可梦)", "image_path": "assets/output_9_pokemon__flareon__pokemon_84.webp"}, {"prompt": "resident_evil, chris redfield, resident evil", "chinese_prompt": "克里斯·雷德菲尔 (恶灵古堡)", "image_path": "assets/output_9_resident_evil__chris_redfield__resident_evil_85.webp"}, {"prompt": "mahou_tsukai_no_yoru, a<PERSON><PERSON>, mahou tsukai no yoru", "chinese_prompt": "苍崎青子 (魔法使之夜)", "image_path": "assets/output_9_mahou_tsukai_no_yoru__a<PERSON><PERSON>_a<PERSON>__mahou_tsukai_no_yoru_86.webp"}, {"prompt": "world_witches_series, minna-<PERSON><PERSON><PERSON> w<PERSON>, world witches series", "chinese_prompt": "明娜·迪特林德·威尔克 (强袭魔女)", "image_path": "assets/output_9_world_witches_series__minna-<PERSON><PERSON><PERSON>_wil<PERSON>__world_witches_series_87.webp"}, {"prompt": "fate_(series), ar<PERSON>a (fate), fate (series)", "chinese_prompt": "阿周那 (Fate)", "image_path": "assets/output_9_fate__series___arjuna__fate___fate__series__88.webp"}, {"prompt": "tate_no_yuusha_no_nariagari, <PERSON><PERSON><PERSON>, tate no yuusha no nariagari", "chinese_prompt": "拉芙塔莉娅 (盾之勇者成名录)", "image_path": "assets/output_9_tate_no_yuusha_no_nariagari__raphtalia__tate_no_yuusha_no_nariagari_89.webp"}, {"prompt": "arknights, ling (arknights), arknights", "chinese_prompt": "令 (明日方舟)", "image_path": "assets/output_9_arknights__ling__arknights___arknights_90.webp"}, {"prompt": "fire_emblem, azura (fire emblem), fire emblem", "chinese_prompt": "阿库娅 (if) (圣火降魔录)", "image_path": "assets/output_9_fire_emblem__a<PERSON>ra__fire_emblem___fire_emblem_91.webp"}, {"prompt": "uzaki-chan_wa_asobitai!, u<PERSON> hana, uzaki-chan wa asobitai!", "chinese_prompt": "宇崎花 (宇崎学妹想要玩)", "image_path": "assets/output_9_u<PERSON>-chan_wa_asobitai___u<PERSON>_hana__uzaki-chan_wa_asobitai__92.webp"}, {"prompt": "gakuen_idolmaster, fujita kotone, gakuen idolmaster", "chinese_prompt": "藤田琴音 (学园偶像大师)", "image_path": "assets/output_9_gakuen_idolmaster__fujita_kotone__gakuen_idolmaster_93.webp"}, {"prompt": "fire_emblem, mica<PERSON> (fire emblem), fire emblem", "chinese_prompt": "米卡雅 (Engage) (圣火降魔录)", "image_path": "assets/output_9_fire_emblem__micaiah__fire_emblem___fire_emblem_94.webp"}, {"prompt": "umamusume, sweep tosho (umamusume), umamusume", "chinese_prompt": "东商变革 (赛马娘)", "image_path": "assets/output_9_umamusume__sweep_tosho__umamusume___umamusume_95.webp"}, {"prompt": "elden_ring, malenia blade of miquella, elden ring", "chinese_prompt": "玛莲妮亚 (艾尔登法环)", "image_path": "assets/output_9_elden_ring__malenia_blade_of_miquella__elden_ring_96.webp"}, {"prompt": "kemono_friends, jaguar (kemono friends), kemono friends", "chinese_prompt": "美洲豹 (动物朋友)", "image_path": "assets/output_9_kemono_friends__jaguar__kemono_friends___kemono_friends_97.webp"}, {"prompt": "bleach, matsumoto rangiku, bleach", "chinese_prompt": "松本乱菊 (死神)", "image_path": "assets/output_9_bleach__matsu<PERSON>_rangiku__bleach_98.webp"}, {"prompt": "arknights, silence (arknights), arknights", "chinese_prompt": "赫默 (明日方舟)", "image_path": "assets/output_9_arknights__silence__arknights___arknights_99.webp"}, {"prompt": "ano_hi_mita_hana_no_namae_wo_bokutachi_wa_mada_shiranai., honma meiko, ano hi mita hana no namae wo bokutachi wa mada shiranai.", "chinese_prompt": "本间芽衣子 (未闻花名)", "image_path": "assets/output_9_ano_hi_mita_hana_no_namae_wo_bokutachi_wa_mada_shiranai___honma_meiko__ano_hi_mita_hana_no_namae_wo_bokutachi_wa_mada_shiranai__100.webp"}, {"prompt": "honkai_(series), topaz (honkai: star rail), honkai (series)", "chinese_prompt": "托帕 (崩坏: 星穹铁道) (崩坏)", "image_path": "assets/output_9_honkai__series___topaz__honkai__star_rail___honkai__series__101.webp"}, {"prompt": "senki_zesshou_symphogear, t<PERSON><PERSON><PERSON><PERSON> shir<PERSON>, senki zesshou symphogear", "chinese_prompt": "月读调 (战姬绝唱SYMPHOGEAR)", "image_path": "assets/output_9_senki_zesshou_symphogear__tsu<PERSON><PERSON><PERSON>_shir<PERSON>__senki_zesshou_symphogear_102.webp"}, {"prompt": "pokemon, popplio, pokemon", "chinese_prompt": "球球海狮 (宝可梦)", "image_path": "assets/output_9_pokemon__popplio__pokemon_103.webp"}, {"prompt": "guilty_gear, baiken, guilty gear", "chinese_prompt": "梅喧 (圣骑士之战)", "image_path": "assets/output_9_guilty_gear__baiken__guilty_gear_104.webp"}, {"prompt": "azur_lane, zara (azur lane), azur lane", "chinese_prompt": "扎拉 (碧蓝航线)", "image_path": "assets/output_9_azur_lane__zara__azur_lane___azur_lane_105.webp"}, {"prompt": "<PERSON><PERSON><PERSON><PERSON>, hya<PERSON><PERSON><PERSON><PERSON> sa<PERSON>, ni<PERSON><PERSON><PERSON>", "chinese_prompt": "壹百满天原莎乐美 (彩虹社)", "image_path": "assets/output_9_niji<PERSON><PERSON>__hya<PERSON><PERSON><PERSON><PERSON>_salome__nijisanji_106.webp"}, {"prompt": "touken_ranbu, na<PERSON><PERSON><PERSON>, touken ranbu", "chinese_prompt": "鲶尾藤四郎 (刀剑乱舞)", "image_path": "assets/output_9_touken_ranbu__nama<PERSON><PERSON>_toush<PERSON>u__touken_ranbu_107.webp"}, {"prompt": "blue_archive, yoshimi (blue archive), blue archive", "chinese_prompt": "伊原木喜美 (蔚蓝档案)", "image_path": "assets/output_9_blue_archive__yoshi<PERSON>__blue_archive___blue_archive_108.webp"}, {"prompt": "yuru_yuri, furutani <PERSON>, yuru yuri", "chinese_prompt": "古谷向日葵 (悠哉日常大王)", "image_path": "assets/output_9_yuru_yuri__furutani_himawari__yuru_yuri_109.webp"}, {"prompt": "kantai_collection, zara (kancolle), kantai collection", "chinese_prompt": "扎拉 (舰队收藏)", "image_path": "assets/output_9_kantai_collection__zara__kancolle___kantai_collection_110.webp"}, {"prompt": "genshin_impact, collei (genshin impact), genshin impact", "chinese_prompt": "柯莱 (原神)", "image_path": "assets/output_9_genshin_impact__collei__genshin_impact___genshin_impact_111.webp"}, {"prompt": "love_live!, emma verde, love live!", "chinese_prompt": "艾玛·薇蒂 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_9_love_live___emma_verde__love_live__112.webp"}, {"prompt": "sword_art_online, silica, sword art online", "chinese_prompt": "绫野珪子 西莉卡 (刀剑神域)", "image_path": "assets/output_9_sword_art_online__silica__sword_art_online_113.webp"}, {"prompt": "kantai_collection, kazagumo (kancolle), kantai collection", "chinese_prompt": "风云 (舰队收藏)", "image_path": "assets/output_9_kantai_collection__kazagumo__kancolle___kantai_collection_114.webp"}, {"prompt": "da<PERSON><PERSON>_kashi, shi<PERSON><PERSON>, dagashi kashi", "chinese_prompt": "枝垂萤 (粗点心战争)", "image_path": "assets/output_9_dagashi_kashi__shidare_hotaru__dagashi_kashi_115.webp"}, {"prompt": "kagerou_project, kozakura marry, kagerou project", "chinese_prompt": "小樱茉莉 (阳炎计划)", "image_path": "assets/output_9_kager<PERSON>_project__koz<PERSON><PERSON>_marry__kagerou_project_116.webp"}, {"prompt": "yurucamp, inuyama aoi, yurucamp", "chinese_prompt": "犬山葵 (摇曳露营)", "image_path": "assets/output_9_yurucamp__inuyama_aoi__yurucamp_117.webp"}, {"prompt": "marvel, spider-man, marvel", "chinese_prompt": "蜘蛛人 (Marvel)", "image_path": "assets/output_9_marvel__spider-man__marvel_118.webp"}, {"prompt": "pokemon, tepig, pokemon", "chinese_prompt": "暖暖猪 (宝可梦)", "image_path": "assets/output_9_pokemon__tepig__pokemon_119.webp"}, {"prompt": "kantai_collection, mikuma (kancolle), kantai collection", "chinese_prompt": "三隈 (舰队收藏)", "image_path": "assets/output_9_kantai_collection__miku<PERSON>__kancolle___kantai_collection_120.webp"}, {"prompt": "arknights, rosmontis (arknights), arknights", "chinese_prompt": "迷迭香 (明日方舟)", "image_path": "assets/output_9_arknights__rosmontis__arknights___arknights_121.webp"}, {"prompt": "senran_kagura, y<PERSON> (senran kagura), senran kagura", "chinese_prompt": "雪泉 (闪乱神乐)", "image_path": "assets/output_9_senran_kagura__yumi__senran_kagura___senran_kagura_122.webp"}, {"prompt": "fate/grand_order, minamoto no rai<PERSON>u (swimsuit lancer) (fate), fate/grand order", "chinese_prompt": "源頼光 (泳装枪兵) (Fate/Grand Order)", "image_path": "assets/output_9_fate_grand_order__minamoto_no_raikou__swimsuit_lancer___fate___fate_grand_order_123.webp"}, {"prompt": "omori, hero (omori), omori", "chinese_prompt": "HERO (Omori)", "image_path": "assets/output_9_omori__hero__omori___omori_124.webp"}, {"prompt": "kobayashi-san_chi_no_maidragon, luc<PERSON> (maidragon), kobayashi-san chi no maidragon", "chinese_prompt": "露科亚 (小林家的龙女仆)", "image_path": "assets/output_9_kobayashi-san_chi_no_maidragon__lucoa__maidragon___kobayashi-san_chi_no_maidragon_125.webp"}, {"prompt": "fate_(series), reines el-melloi archisorte, fate (series)", "chinese_prompt": "莱妮丝·埃尔梅罗·阿奇佐尔缇 (Fate)", "image_path": "assets/output_9_fate__series___reines_el-melloi_archisorte__fate__series__126.webp"}, {"prompt": "pretty_series, manaka laala, pretty series", "chinese_prompt": "真中菈菈 (星光系列)", "image_path": "assets/output_9_pretty_series__manaka_laala__pretty_series_127.webp"}, {"prompt": "pokemon, nate (pokemon), pokemon", "chinese_prompt": "共平 (宝可梦)", "image_path": "assets/output_9_pokemon__nate__pokemon___pokemon_128.webp"}, {"prompt": "code_geass, kururugi suzaku, code geass", "chinese_prompt": "枢木朱雀 (Code Geass)", "image_path": "assets/output_9_code_geass__kururugi_suzaku__code_geass_129.webp"}, {"prompt": "kamitsubaki_studio, kaf (kamitsubaki studio), kamitsubaki studio", "chinese_prompt": "花谱 (<PERSON><PERSON>) (神椿)", "image_path": "assets/output_9_kamit<PERSON><PERSON><PERSON>_studio__kaf__kamit<PERSON><PERSON><PERSON>_studio___kamitsubaki_studio_130.webp"}, {"prompt": "granblue_fantasy, vikala (granblue fantasy), granblue fantasy", "chinese_prompt": "碧卡拉 (碧蓝幻想)", "image_path": "assets/output_9_granblue_fantasy__vikala__granblue_fantasy___granblue_fantasy_131.webp"}, {"prompt": "marvel, spider-gwen, marvel", "chinese_prompt": "女蜘蛛人 (关·史黛西) (Marvel)", "image_path": "assets/output_9_marvel__spider-gwen__marvel_132.webp"}, {"prompt": "watashi_ga_motenai_no_wa_dou_kangaete<PERSON>_omaera_ga_warui!, nemoto hina, watashi ga motenai no wa dou kangaetemo omaera ga warui!", "chinese_prompt": "根元阳菜 (我不受欢迎，怎么想都是你们的错)", "image_path": "assets/output_9_watashi_ga_motenai_no_wa_dou_kangaetemo_omaera_ga_warui___nemoto_hina__watashi_ga_motenai_no_wa_dou_kangaetemo_omaera_ga_warui__133.webp"}, {"prompt": "girls_und_panzer, ka<PERSON>ima momo, girls und panzer", "chinese_prompt": "河嶋桃 (少女与战车)", "image_path": "assets/output_9_girls_und_panzer__kawashima_momo__girls_und_panzer_134.webp"}, {"prompt": "final_fantasy, white mage, final fantasy", "chinese_prompt": "白魔法师 (最终幻想)", "image_path": "assets/output_9_final_fantasy__white_mage__final_fantasy_135.webp"}, {"prompt": "poptepipic, pipimi, poptepipic", "chinese_prompt": "pipi美 (pop子和pipi美的日常)", "image_path": "assets/output_9_poptepipic__pipimi__poptepipic_136.webp"}, {"prompt": "vocaloid, racing miku, vocaloid", "chinese_prompt": "赛车初音 (Vocaloid)", "image_path": "assets/output_9_vocaloid__racing_miku__vocaloid_137.webp"}, {"prompt": "kantai_collection, i-26 (kanco<PERSON>), kantai collection", "chinese_prompt": "I-26 (舰队收藏)", "image_path": "assets/output_9_kantai_collection__i-26__kancolle___kantai_collection_138.webp"}, {"prompt": "fate_(series), kishi<PERSON><PERSON> hakuno (female), fate (series)", "chinese_prompt": "岸波白野 (女性) (Fate)", "image_path": "assets/output_9_fate__series___kishinami_hakuno__female___fate__series__139.webp"}, {"prompt": "kantai_collection, kongou kai ni (kancolle), kantai collection", "chinese_prompt": "金刚改二 (舰队收藏)", "image_path": "assets/output_9_kantai_collection__kongou_kai_ni__kancolle___kantai_collection_140.webp"}, {"prompt": "pokemon, garchomp, pokemon", "chinese_prompt": "烈咬陆鲨 (宝可梦)", "image_path": "assets/output_9_pokemon__garchomp__pokemon_141.webp"}, {"prompt": "arknights, ceobe (arknights), arknights", "chinese_prompt": "刻俄柏 (明日方舟)", "image_path": "assets/output_9_arknights__ceobe__arknights___arknights_142.webp"}, {"prompt": "omori, basil (headspace) (omori), omori", "chinese_prompt": "BASIL (心境), (Omori)", "image_path": "assets/output_9_omori__basil__headspace___omori___omori_143.webp"}, {"prompt": "azur_lane, cheshire (azur lane), azur lane", "chinese_prompt": "柴郡 (碧蓝航线)", "image_path": "assets/output_9_azur_lane__cheshire__azur_lane___azur_lane_144.webp"}, {"prompt": "touken_ranbu, hone<PERSON><PERSON> to<PERSON>, touken ranbu", "chinese_prompt": "骨喰藤四郎 (刀剑乱舞)", "image_path": "assets/output_9_touken_ranbu__hone<PERSON><PERSON>_toushirou__touken_ranbu_145.webp"}, {"prompt": "kirby_(series), meta knight, kirby (series)", "chinese_prompt": "魅塔骑士 (星之卡比)", "image_path": "assets/output_9_kirby__series___meta_knight__kirby__series__146.webp"}, {"prompt": "mob_psycho_100, ka<PERSON><PERSON> shi<PERSON>o, mob psycho 100", "chinese_prompt": "影山茂夫 (灵能百分百)", "image_path": "assets/output_9_mob_psycho_100__kageyama_shigeo__mob_psycho_100_147.webp"}, {"prompt": "senpai_ga_uzai_kouhai_no_hanashi, takeda harumi (s<PERSON><PERSON><PERSON>), senpai ga uzai kouhai no hanashi", "chinese_prompt": "武田晴海 (前辈有够烦)", "image_path": "assets/output_9_senpai_ga_uzai_kouhai_no_hanashi__takeda_harumi__shiromanta___senpai_ga_uzai_kouhai_no_hanashi_148.webp"}, {"prompt": "niji<PERSON>ji, kuzuha (niji<PERSON>ji), niji<PERSON>ji", "chinese_prompt": "葛叶 (彩虹社)", "image_path": "assets/output_9_niji<PERSON><PERSON>__kuzuha__niji<PERSON><PERSON>___nijisanji_149.webp"}, {"prompt": "genshin_impact, sucrose (genshin impact), genshin impact", "chinese_prompt": "砂糖 (原神)", "image_path": "assets/output_9_genshin_impact__sucrose__genshin_impact___genshin_impact_150.webp"}, {"prompt": "sanrio, cinnamoroll, sanrio", "chinese_prompt": "大耳狗 (三丽鸥)", "image_path": "assets/output_9_sanrio__cinnamoroll__sanrio_151.webp"}, {"prompt": "fate_(series), merlin (fate), fate (series)", "chinese_prompt": "梅林 (Fate)", "image_path": "assets/output_9_fate__series___merlin__fate___fate__series__152.webp"}, {"prompt": "animal_crossing, isabelle (animal crossing), animal crossing", "chinese_prompt": "西施惠 (动物之森)", "image_path": "assets/output_9_animal_crossing__isabelle__animal_crossing___animal_crossing_153.webp"}, {"prompt": "tiger_&_bunny, i<PERSON> karel<PERSON>, tiger & bunny", "chinese_prompt": "伊凡·卡雷林 (TIGER & BUNNY)", "image_path": "assets/output_9_tiger___bunny__i<PERSON>_karelin__tiger___bunny_154.webp"}, {"prompt": "hibike!_euphonium, yo<PERSON><PERSON> mizore, hibike! euphonium", "chinese_prompt": "铠冢霙 (吹响吧！上低音号)", "image_path": "assets/output_9_hibike__euphonium__yoroizuka_mizore__hibike__euphonium_155.webp"}, {"prompt": "jojo_no_kimyou_na_bouken, guido mista, jojo no kimyou na bouken", "chinese_prompt": "基多·米斯塔 (JOJO的奇妙冒险)", "image_path": "assets/output_9_jojo_no_kimyou_na_bouken__guido_mista__jojo_no_kimyou_na_bouken_156.webp"}, {"prompt": "love_live!, mi<PERSON>ne shi<PERSON>ko, love live!", "chinese_prompt": "三船栞子 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_9_love_live___mifune_s<PERSON><PERSON><PERSON>__love_live__157.webp"}, {"prompt": "kantai_collection, hatsushimo (kancolle), kantai collection", "chinese_prompt": "初霜 (舰队收藏)", "image_path": "assets/output_9_kantai_collection__hatsush<PERSON>__kancolle___kantai_collection_158.webp"}, {"prompt": "majo_no_tabitabi, el<PERSON><PERSON> (majo no tabitabi), majo no tabitabi", "chinese_prompt": "伊蕾娜 (魔女之旅)", "image_path": "assets/output_9_majo_no_tabitabi__elaina__majo_no_tabitabi___majo_no_tabitabi_159.webp"}, {"prompt": "mega_man_(series), x (mega man), mega man (series)", "chinese_prompt": "X (洛克人)", "image_path": "assets/output_9_mega_man__series___x__mega_man___mega_man__series__160.webp"}, {"prompt": "zombie_land_saga, minamoto sakura, zombie land saga", "chinese_prompt": "源樱 (佐贺偶像是传奇)", "image_path": "assets/output_9_zombie_land_saga__minamoto_sakura__zombie_land_saga_161.webp"}, {"prompt": "pokemon, grookey, pokemon", "chinese_prompt": "敲音猴 (宝可梦)", "image_path": "assets/output_9_pokemon__grookey__pokemon_162.webp"}, {"prompt": "kantai_collection, chitose (kancolle), kantai collection", "chinese_prompt": "千歳 (舰队收藏)", "image_path": "assets/output_9_kantai_collection__chitose__kancolle___kantai_collection_163.webp"}, {"prompt": "touken_ranbu, midare toush<PERSON>, touken ranbu", "chinese_prompt": "乱藤四郎 (刀剑乱舞)", "image_path": "assets/output_9_touken_ranbu__midare_toushirou__touken_ranbu_164.webp"}, {"prompt": "xenoblade_chronicles_(series), mio (xenoblade), xenoblade chronicles (series)", "chinese_prompt": "弥央 (异度神剑)", "image_path": "assets/output_9_xenoblade_chronicles__series___mio__xenoblade___xenoblade_chronicles__series__165.webp"}, {"prompt": "kantai_collection, hayasui (kancolle), kantai collection", "chinese_prompt": "速吸 (舰队收藏)", "image_path": "assets/output_9_kantai_collection__hayasui__kancolle___kantai_collection_166.webp"}, {"prompt": "idolmaster, <PERSON><PERSON><PERSON> <PERSON>i, idolmaster", "chinese_prompt": "和泉爱依 (闪耀色彩) (偶像大师)", "image_path": "assets/output_9_idolmaster__i<PERSON><PERSON>_mei__idolmaster_167.webp"}, {"prompt": "little_busters!, kami<PERSON><PERSON> komari, little busters!", "chinese_prompt": "神北小毬 (校园克星)", "image_path": "assets/output_9_little_busters___kamikita_komari__little_busters__168.webp"}, {"prompt": "kantai_collection, hatsukaze (kancolle), kantai collection", "chinese_prompt": "初风 (舰队收藏)", "image_path": "assets/output_9_kantai_collection__hatsukaze__kancolle___kantai_collection_169.webp"}, {"prompt": "love_live!, ta<PERSON><PERSON> yuu, love live!", "chinese_prompt": "高咲侑 (Love Live! 虹咲学园学园偶像同好会)", "image_path": "assets/output_9_love_live___ta<PERSON><PERSON>_yuu__love_live__170.webp"}, {"prompt": "amagi_brilliant_park, sento isuzu, amagi brilliant park", "chinese_prompt": "千斗五十铃 (甘城辉煌乐园救世主)", "image_path": "assets/output_9_amagi_brilliant_park__sento_isuzu__amagi_brilliant_park_171.webp"}, {"prompt": "fate_(series), medb (fate), fate (series)", "chinese_prompt": "梅芙 (Fate)", "image_path": "assets/output_9_fate__series___medb__fate___fate__series__172.webp"}, {"prompt": "original, backbeako, original", "chinese_prompt": "<PERSON><PERSON><PERSON><PERSON> (原创)", "image_path": "assets/output_9_original__backbeako__original_173.webp"}, {"prompt": "fate_(series), nursery rhyme (fate), fate (series)", "chinese_prompt": "童谣 (Fate)", "image_path": "assets/output_9_fate__series___nursery_rhyme__fate___fate__series__174.webp"}, {"prompt": "fire_emblem, be<PERSON><PERSON><PERSON>, fire emblem", "chinese_prompt": "贝尔娜提塔·冯·瓦立 (风花雪月) (圣火降魔录)", "image_path": "assets/output_9_fire_emblem__be<PERSON><PERSON><PERSON>_<PERSON>_varley__fire_emblem_175.webp"}, {"prompt": "xenoblade_chronicles_(series), nia (blade) (xenoblade), xenoblade chronicles (series)", "chinese_prompt": "妮雅 (刀) (异度神剑)", "image_path": "assets/output_9_xenoblade_chronicles__series___nia__blade___xenoblade___xenoblade_chronicles__series__176.webp"}, {"prompt": "precure, cure black, precure", "chinese_prompt": "美墨渚 cure black (光之美少女)", "image_path": "assets/output_9_precure__cure_black__precure_177.webp"}, {"prompt": "dragon_quest, slime (dragon quest), dragon quest", "chinese_prompt": "史莱姆 (勇者斗恶龙)", "image_path": "assets/output_9_dragon_quest__slime__dragon_quest___dragon_quest_178.webp"}, {"prompt": "princess_connect!, kyo<PERSON> (princess connect!), princess connect!", "chinese_prompt": "京香 (公主连结)", "image_path": "assets/output_9_princess_connect___kyouka__princess_connect____princess_connect__179.webp"}, {"prompt": "ranma_1/2, shampoo (ranma 1/2), ranma 1/2", "chinese_prompt": "珊璞 (乱马1/2)", "image_path": "assets/output_9_ranma_1_2__shampoo__ranma_1_2___ranma_1_2_180.webp"}, {"prompt": "kantai_collection, fumizuki (kancolle), kantai collection", "chinese_prompt": "文月 (舰队收藏)", "image_path": "assets/output_9_kantai_collection__fumizuki__kancolle___kantai_collection_181.webp"}, {"prompt": "honkai_(series), jing<PERSON><PERSON> (honkai: star rail), honkai (series)", "chinese_prompt": "镜流 (崩坏：星穹铁道) (崩坏)", "image_path": "assets/output_9_honkai__series___jingliu__honkai__star_rail___honkai__series__182.webp"}, {"prompt": "bishoujo_senshi_sailor_moon, to<PERSON><PERSON> hotaru, bishoujo senshi sailor moon", "chinese_prompt": "土萌萤 水手土星 (美少女战士)", "image_path": "assets/output_9_bishoujo_senshi_sailor_moon__tomoe_hotaru__bishoujo_senshi_sailor_moon_183.webp"}, {"prompt": "kannagi, nagi (kannagi), kannagi", "chinese_prompt": "薙 (神薙)", "image_path": "assets/output_9_kannagi__nagi__kannagi___kannagi_184.webp"}, {"prompt": "su<PERSON><PERSON>_haru<PERSON>_no_yuu<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, suzumiya haruhi no yuuutsu", "chinese_prompt": "鹤屋 (凉宫春日的忧郁)", "image_path": "assets/output_9_su<PERSON><PERSON>_haru<PERSON>_no_yuuutsu__t<PERSON><PERSON><PERSON>__suzu<PERSON>_haruhi_no_yuuutsu_185.webp"}, {"prompt": "fire_emblem, claude von <PERSON>, fire emblem", "chinese_prompt": "库罗德·冯·里刚 ((风花雪月) (圣火降魔录)", "image_path": "assets/output_9_fire_emblem__claude_<PERSON>_r<PERSON><PERSON>__fire_emblem_186.webp"}, {"prompt": "arknights, blaze (arknights), arknights", "chinese_prompt": "煌 (明日方舟)", "image_path": "assets/output_9_arknights__blaze__arknights___arknights_187.webp"}, {"prompt": "fate_(series), to<PERSON><PERSON> to<PERSON>, fate (series)", "chinese_prompt": "远坂时臣 (Fate)", "image_path": "assets/output_9_fate__series___to<PERSON><PERSON>_tokiomi__fate__series__188.webp"}, {"prompt": "dc_comics, raven (dc), dc comics", "chinese_prompt": "瑞雯 (DC)", "image_path": "assets/output_9_dc_comics__raven__dc___dc_comics_189.webp"}, {"prompt": "fate_(series), art<PERSON> pendragon (alter swimsuit rider) (second ascension) (fate), fate (series)", "chinese_prompt": "阿尔托莉亚·潘德拉贡 (泳装骑) (第二灵基) (Fate)", "image_path": "assets/output_9_fate__series___artoria_pendragon__alter_swimsuit_rider___second_ascension___fate___fate__series__190.webp"}, {"prompt": "kaguya-sama_wa_kokurasetai_~tensai-tachi_no_renai_zunousen~, hayasaka ai, kaguya-sama wa kokurasetai ~tensai-tachi no renai zunousen~", "chinese_prompt": "早坂爱 (辉夜姬想让人告白)", "image_path": "assets/output_9_kaguya-sama_wa_kokurasetai__tensai-tachi_no_renai_zunousen___hayasaka_ai__kaguya-sama_wa_kokurasetai__tensai-tachi_no_renai_zunousen__191.webp"}, {"prompt": "genshin_impact, arataki itto, genshin impact", "chinese_prompt": "荒泷一斗 (原神)", "image_path": "assets/output_9_genshin_impact__arataki_itto__genshin_impact_192.webp"}, {"prompt": "kantai_collection, chikuma (kancolle), kantai collection", "chinese_prompt": "筑摩 (舰队收藏)", "image_path": "assets/output_9_kantai_collection__chikuma__kancolle___kantai_collection_193.webp"}, {"prompt": "umamusume, mejiro palmer (umamusume), umamusume", "chinese_prompt": "目白善信 (赛马娘)", "image_path": "assets/output_9_umamusume__mejiro_palmer__umamusume___umamusume_194.webp"}, {"prompt": "touh<PERSON>, wa<PERSON><PERSON>ki no toyohime, touhou", "chinese_prompt": "绵月丰姬 (东方)", "image_path": "assets/output_9_touh<PERSON>__wa<PERSON><PERSON><PERSON>_no_toyohime__touhou_195.webp"}, {"prompt": "overlord_(maruyama), albedo (overlord), overlord (maruyama)", "chinese_prompt": "雅儿贝德 (OVERLORD)", "image_path": "assets/output_9_overlord__maruyama___albedo__overlord___overlord__maruyama__196.webp"}, {"prompt": "kantai_collection, tsushima (kancolle), kantai collection", "chinese_prompt": "津岛 (舰队收藏)", "image_path": "assets/output_9_kantai_collection__tsushima__kancolle___kantai_collection_197.webp"}, {"prompt": "mega_man_(series), aile (mega man zx), mega man (series)", "chinese_prompt": "艾儿 (洛克人)", "image_path": "assets/output_9_mega_man__series___aile__mega_man_zx___mega_man__series__198.webp"}, {"prompt": "nier_(series), a2 (nier:automata), nier (series)", "chinese_prompt": "A2 (尼尔:自动人形)", "image_path": "assets/output_9_nier__series___a2__nier_automata___nier__series__199.webp"}, {"prompt": "luo_xia<PERSON><PERSON>_zhan<PERSON>, luo xia<PERSON><PERSON>, luo xiaohei zhanji", "chinese_prompt": "罗小黑 (罗小黑战记)", "image_path": "assets/output_9_luo_xiaohei_zhan<PERSON>__luo_xiaohei__luo_xiaohei_zhanji_200.webp"}, {"prompt": "angel_beats!, yui (angel beats!), angel beats!", "chinese_prompt": "由依 (Angel Beats!)", "image_path": "assets/output_9_angel_beats___yui__angel_beats____angel_beats__201.webp"}, {"prompt": "genshin_impact, diona (genshin impact), genshin impact", "chinese_prompt": "迪奥娜 (原神)", "image_path": "assets/output_9_genshin_impact__diona__genshin_impact___genshin_impact_202.webp"}, {"prompt": "pokemon, steven stone, pokemon", "chinese_prompt": "大吾 (宝可梦)", "image_path": "assets/output_9_pokemon__steven_stone__pokemon_203.webp"}, {"prompt": "senki_zess<PERSON>_symphogear, ka<PERSON><PERSON> tsu<PERSON>, senki zesshou symphogear", "chinese_prompt": "风鸣翼 (战姬绝唱SYMPHOGEAR)", "image_path": "assets/output_9_senki_zesshou_symphogear__ka<PERSON><PERSON>_tsu<PERSON><PERSON>__senki_zesshou_symphogear_204.webp"}]